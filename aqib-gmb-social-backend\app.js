"use strict";

// Import required modules
const express = require("express");
const bodyParser = require("body-parser");
const cors = require("cors");
const swaggerJsDoc = require("swagger-jsdoc");
const swaggerUi = require("swagger-ui-express");
const swaggerSpec = require("./config/swagger");
// const { combiningReqParams } = require('./middleware/combiningReqParams');
const { errorLogger } = require("./middleware/errorLogger");
const logger = require("./utils/logger");

// Initialize express app
const app = express();

// Middleware
app.use(cors());
app.use(bodyParser.json({ limit: "50mb" }));
app.use(bodyParser.urlencoded({ extended: true, limit: "50mb" }));

// Define the combiningReqParams middleware inline
app.use((req, res, next) => {
  // Combine all parameters into a single object
  req.allParams = {
    ...req.body,
    ...req.query,
    ...req.params,
  };
  next();
});

const db = require("./config/db");

//Routes
const routes = require("./routes");
// const facebookRoutes = require("./routes/facebook.routes");

// Database connection check
(async () => {
  try {
    const result = await db.query("SELECT CURRENT_TIMESTAMP");
    logger.info("Database connected successfully", {
      timestamp: result[0][0]?.CURRENT_TIMESTAMP || new Date().toISOString(),
      database: process.env.APP_DB_NAME,
      host: process.env.APP_DB_HOST,
    });
  } catch (error) {
    logger.error("Database connection failed", {
      error: error.message,
      stack: error.stack,
      database: process.env.APP_DB_NAME,
      host: process.env.APP_DB_HOST,
    });
    // Don't crash the app on DB connection failure
    console.error("Database connection error:", error.message);
  }
})();

// Root endpoint
app.get("/", async (req, res) => {
  res.send({
    message: `Backend is running`,
    environment: process.env.APP_ENV_NAME || "development",
    version: process.env.APP_VER_PREFIX || "v1",
  });
});

// Swagger UI setup
app.use(
  "/api-docs",
  swaggerUi.serve,
  swaggerUi.setup(swaggerSpec, {
    explorer: true,
    customCss: ".swagger-ui .topbar { display: none }",
    swaggerOptions: {
      docExpansion: "none",
      persistAuthorization: true,
    },
  })
);

// API routes
app.use(`/${process.env.APP_VER_PREFIX || "v1"}`, routes);
app.use("/api/facebook", facebookRoutes);

// Add error logging middleware (should be at the end)
app.use(errorLogger);

// Log application startup
logger.info("Application started", {
  environment: process.env.APP_ENV_NAME || "development",
  version: process.env.APP_VER_PREFIX || "v1",
  port: process.env.APP_PORT || 3000,
});

// Export the app
module.exports = app;
