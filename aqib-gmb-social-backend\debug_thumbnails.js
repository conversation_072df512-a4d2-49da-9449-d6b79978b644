const pool = require("./config/db");
const logger = require("./utils/logger");

async function debugThumbnails() {
  try {
    console.log("🔍 Debugging thumbnail data...");

    // Check table structure
    console.log("\n📋 Checking table structure:");
    const tableStructure = await pool.query("DESCRIBE business_assets");
    console.log("Table columns:", tableStructure.map(col => ({
      Field: col.Field,
      Type: col.Type,
      Null: col.Null,
      Key: col.Key,
      Default: col.Default
    })));

    // Check recent assets
    console.log("\n📁 Checking recent assets:");
    const recentAssets = await pool.query(`
      SELECT 
        id, 
        file_name, 
        original_file_name, 
        file_type,
        s3_key,
        thumbnail_s3_key,
        thumbnail_s3_url,
        thumbnail_file_name,
        thumbnail_size,
        upload_date
      FROM business_assets 
      WHERE status = 'active' 
      ORDER BY upload_date DESC 
      LIMIT 10
    `);

    console.log(`Found ${recentAssets.length} recent assets:`);
    recentAssets.forEach((asset, index) => {
      console.log(`\n${index + 1}. Asset ID: ${asset.id}`);
      console.log(`   File: ${asset.original_file_name} (${asset.file_type})`);
      console.log(`   S3 Key: ${asset.s3_key}`);
      console.log(`   Thumbnail S3 Key: ${asset.thumbnail_s3_key || 'NULL'}`);
      console.log(`   Thumbnail URL: ${asset.thumbnail_s3_url || 'NULL'}`);
      console.log(`   Thumbnail File: ${asset.thumbnail_file_name || 'NULL'}`);
      console.log(`   Thumbnail Size: ${asset.thumbnail_size || 'NULL'}`);
      console.log(`   Upload Date: ${asset.upload_date}`);
    });

    // Check for assets with thumbnails
    console.log("\n🖼️ Checking assets with thumbnails:");
    const assetsWithThumbnails = await pool.query(`
      SELECT COUNT(*) as count
      FROM business_assets 
      WHERE status = 'active' 
      AND thumbnail_s3_key IS NOT NULL
    `);
    console.log(`Assets with thumbnails: ${assetsWithThumbnails[0].count}`);

    // Check for assets without thumbnails
    const assetsWithoutThumbnails = await pool.query(`
      SELECT COUNT(*) as count
      FROM business_assets 
      WHERE status = 'active' 
      AND thumbnail_s3_key IS NULL
    `);
    console.log(`Assets without thumbnails: ${assetsWithoutThumbnails[0].count}`);

    // Check by file type
    console.log("\n📊 Assets by type:");
    const assetsByType = await pool.query(`
      SELECT 
        file_type,
        COUNT(*) as total,
        SUM(CASE WHEN thumbnail_s3_key IS NOT NULL THEN 1 ELSE 0 END) as with_thumbnails
      FROM business_assets 
      WHERE status = 'active'
      GROUP BY file_type
    `);
    assetsByType.forEach(row => {
      console.log(`   ${row.file_type}: ${row.total} total, ${row.with_thumbnails} with thumbnails`);
    });

    console.log("\n✅ Debug complete!");

  } catch (error) {
    console.error("❌ Error debugging thumbnails:", error);
  } finally {
    process.exit(0);
  }
}

debugThumbnails();
