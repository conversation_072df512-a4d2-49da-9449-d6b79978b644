{"name": "@apidevtools/swagger-parser", "version": "11.0.0", "description": "Swagger 2.0 and OpenAPI 3.0 parser and validator for Node and browsers", "keywords": ["swagger", "openapi", "open-api", "json", "yaml", "parse", "parser", "validate", "validator", "validation", "spec", "specification", "schema", "reference", "dereference"], "author": {"name": "<PERSON>", "url": "https://jamesmessinger.com"}, "homepage": "https://apidevtools.com/swagger-parser/", "repository": {"type": "git", "url": "https://github.com/APIDevTools/swagger-parser.git"}, "license": "MIT", "main": "lib/index.js", "typings": "lib/index.d.ts", "files": ["lib"], "scripts": {"clean": "rimraf .nyc_output coverage", "lint": "eslint lib test", "lint:fix": "eslint --fix lib test", "test": "npm run test:node && npm run test:typescript", "test:node": "mocha", "test:typescript": "tsc --noEmit --strict --skipDefaultLib<PERSON>heck --skipLib<PERSON>heck --lib esnext,dom test/specs/typescript-definition.spec.ts", "coverage": "npm run coverage:node", "coverage:node": "cross-env QUICK_TEST=true nyc mocha"}, "devDependencies": {"@eslint/compat": "^1.2.9", "@eslint/js": "^9.28.0", "@jsdevtools/host-environment": "^2.1.2", "@types/node": "^22.15.29", "chai": "^5", "cross-env": "^7.0.3", "esbuild": "^0.25.5", "esbuild-plugin-polyfill-node": "^0.3.0", "eslint": "^9.28.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-jsdoc": "^50.7.1", "eslint-plugin-prettier": "^5.4.1", "eslint-plugin-unused-imports": "^4.1.4", "globals": "^16.2.0", "js-yaml": "^4.1.0", "mocha": "^11.5.0", "nyc": "^17.1.0", "openapi-types": "^12.1.3", "prettier": "^3.5.3", "rimraf": "^6.0.1", "typescript": "^5.8.3", "typescript-eslint": "^8.33.1"}, "dependencies": {"@apidevtools/json-schema-ref-parser": "13.0.1", "@apidevtools/openapi-schemas": "^2.1.0", "@apidevtools/swagger-methods": "^3.0.2", "ajv": "^8.17.1", "ajv-draft-04": "^1.0.0", "call-me-maybe": "^1.0.2"}, "peerDependencies": {"openapi-types": ">=7"}, "packageManager": "yarn@4.9.1"}