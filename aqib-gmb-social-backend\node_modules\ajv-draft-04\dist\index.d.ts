import type { AnySchemaObject, Options } from "ajv/dist/core";
import AjvCore from "ajv/dist/core";
declare class Ajv extends AjvCore {
    constructor(opts?: Options);
    _addVocabularies(): void;
    _addDefaultMetaSchema(): void;
    defaultMeta(): string | AnySchemaObject | undefined;
}
export default Ajv;
export { Format, FormatDefinition, AsyncFormatDefinition, KeywordDefinition, KeywordErrorDefinition, CodeKeywordDefinition, MacroKeywordDefinition, FuncKeywordDefinition, Vocabulary, Schema, SchemaObject, AnySchemaObject, AsyncSchema, AnySchema, ValidateFunction, AsyncValidateFunction, SchemaValidateFunction, ErrorObject, ErrorNoParams, } from "ajv/dist/types";
export { Plugin, Options, CodeOptions, InstanceOptions, Logger, ErrorsTextOptions, } from "ajv/dist/core";
export { SchemaCxt, SchemaObjCxt } from "ajv/dist/core";
export { KeywordCxt } from "ajv/dist/core";
export { DefinedError } from "ajv/dist/core";
export { JSONType } from "ajv/dist/core";
export { JSONSchemaType } from "ajv/dist/core";
export { _, str, stringify, nil, Name, Code, CodeGen, CodeGenOptions } from "ajv/dist/core";
