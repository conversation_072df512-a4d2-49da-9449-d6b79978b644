{"version": 3, "file": "limitNumber.js", "sourceRoot": "", "sources": ["../../../src/vocabulary/validation/limitNumber.ts"], "names": [], "mappings": ";;AAOA,wCAA0C;AAC1C,sDAAkD;AAElD,MAAM,GAAG,GAAG,mBAAS,CAAA;AAmBrB,MAAM,IAAI,GAA8B;IACtC,OAAO,EAAE;QACP,SAAS,EAAE,kBAAkB;QAC7B,GAAG,EAAE;YACH,EAAC,KAAK,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,CAAC,EAAE,EAAC;YACxC,EAAC,KAAK,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,IAAI,EAAE,GAAG,CAAC,GAAG,EAAC;SACxC;KACF;IACD,OAAO,EAAE;QACP,SAAS,EAAE,kBAAkB;QAC7B,GAAG,EAAE;YACH,EAAC,KAAK,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,CAAC,EAAE,EAAC;YACxC,EAAC,KAAK,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,IAAI,EAAE,GAAG,CAAC,GAAG,EAAC;SACxC;KACF;CACF,CAAA;AAQD,MAAM,KAAK,GAA2B;IACpC,OAAO,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,UAAG,CAAA,WAAW,KAAK,CAAC,GAAG,CAAC,CAAC,KAAK,IAAI,GAAG,CAAC,UAAU,EAAE;IACpE,MAAM,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,QAAC,CAAA,gBAAgB,KAAK,CAAC,GAAG,CAAC,CAAC,KAAK,YAAY,GAAG,CAAC,UAAU,GAAG;CAChF,CAAA;AAED,MAAM,GAAG,GAA0B;IACjC,OAAO,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;IAC1B,IAAI,EAAE,QAAQ;IACd,UAAU,EAAE,QAAQ;IACpB,KAAK,EAAE,IAAI;IACX,KAAK;IACL,IAAI,CAAC,GAAe;QAClB,MAAM,EAAC,IAAI,EAAE,UAAU,EAAC,GAAG,GAAG,CAAA;QAC9B,GAAG,CAAC,SAAS,CAAC,QAAC,CAAA,GAAG,IAAI,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,IAAI,UAAU,aAAa,IAAI,GAAG,CAAC,CAAA;IAC9E,CAAC;CACF,CAAA;AAED,SAAS,KAAK,CAAC,GAAoB;;IACjC,MAAM,OAAO,GAAG,GAAG,CAAC,OAAmB,CAAA;IACvC,MAAM,MAAM,GAAG,CAAA,MAAA,GAAG,CAAC,YAAY,0CAAG,IAAI,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;IAClE,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;AAClC,CAAC;AAED,kBAAe,GAAG,CAAA"}