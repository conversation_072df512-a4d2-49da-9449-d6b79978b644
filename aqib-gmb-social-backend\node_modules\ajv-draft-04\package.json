{"name": "ajv-draft-04", "version": "1.0.0", "description": "Ajv class for JSON Schema draft-04", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["src/", "dist/"], "scripts": {"build": "rm -rf dist && tsc && cp -r src/refs dist", "prettier:write": "prettier --write \"./**/*.{json,yaml,js,ts}\"", "prettier:check": "prettier --list-different \"./**/*.{json,yaml,js,ts}\"", "eslint": "eslint \"./{src,tests,scripts}/**/*.{ts,js}\"", "json-tests": "rm -rf tests/_json/*.js && node scripts/jsontests", "test-spec": "jest", "test-cov": "jest --coverage", "test-debug": "node --inspect-brk node_modules/.bin/jest --runInBand", "test": "npm run json-tests && npm run prettier:check && npm run build && npm run eslint && npm run test-cov", "test-ci": "AJV_FULL_TEST=true npm test"}, "repository": {"type": "git", "url": "git+https://github.com/ajv-validator/ajv-draft-04.git"}, "keywords": ["Ajv", "JSON-Schema", "validation", "draft-04"], "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/ajv-validator/ajv-draft-04/issues"}, "homepage": "https://github.com/ajv-validator/ajv-draft-04#readme", "dependencies": {}, "peerDependencies": {"ajv": "^8.5.0"}, "peerDependenciesMeta": {"ajv": {"optional": true}}, "devDependencies": {"@ajv-validator/config": "^0.3.0", "@types/jest": "^26.0.5", "@types/node": "^14.10.1", "@typescript-eslint/eslint-plugin": "^3.7.0", "@typescript-eslint/parser": "^3.7.0", "ajv-formats": "^2.0.0", "eslint": "^7.5.0", "eslint-config-prettier": "^6.11.0", "husky": "^4.2.5", "jest": "^26.1.0", "json-schema-test": "^2.0.0", "lint-staged": "^10.2.11", "prettier": "^2.0.5", "ts-jest": "^26.1.3", "typescript": "^4.0.0"}, "prettier": "@ajv-validator/config/prettierrc.json", "husky": {"hooks": {"pre-commit": "lint-staged && npm test"}}, "lint-staged": {"*.{json,yaml,js,ts}": "prettier --write"}}