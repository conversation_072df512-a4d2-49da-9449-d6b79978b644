import { OperatorFunction, ObservableInput } from '../types';
export declare function combineAll<T>(): OperatorFunction<ObservableInput<T>, T[]>;
export declare function combineAll<T>(): OperatorFunction<any, T[]>;
export declare function combineAll<T, R>(project: (...values: T[]) => R): OperatorFunction<ObservableInput<T>, R>;
export declare function combineAll<R>(project: (...values: Array<any>) => R): OperatorFunction<any, R>;
