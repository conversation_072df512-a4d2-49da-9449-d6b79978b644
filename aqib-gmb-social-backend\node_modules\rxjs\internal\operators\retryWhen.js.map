{"version": 3, "file": "retryWhen.js", "sources": ["../../src/internal/operators/retryWhen.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAGA,sCAAqC;AAIrC,oDAAiG;AAgBjG,SAAgB,SAAS,CAAI,QAAsD;IACjF,OAAO,UAAC,MAAqB,IAAK,OAAA,MAAM,CAAC,IAAI,CAAC,IAAI,iBAAiB,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC,EAApD,CAAoD,CAAC;AACzF,CAAC;AAFD,8BAEC;AAED;IACE,2BAAsB,QAAsD,EACtD,MAAqB;QADrB,aAAQ,GAAR,QAAQ,CAA8C;QACtD,WAAM,GAAN,MAAM,CAAe;IAC3C,CAAC;IAED,gCAAI,GAAJ,UAAK,UAAyB,EAAE,MAAW;QACzC,OAAO,MAAM,CAAC,SAAS,CAAC,IAAI,mBAAmB,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;IAC3F,CAAC;IACH,wBAAC;AAAD,CAAC,AARD,IAQC;AAOD;IAAwC,uCAA2B;IAMjE,6BAAY,WAA0B,EAClB,QAAsD,EACtD,MAAqB;QAFzC,YAGE,kBAAM,WAAW,CAAC,SACnB;QAHmB,cAAQ,GAAR,QAAQ,CAA8C;QACtD,YAAM,GAAN,MAAM,CAAe;;IAEzC,CAAC;IAED,mCAAK,GAAL,UAAM,GAAQ;QACZ,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YAEnB,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;YACzB,IAAI,OAAO,GAAQ,IAAI,CAAC,OAAO,CAAC;YAChC,IAAI,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,CAAC;YAEnD,IAAI,CAAC,OAAO,EAAE;gBACZ,MAAM,GAAG,IAAI,iBAAO,EAAE,CAAC;gBACvB,IAAI;oBACM,IAAA,wBAAQ,CAAU;oBAC1B,OAAO,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;iBAC5B;gBAAC,OAAO,CAAC,EAAE;oBACV,OAAO,iBAAM,KAAK,YAAC,CAAC,CAAC,CAAC;iBACvB;gBACD,mBAAmB,GAAG,+BAAc,CAAC,OAAO,EAAE,IAAI,sCAAqB,CAAC,IAAI,CAAC,CAAC,CAAC;aAChF;iBAAM;gBACL,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;gBACxB,IAAI,CAAC,mBAAmB,GAAG,SAAS,CAAC;aACtC;YAED,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAE9B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;YACrB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;YACvB,IAAI,CAAC,mBAAmB,GAAG,mBAAmB,CAAC;YAE/C,MAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;SACnB;IACH,CAAC;IAGD,0CAAY,GAAZ;QACQ,IAAA,SAAsC,EAApC,kBAAM,EAAE,4CAAmB,CAAU;QAC7C,IAAI,MAAM,EAAE;YACV,MAAM,CAAC,WAAW,EAAE,CAAC;YACrB,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;SACzB;QACD,IAAI,mBAAmB,EAAE;YACvB,mBAAmB,CAAC,WAAW,EAAE,CAAC;YAClC,IAAI,CAAC,mBAAmB,GAAG,SAAS,CAAC;SACtC;QACD,IAAI,CAAC,OAAO,GAAG,SAAS,CAAC;IAC3B,CAAC;IAED,wCAAU,GAAV;QACU,IAAA,gCAAY,CAAU;QAE9B,IAAI,CAAC,YAAY,GAAG,IAAK,CAAC;QAC1B,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAC9B,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QAEjC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;IAC9B,CAAC;IACH,0BAAC;AAAD,CAAC,AAlED,CAAwC,sCAAqB,GAkE5D"}