{"version": 3, "file": "single.js", "sources": ["../../src/internal/operators/single.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAEA,4CAA2C;AAC3C,iDAAgD;AAkDhD,SAAgB,MAAM,CAAI,SAAuE;IAC/F,OAAO,UAAC,MAAqB,IAAK,OAAA,MAAM,CAAC,IAAI,CAAC,IAAI,cAAc,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC,EAAlD,CAAkD,CAAC;AACvF,CAAC;AAFD,wBAEC;AAED;IACE,wBAAoB,SAAuE,EACvE,MAAsB;QADtB,cAAS,GAAT,SAAS,CAA8D;QACvE,WAAM,GAAN,MAAM,CAAgB;IAC1C,CAAC;IAED,6BAAI,GAAJ,UAAK,UAAyB,EAAE,MAAW;QACzC,OAAO,MAAM,CAAC,SAAS,CAAC,IAAI,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;IACzF,CAAC;IACH,qBAAC;AAAD,CAAC,AARD,IAQC;AAOD;IAAkC,oCAAa;IAK7C,0BAAY,WAAwB,EAChB,SAAuE,EACvE,MAAsB;QAF1C,YAGE,kBAAM,WAAW,CAAC,SACnB;QAHmB,eAAS,GAAT,SAAS,CAA8D;QACvE,YAAM,GAAN,MAAM,CAAgB;QANlC,eAAS,GAAY,KAAK,CAAC;QAE3B,WAAK,GAAW,CAAC,CAAC;;IAM1B,CAAC;IAEO,2CAAgB,GAAxB,UAAyB,KAAQ;QAC/B,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,yCAAyC,CAAC,CAAC;SACnE;aAAM;YACL,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;YACtB,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;SAC1B;IACH,CAAC;IAES,gCAAK,GAAf,UAAgB,KAAQ;QACtB,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;QAE3B,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;SAC5B;aAAM;YACL,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;SAC9B;IACH,CAAC;IAEO,kCAAO,GAAf,UAAgB,KAAQ,EAAE,KAAa;QACrC,IAAI;YACF,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE;gBAC7C,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;aAC9B;SACF;QAAC,OAAO,GAAG,EAAE;YACZ,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;SAC7B;IACH,CAAC;IAES,oCAAS,GAAnB;QACE,IAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;QAErC,IAAI,IAAI,CAAC,KAAK,GAAG,CAAC,EAAE;YAClB,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;YAChE,WAAW,CAAC,QAAQ,EAAE,CAAC;SACxB;aAAM;YACL,WAAW,CAAC,KAAK,CAAC,IAAI,uBAAU,CAAC,CAAC;SACnC;IACH,CAAC;IACH,uBAAC;AAAD,CAAC,AAlDD,CAAkC,uBAAU,GAkD3C"}