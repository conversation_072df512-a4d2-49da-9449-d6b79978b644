{"version": 3, "file": "take.js", "sources": ["../../src/internal/operators/take.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AACA,4CAA2C;AAC3C,2EAA0E;AAC1E,6CAA4C;AAkD5C,SAAgB,IAAI,CAAI,KAAa;IACnC,OAAO,UAAC,MAAqB;QAC3B,IAAI,KAAK,KAAK,CAAC,EAAE;YACf,OAAO,aAAK,EAAE,CAAC;SAChB;aAAM;YACL,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC;SAC7C;IACH,CAAC,CAAC;AACJ,CAAC;AARD,oBAQC;AAED;IACE,sBAAoB,KAAa;QAAb,UAAK,GAAL,KAAK,CAAQ;QAC/B,IAAI,IAAI,CAAC,KAAK,GAAG,CAAC,EAAE;YAClB,MAAM,IAAI,iDAAuB,CAAC;SACnC;IACH,CAAC;IAED,2BAAI,GAAJ,UAAK,UAAyB,EAAE,MAAW;QACzC,OAAO,MAAM,CAAC,SAAS,CAAC,IAAI,cAAc,CAAC,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;IACtE,CAAC;IACH,mBAAC;AAAD,CAAC,AAVD,IAUC;AAOD;IAAgC,kCAAa;IAG3C,wBAAY,WAA0B,EAAU,KAAa;QAA7D,YACE,kBAAM,WAAW,CAAC,SACnB;QAF+C,WAAK,GAAL,KAAK,CAAQ;QAFrD,WAAK,GAAW,CAAC,CAAC;;IAI1B,CAAC;IAES,8BAAK,GAAf,UAAgB,KAAQ;QACtB,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACzB,IAAM,KAAK,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC;QAC3B,IAAI,KAAK,IAAI,KAAK,EAAE;YAClB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC7B,IAAI,KAAK,KAAK,KAAK,EAAE;gBACnB,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC;gBAC5B,IAAI,CAAC,WAAW,EAAE,CAAC;aACpB;SACF;IACH,CAAC;IACH,qBAAC;AAAD,CAAC,AAlBD,CAAgC,uBAAU,GAkBzC"}