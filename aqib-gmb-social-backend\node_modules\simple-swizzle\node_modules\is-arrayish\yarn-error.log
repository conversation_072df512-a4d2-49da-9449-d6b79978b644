Arguments: 
  /Users/<USER>/n/bin/node /Users/<USER>/.yarn/bin/yarn.js test

PATH: 
  /Users/<USER>/.yarn/bin:/Users/<USER>/.config/yarn/global/node_modules/.bin:/Users/<USER>/perl5/bin:/Users/<USER>/google-cloud-sdk/bin:/usr/local/sbin:/usr/local/bin:/usr/local/bin:/usr/bin:/bin:/usr/sbin:/sbin:/opt/X11/bin:/Users/<USER>/bin:/Users/<USER>/.local/bin:/src/.go/bin:/src/llvm/llvm/build/bin:/Users/<USER>/Library/Android/sdk/platform-tools:/Users/<USER>/n/bin:/usr/local/texlive/2017/bin/x86_64-darwin/

Yarn version: 
  1.5.1

Node version: 
  9.6.1

Platform: 
  darwin x64

npm manifest: 
  {
    "name": "is-arrayish",
    "description": "Determines if an object can be used as an array",
    "version": "0.3.1",
    "author": "Qix (http://github.com/qix-)",
    "keywords": [
      "is",
      "array",
      "duck",
      "type",
      "arrayish",
      "similar",
      "proto",
      "prototype",
      "type"
    ],
    "license": "MIT",
    "scripts": {
      "test": "mocha --require coffeescript/register",
      "lint": "zeit-eslint --ext .jsx,.js .",
      "lint-staged": "git diff --diff-filter=ACMRT --cached --name-only '*.js' '*.jsx' | xargs zeit-eslint"
    },
    "repository": {
      "type": "git",
      "url": "https://github.com/qix-/node-is-arrayish.git"
    },
    "devDependencies": {
      "@zeit/eslint-config-node": "^0.3.0",
      "@zeit/git-hooks": "^0.1.4",
      "coffeescript": "^2.3.1",
      "coveralls": "^3.0.1",
      "eslint": "^4.19.1",
      "istanbul": "^0.4.5",
      "mocha": "^5.2.0",
      "should": "^13.2.1"
    },
    "eslintConfig": {
      "extends": [
        "@zeit/eslint-config-node"
      ]
    },
    "git": {
      "pre-commit": "lint-staged"
    }
  }

yarn manifest: 
  No manifest

Lockfile: 
  # THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
  # yarn lockfile v1
  
  
  "@zeit/eslint-config-base@0.3.0":
    version "0.3.0"
    resolved "https://registry.yarnpkg.com/@zeit/eslint-config-base/-/eslint-config-base-0.3.0.tgz#32a58c3e52eca4025604758cb4591f3d28e22fb4"
    dependencies:
      arg "^1.0.0"
      chalk "^2.3.0"
  
  "@zeit/eslint-config-node@^0.3.0":
    version "0.3.0"
    resolved "https://registry.yarnpkg.com/@zeit/eslint-config-node/-/eslint-config-node-0.3.0.tgz#6e328328f366f66c2a0549a69131bbcd9735f098"
    dependencies:
      "@zeit/eslint-config-base" "0.3.0"
  
  "@zeit/git-hooks@^0.1.4":
    version "0.1.4"
    resolved "https://registry.yarnpkg.com/@zeit/git-hooks/-/git-hooks-0.1.4.tgz#70583db5dd69726a62c7963520e67f2c3a33cc5f"
  
  abbrev@1:
    version "1.1.1"
    resolved "https://registry.yarnpkg.com/abbrev/-/abbrev-1.1.1.tgz#f8f2c887ad10bf67f634f005b6987fed3179aac8"
  
  abbrev@1.0.x:
    version "1.0.9"
    resolved "https://registry.yarnpkg.com/abbrev/-/abbrev-1.0.9.tgz#91b4792588a7738c25f35dd6f63752a2f8776135"
  
  acorn-jsx@^3.0.0:
    version "3.0.1"
    resolved "https://registry.yarnpkg.com/acorn-jsx/-/acorn-jsx-3.0.1.tgz#afdf9488fb1ecefc8348f6fb22f464e32a58b36b"
    dependencies:
      acorn "^3.0.4"
  
  acorn@^3.0.4:
    version "3.3.0"
    resolved "https://registry.yarnpkg.com/acorn/-/acorn-3.3.0.tgz#45e37fb39e8da3f25baee3ff5369e2bb5f22017a"
  
  acorn@^5.5.0:
    version "5.7.1"
    resolved "https://registry.yarnpkg.com/acorn/-/acorn-5.7.1.tgz#f095829297706a7c9776958c0afc8930a9b9d9d8"
  
  ajv-keywords@^2.1.0:
    version "2.1.1"
    resolved "https://registry.yarnpkg.com/ajv-keywords/-/ajv-keywords-2.1.1.tgz#617997fc5f60576894c435f940d819e135b80762"
  
  ajv@^5.1.0, ajv@^5.2.3, ajv@^5.3.0:
    version "5.5.2"
    resolved "https://registry.yarnpkg.com/ajv/-/ajv-5.5.2.tgz#73b5eeca3fab653e3d3f9422b341ad42205dc965"
    dependencies:
      co "^4.6.0"
      fast-deep-equal "^1.0.0"
      fast-json-stable-stringify "^2.0.0"
      json-schema-traverse "^0.3.0"
  
  align-text@^0.1.1, align-text@^0.1.3:
    version "0.1.4"
    resolved "https://registry.yarnpkg.com/align-text/-/align-text-0.1.4.tgz#0cd90a561093f35d0a99256c22b7069433fad117"
    dependencies:
      kind-of "^3.0.2"
      longest "^1.0.1"
      repeat-string "^1.5.2"
  
  amdefine@>=0.0.4:
    version "1.0.1"
    resolved "https://registry.yarnpkg.com/amdefine/-/amdefine-1.0.1.tgz#4a5282ac164729e93619bcfd3ad151f817ce91f5"
  
  ansi-escapes@^3.0.0:
    version "3.1.0"
    resolved "https://registry.yarnpkg.com/ansi-escapes/-/ansi-escapes-3.1.0.tgz#f73207bb81207d75fd6c83f125af26eea378ca30"
  
  ansi-regex@^2.0.0:
    version "2.1.1"
    resolved "https://registry.yarnpkg.com/ansi-regex/-/ansi-regex-2.1.1.tgz#c3b33ab5ee360d86e0e628f0468ae7ef27d654df"
  
  ansi-regex@^3.0.0:
    version "3.0.0"
    resolved "https://registry.yarnpkg.com/ansi-regex/-/ansi-regex-3.0.0.tgz#ed0317c322064f79466c02966bddb605ab37d998"
  
  ansi-styles@^2.2.1:
    version "2.2.1"
    resolved "https://registry.yarnpkg.com/ansi-styles/-/ansi-styles-2.2.1.tgz#b432dd3358b634cf75e1e4664368240533c1ddbe"
  
  ansi-styles@^3.2.1:
    version "3.2.1"
    resolved "https://registry.yarnpkg.com/ansi-styles/-/ansi-styles-3.2.1.tgz#41fbb20243e50b12be0f04b8dedbf07520ce841d"
    dependencies:
      color-convert "^1.9.0"
  
  arg@^1.0.0:
    version "1.0.1"
    resolved "https://registry.yarnpkg.com/arg/-/arg-1.0.1.tgz#892a26d841bd5a64880bbc8f73dd64a705910ca3"
  
  argparse@^1.0.7:
    version "1.0.10"
    resolved "https://registry.yarnpkg.com/argparse/-/argparse-1.0.10.tgz#bcd6791ea5ae09725e17e5ad988134cd40b3d911"
    dependencies:
      sprintf-js "~1.0.2"
  
  array-union@^1.0.1:
    version "1.0.2"
    resolved "https://registry.yarnpkg.com/array-union/-/array-union-1.0.2.tgz#9a34410e4f4e3da23dea375be5be70f24778ec39"
    dependencies:
      array-uniq "^1.0.1"
  
  array-uniq@^1.0.1:
    version "1.0.3"
    resolved "https://registry.yarnpkg.com/array-uniq/-/array-uniq-1.0.3.tgz#af6ac877a25cc7f74e058894753858dfdb24fdb6"
  
  arrify@^1.0.0:
    version "1.0.1"
    resolved "https://registry.yarnpkg.com/arrify/-/arrify-1.0.1.tgz#898508da2226f380df904728456849c1501a4b0d"
  
  asn1@~0.2.3:
    version "0.2.3"
    resolved "https://registry.yarnpkg.com/asn1/-/asn1-0.2.3.tgz#dac8787713c9966849fc8180777ebe9c1ddf3b86"
  
  assert-plus@1.0.0, assert-plus@^1.0.0:
    version "1.0.0"
    resolved "https://registry.yarnpkg.com/assert-plus/-/assert-plus-1.0.0.tgz#f12e0f3c5d77b0b1cdd9146942e4e96c1e4dd525"
  
  async@1.x, async@^1.4.0:
    version "1.5.2"
    resolved "https://registry.yarnpkg.com/async/-/async-1.5.2.tgz#ec6a61ae56480c0c3cb241c95618e20892f9672a"
  
  asynckit@^0.4.0:
    version "0.4.0"
    resolved "https://registry.yarnpkg.com/asynckit/-/asynckit-0.4.0.tgz#c79ed97f7f34cb8f2ba1bc9790bcc366474b4b79"
  
  aws-sign2@~0.7.0:
    version "0.7.0"
    resolved "https://registry.yarnpkg.com/aws-sign2/-/aws-sign2-0.7.0.tgz#b46e890934a9591f2d2f6f86d7e6a9f1b3fe76a8"
  
  aws4@^1.6.0:
    version "1.7.0"
    resolved "https://registry.yarnpkg.com/aws4/-/aws4-1.7.0.tgz#d4d0e9b9dbfca77bf08eeb0a8a471550fe39e289"
  
  babel-code-frame@^6.22.0:
    version "6.26.0"
    resolved "https://registry.yarnpkg.com/babel-code-frame/-/babel-code-frame-6.26.0.tgz#63fd43f7dc1e3bb7ce35947db8fe369a3f58c74b"
    dependencies:
      chalk "^1.1.3"
      esutils "^2.0.2"
      js-tokens "^3.0.2"
  
  balanced-match@^1.0.0:
    version "1.0.0"
    resolved "https://registry.yarnpkg.com/balanced-match/-/balanced-match-1.0.0.tgz#89b4d199ab2bee49de164ea02b89ce462d71b767"
  
  bcrypt-pbkdf@^1.0.0:
    version "1.0.1"
    resolved "https://registry.yarnpkg.com/bcrypt-pbkdf/-/bcrypt-pbkdf-1.0.1.tgz#63bc5dcb61331b92bc05fd528953c33462a06f8d"
    dependencies:
      tweetnacl "^0.14.3"
  
  brace-expansion@^1.1.7:
    version "1.1.11"
    resolved "https://registry.yarnpkg.com/brace-expansion/-/brace-expansion-1.1.11.tgz#3c7fcbf529d87226f3d2f52b966ff5271eb441dd"
    dependencies:
      balanced-match "^1.0.0"
      concat-map "0.0.1"
  
  browser-stdout@1.3.1:
    version "1.3.1"
    resolved "https://registry.yarnpkg.com/browser-stdout/-/browser-stdout-1.3.1.tgz#baa559ee14ced73452229bad7326467c61fabd60"
  
  buffer-from@^1.0.0:
    version "1.1.0"
    resolved "https://registry.yarnpkg.com/buffer-from/-/buffer-from-1.1.0.tgz#87fcaa3a298358e0ade6e442cfce840740d1ad04"
  
  caller-path@^0.1.0:
    version "0.1.0"
    resolved "https://registry.yarnpkg.com/caller-path/-/caller-path-0.1.0.tgz#94085ef63581ecd3daa92444a8fe94e82577751f"
    dependencies:
      callsites "^0.2.0"
  
  callsites@^0.2.0:
    version "0.2.0"
    resolved "https://registry.yarnpkg.com/callsites/-/callsites-0.2.0.tgz#afab96262910a7f33c19a5775825c69f34e350ca"
  
  camelcase@^1.0.2:
    version "1.2.1"
    resolved "https://registry.yarnpkg.com/camelcase/-/camelcase-1.2.1.tgz#9bb5304d2e0b56698b2c758b08a3eaa9daa58a39"
  
  caseless@~0.12.0:
    version "0.12.0"
    resolved "https://registry.yarnpkg.com/caseless/-/caseless-0.12.0.tgz#1b681c21ff84033c826543090689420d187151dc"
  
  center-align@^0.1.1:
    version "0.1.3"
    resolved "https://registry.yarnpkg.com/center-align/-/center-align-0.1.3.tgz#aa0d32629b6ee972200411cbd4461c907bc2b7ad"
    dependencies:
      align-text "^0.1.3"
      lazy-cache "^1.0.3"
  
  chalk@^1.1.3:
    version "1.1.3"
    resolved "https://registry.yarnpkg.com/chalk/-/chalk-1.1.3.tgz#a8115c55e4a702fe4d150abd3872822a7e09fc98"
    dependencies:
      ansi-styles "^2.2.1"
      escape-string-regexp "^1.0.2"
      has-ansi "^2.0.0"
      strip-ansi "^3.0.0"
      supports-color "^2.0.0"
  
  chalk@^2.0.0, chalk@^2.1.0, chalk@^2.3.0:
    version "2.4.1"
    resolved "https://registry.yarnpkg.com/chalk/-/chalk-2.4.1.tgz#18c49ab16a037b6eb0152cc83e3471338215b66e"
    dependencies:
      ansi-styles "^3.2.1"
      escape-string-regexp "^1.0.5"
      supports-color "^5.3.0"
  
  chardet@^0.4.0:
    version "0.4.2"
    resolved "https://registry.yarnpkg.com/chardet/-/chardet-0.4.2.tgz#b5473b33dc97c424e5d98dc87d55d4d8a29c8bf2"
  
  circular-json@^0.3.1:
    version "0.3.3"
    resolved "https://registry.yarnpkg.com/circular-json/-/circular-json-0.3.3.tgz#815c99ea84f6809529d2f45791bdf82711352d66"
  
  cli-cursor@^2.1.0:
    version "2.1.0"
    resolved "https://registry.yarnpkg.com/cli-cursor/-/cli-cursor-2.1.0.tgz#b35dac376479facc3e94747d41d0d0f5238ffcb5"
    dependencies:
      restore-cursor "^2.0.0"
  
  cli-width@^2.0.0:
    version "2.2.0"
    resolved "https://registry.yarnpkg.com/cli-width/-/cli-width-2.2.0.tgz#ff19ede8a9a5e579324147b0c11f0fbcbabed639"
  
  cliui@^2.1.0:
    version "2.1.0"
    resolved "https://registry.yarnpkg.com/cliui/-/cliui-2.1.0.tgz#4b475760ff80264c762c3a1719032e91c7fea0d1"
    dependencies:
      center-align "^0.1.1"
      right-align "^0.1.1"
      wordwrap "0.0.2"
  
  co@^4.6.0:
    version "4.6.0"
    resolved "https://registry.yarnpkg.com/co/-/co-4.6.0.tgz#6ea6bdf3d853ae54ccb8e47bfa0bf3f9031fb184"
  
  coffeescript@^2.3.1:
    version "2.3.1"
    resolved "https://registry.yarnpkg.com/coffeescript/-/coffeescript-2.3.1.tgz#a25f69c251d25805c9842e57fc94bfc453ef6aed"
  
  color-convert@^1.9.0:
    version "1.9.2"
    resolved "https://registry.yarnpkg.com/color-convert/-/color-convert-1.9.2.tgz#49881b8fba67df12a96bdf3f56c0aab9e7913147"
    dependencies:
      color-name "1.1.1"
  
  color-name@1.1.1:
    version "1.1.1"
    resolved "https://registry.yarnpkg.com/color-name/-/color-name-1.1.1.tgz#4b1415304cf50028ea81643643bd82ea05803689"
  
  combined-stream@1.0.6, combined-stream@~1.0.5:
    version "1.0.6"
    resolved "https://registry.yarnpkg.com/combined-stream/-/combined-stream-1.0.6.tgz#723e7df6e801ac5613113a7e445a9b69cb632818"
    dependencies:
      delayed-stream "~1.0.0"
  
  commander@2.15.1:
    version "2.15.1"
    resolved "https://registry.yarnpkg.com/commander/-/commander-2.15.1.tgz#df46e867d0fc2aec66a34662b406a9ccafff5b0f"
  
  concat-map@0.0.1:
    version "0.0.1"
    resolved "https://registry.yarnpkg.com/concat-map/-/concat-map-0.0.1.tgz#d8a96bd77fd68df7793a73036a3ba0d5405d477b"
  
  concat-stream@^1.6.0:
    version "1.6.2"
    resolved "https://registry.yarnpkg.com/concat-stream/-/concat-stream-1.6.2.tgz#904bdf194cd3122fc675c77fc4ac3d4ff0fd1a34"
    dependencies:
      buffer-from "^1.0.0"
      inherits "^2.0.3"
      readable-stream "^2.2.2"
      typedarray "^0.0.6"
  
  core-util-is@1.0.2, core-util-is@~1.0.0:
    version "1.0.2"
    resolved "https://registry.yarnpkg.com/core-util-is/-/core-util-is-1.0.2.tgz#b5fd54220aa2bc5ab57aab7140c940754503c1a7"
  
  coveralls@^3.0.1:
    version "3.0.1"
    resolved "https://registry.yarnpkg.com/coveralls/-/coveralls-3.0.1.tgz#12e15914eaa29204e56869a5ece7b9e1492d2ae2"
    dependencies:
      js-yaml "^3.6.1"
      lcov-parse "^0.0.10"
      log-driver "^1.2.5"
      minimist "^1.2.0"
      request "^2.79.0"
  
  cross-spawn@^5.1.0:
    version "5.1.0"
    resolved "https://registry.yarnpkg.com/cross-spawn/-/cross-spawn-5.1.0.tgz#e8bd0efee58fcff6f8f94510a0a554bbfa235449"
    dependencies:
      lru-cache "^4.0.1"
      shebang-command "^1.2.0"
      which "^1.2.9"
  
  dashdash@^1.12.0:
    version "1.14.1"
    resolved "https://registry.yarnpkg.com/dashdash/-/dashdash-1.14.1.tgz#853cfa0f7cbe2fed5de20326b8dd581035f6e2f0"
    dependencies:
      assert-plus "^1.0.0"
  
  debug@3.1.0, debug@^3.1.0:
    version "3.1.0"
    resolved "https://registry.yarnpkg.com/debug/-/debug-3.1.0.tgz#5bb5a0672628b64149566ba16819e61518c67261"
    dependencies:
      ms "2.0.0"
  
  decamelize@^1.0.0:
    version "1.2.0"
    resolved "https://registry.yarnpkg.com/decamelize/-/decamelize-1.2.0.tgz#f6534d15148269b20352e7bee26f501f9a191290"
  
  deep-is@~0.1.3:
    version "0.1.3"
    resolved "https://registry.yarnpkg.com/deep-is/-/deep-is-0.1.3.tgz#b369d6fb5dbc13eecf524f91b070feedc357cf34"
  
  del@^2.0.2:
    version "2.2.2"
    resolved "https://registry.yarnpkg.com/del/-/del-2.2.2.tgz#c12c981d067846c84bcaf862cff930d907ffd1a8"
    dependencies:
      globby "^5.0.0"
      is-path-cwd "^1.0.0"
      is-path-in-cwd "^1.0.0"
      object-assign "^4.0.1"
      pify "^2.0.0"
      pinkie-promise "^2.0.0"
      rimraf "^2.2.8"
  
  delayed-stream@~1.0.0:
    version "1.0.0"
    resolved "https://registry.yarnpkg.com/delayed-stream/-/delayed-stream-1.0.0.tgz#df3ae199acadfb7d440aaae0b29e2272b24ec619"
  
  diff@3.5.0:
    version "3.5.0"
    resolved "https://registry.yarnpkg.com/diff/-/diff-3.5.0.tgz#800c0dd1e0a8bfbc95835c202ad220fe317e5a12"
  
  doctrine@^2.1.0:
    version "2.1.0"
    resolved "https://registry.yarnpkg.com/doctrine/-/doctrine-2.1.0.tgz#5cd01fc101621b42c4cd7f5d1a66243716d3f39d"
    dependencies:
      esutils "^2.0.2"
  
  ecc-jsbn@~0.1.1:
    version "0.1.1"
    resolved "https://registry.yarnpkg.com/ecc-jsbn/-/ecc-jsbn-0.1.1.tgz#0fc73a9ed5f0d53c38193398523ef7e543777505"
    dependencies:
      jsbn "~0.1.0"
  
  escape-string-regexp@1.0.5, escape-string-regexp@^1.0.2, escape-string-regexp@^1.0.5:
    version "1.0.5"
    resolved "https://registry.yarnpkg.com/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz#1b61c0562190a8dff6ae3bb2cf0200ca130b86d4"
  
  escodegen@1.8.x:
    version "1.8.1"
    resolved "https://registry.yarnpkg.com/escodegen/-/escodegen-1.8.1.tgz#5a5b53af4693110bebb0867aa3430dd3b70a1018"
    dependencies:
      esprima "^2.7.1"
      estraverse "^1.9.1"
      esutils "^2.0.2"
      optionator "^0.8.1"
    optionalDependencies:
      source-map "~0.2.0"
  
  eslint-scope@^3.7.1:
    version "3.7.1"
    resolved "https://registry.yarnpkg.com/eslint-scope/-/eslint-scope-3.7.1.tgz#3d63c3edfda02e06e01a452ad88caacc7cdcb6e8"
    dependencies:
      esrecurse "^4.1.0"
      estraverse "^4.1.1"
  
  eslint-visitor-keys@^1.0.0:
    version "1.0.0"
    resolved "https://registry.yarnpkg.com/eslint-visitor-keys/-/eslint-visitor-keys-1.0.0.tgz#3f3180fb2e291017716acb4c9d6d5b5c34a6a81d"
  
  eslint@^4.19.1:
    version "4.19.1"
    resolved "https://registry.yarnpkg.com/eslint/-/eslint-4.19.1.tgz#32d1d653e1d90408854bfb296f076ec7e186a300"
    dependencies:
      ajv "^5.3.0"
      babel-code-frame "^6.22.0"
      chalk "^2.1.0"
      concat-stream "^1.6.0"
      cross-spawn "^5.1.0"
      debug "^3.1.0"
      doctrine "^2.1.0"
      eslint-scope "^3.7.1"
      eslint-visitor-keys "^1.0.0"
      espree "^3.5.4"
      esquery "^1.0.0"
      esutils "^2.0.2"
      file-entry-cache "^2.0.0"
      functional-red-black-tree "^1.0.1"
      glob "^7.1.2"
      globals "^11.0.1"
      ignore "^3.3.3"
      imurmurhash "^0.1.4"
      inquirer "^3.0.6"
      is-resolvable "^1.0.0"
      js-yaml "^3.9.1"
      json-stable-stringify-without-jsonify "^1.0.1"
      levn "^0.3.0"
      lodash "^4.17.4"
      minimatch "^3.0.2"
      mkdirp "^0.5.1"
      natural-compare "^1.4.0"
      optionator "^0.8.2"
      path-is-inside "^1.0.2"
      pluralize "^7.0.0"
      progress "^2.0.0"
      regexpp "^1.0.1"
      require-uncached "^1.0.3"
      semver "^5.3.0"
      strip-ansi "^4.0.0"
      strip-json-comments "~2.0.1"
      table "4.0.2"
      text-table "~0.2.0"
  
  espree@^3.5.4:
    version "3.5.4"
    resolved "https://registry.yarnpkg.com/espree/-/espree-3.5.4.tgz#b0f447187c8a8bed944b815a660bddf5deb5d1a7"
    dependencies:
      acorn "^5.5.0"
      acorn-jsx "^3.0.0"
  
  esprima@2.7.x, esprima@^2.7.1:
    version "2.7.3"
    resolved "https://registry.yarnpkg.com/esprima/-/esprima-2.7.3.tgz#96e3b70d5779f6ad49cd032673d1c312767ba581"
  
  esprima@^4.0.0:
    version "4.0.0"
    resolved "https://registry.yarnpkg.com/esprima/-/esprima-4.0.0.tgz#4499eddcd1110e0b218bacf2fa7f7f59f55ca804"
  
  esquery@^1.0.0:
    version "1.0.1"
    resolved "https://registry.yarnpkg.com/esquery/-/esquery-1.0.1.tgz#406c51658b1f5991a5f9b62b1dc25b00e3e5c708"
    dependencies:
      estraverse "^4.0.0"
  
  esrecurse@^4.1.0:
    version "4.2.1"
    resolved "https://registry.yarnpkg.com/esrecurse/-/esrecurse-4.2.1.tgz#007a3b9fdbc2b3bb87e4879ea19c92fdbd3942cf"
    dependencies:
      estraverse "^4.1.0"
  
  estraverse@^1.9.1:
    version "1.9.3"
    resolved "https://registry.yarnpkg.com/estraverse/-/estraverse-1.9.3.tgz#af67f2dc922582415950926091a4005d29c9bb44"
  
  estraverse@^4.0.0, estraverse@^4.1.0, estraverse@^4.1.1:
    version "4.2.0"
    resolved "https://registry.yarnpkg.com/estraverse/-/estraverse-4.2.0.tgz#0dee3fed31fcd469618ce7342099fc1afa0bdb13"
  
  esutils@^2.0.2:
    version "2.0.2"
    resolved "https://registry.yarnpkg.com/esutils/-/esutils-2.0.2.tgz#0abf4f1caa5bcb1f7a9d8acc6dea4faaa04bac9b"
  
  extend@~3.0.1:
    version "3.0.1"
    resolved "https://registry.yarnpkg.com/extend/-/extend-3.0.1.tgz#a755ea7bc1adfcc5a31ce7e762dbaadc5e636444"
  
  external-editor@^2.0.4:
    version "2.2.0"
    resolved "https://registry.yarnpkg.com/external-editor/-/external-editor-2.2.0.tgz#045511cfd8d133f3846673d1047c154e214ad3d5"
    dependencies:
      chardet "^0.4.0"
      iconv-lite "^0.4.17"
      tmp "^0.0.33"
  
  extsprintf@1.3.0:
    version "1.3.0"
    resolved "https://registry.yarnpkg.com/extsprintf/-/extsprintf-1.3.0.tgz#96918440e3041a7a414f8c52e3c574eb3c3e1e05"
  
  extsprintf@^1.2.0:
    version "1.4.0"
    resolved "https://registry.yarnpkg.com/extsprintf/-/extsprintf-1.4.0.tgz#e2689f8f356fad62cca65a3a91c5df5f9551692f"
  
  fast-deep-equal@^1.0.0:
    version "1.1.0"
    resolved "https://registry.yarnpkg.com/fast-deep-equal/-/fast-deep-equal-1.1.0.tgz#c053477817c86b51daa853c81e059b733d023614"
  
  fast-json-stable-stringify@^2.0.0:
    version "2.0.0"
    resolved "https://registry.yarnpkg.com/fast-json-stable-stringify/-/fast-json-stable-stringify-2.0.0.tgz#d5142c0caee6b1189f87d3a76111064f86c8bbf2"
  
  fast-levenshtein@~2.0.4:
    version "2.0.6"
    resolved "https://registry.yarnpkg.com/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz#3d8a5c66883a16a30ca8643e851f19baa7797917"
  
  figures@^2.0.0:
    version "2.0.0"
    resolved "https://registry.yarnpkg.com/figures/-/figures-2.0.0.tgz#3ab1a2d2a62c8bfb431a0c94cb797a2fce27c962"
    dependencies:
      escape-string-regexp "^1.0.5"
  
  file-entry-cache@^2.0.0:
    version "2.0.0"
    resolved "https://registry.yarnpkg.com/file-entry-cache/-/file-entry-cache-2.0.0.tgz#c392990c3e684783d838b8c84a45d8a048458361"
    dependencies:
      flat-cache "^1.2.1"
      object-assign "^4.0.1"
  
  flat-cache@^1.2.1:
    version "1.3.0"
    resolved "https://registry.yarnpkg.com/flat-cache/-/flat-cache-1.3.0.tgz#d3030b32b38154f4e3b7e9c709f490f7ef97c481"
    dependencies:
      circular-json "^0.3.1"
      del "^2.0.2"
      graceful-fs "^4.1.2"
      write "^0.2.1"
  
  forever-agent@~0.6.1:
    version "0.6.1"
    resolved "https://registry.yarnpkg.com/forever-agent/-/forever-agent-0.6.1.tgz#fbc71f0c41adeb37f96c577ad1ed42d8fdacca91"
  
  form-data@~2.3.1:
    version "2.3.2"
    resolved "https://registry.yarnpkg.com/form-data/-/form-data-2.3.2.tgz#4970498be604c20c005d4f5c23aecd21d6b49099"
    dependencies:
      asynckit "^0.4.0"
      combined-stream "1.0.6"
      mime-types "^2.1.12"
  
  fs.realpath@^1.0.0:
    version "1.0.0"
    resolved "https://registry.yarnpkg.com/fs.realpath/-/fs.realpath-1.0.0.tgz#1504ad2523158caa40db4a2787cb01411994ea4f"
  
  functional-red-black-tree@^1.0.1:
    version "1.0.1"
    resolved "https://registry.yarnpkg.com/functional-red-black-tree/-/functional-red-black-tree-1.0.1.tgz#1b0ab3bd553b2a0d6399d29c0e3ea0b252078327"
  
  getpass@^0.1.1:
    version "0.1.7"
    resolved "https://registry.yarnpkg.com/getpass/-/getpass-0.1.7.tgz#5eff8e3e684d569ae4cb2b1282604e8ba62149fa"
    dependencies:
      assert-plus "^1.0.0"
  
  glob@7.1.2, glob@^7.0.3, glob@^7.0.5, glob@^7.1.2:
    version "7.1.2"
    resolved "https://registry.yarnpkg.com/glob/-/glob-7.1.2.tgz#c19c9df9a028702d678612384a6552404c636d15"
    dependencies:
      fs.realpath "^1.0.0"
      inflight "^1.0.4"
      inherits "2"
      minimatch "^3.0.4"
      once "^1.3.0"
      path-is-absolute "^1.0.0"
  
  glob@^5.0.15:
    version "5.0.15"
    resolved "https://registry.yarnpkg.com/glob/-/glob-5.0.15.tgz#1bc936b9e02f4a603fcc222ecf7633d30b8b93b1"
    dependencies:
      inflight "^1.0.4"
      inherits "2"
      minimatch "2 || 3"
      once "^1.3.0"
      path-is-absolute "^1.0.0"
  
  globals@^11.0.1:
    version "11.5.0"
    resolved "https://registry.yarnpkg.com/globals/-/globals-11.5.0.tgz#6bc840de6771173b191f13d3a9c94d441ee92642"
  
  globby@^5.0.0:
    version "5.0.0"
    resolved "https://registry.yarnpkg.com/globby/-/globby-5.0.0.tgz#ebd84667ca0dbb330b99bcfc68eac2bc54370e0d"
    dependencies:
      array-union "^1.0.1"
      arrify "^1.0.0"
      glob "^7.0.3"
      object-assign "^4.0.1"
      pify "^2.0.0"
      pinkie-promise "^2.0.0"
  
  graceful-fs@^4.1.2:
    version "4.1.11"
    resolved "https://registry.yarnpkg.com/graceful-fs/-/graceful-fs-4.1.11.tgz#0e8bdfe4d1ddb8854d64e04ea7c00e2a026e5658"
  
  growl@1.10.5:
    version "1.10.5"
    resolved "https://registry.yarnpkg.com/growl/-/growl-1.10.5.tgz#f2735dc2283674fa67478b10181059355c369e5e"
  
  handlebars@^4.0.1:
    version "4.0.11"
    resolved "https://registry.yarnpkg.com/handlebars/-/handlebars-4.0.11.tgz#630a35dfe0294bc281edae6ffc5d329fc7982dcc"
    dependencies:
      async "^1.4.0"
      optimist "^0.6.1"
      source-map "^0.4.4"
    optionalDependencies:
      uglify-js "^2.6"
  
  har-schema@^2.0.0:
    version "2.0.0"
    resolved "https://registry.yarnpkg.com/har-schema/-/har-schema-2.0.0.tgz#a94c2224ebcac04782a0d9035521f24735b7ec92"
  
  har-validator@~5.0.3:
    version "5.0.3"
    resolved "https://registry.yarnpkg.com/har-validator/-/har-validator-5.0.3.tgz#ba402c266194f15956ef15e0fcf242993f6a7dfd"
    dependencies:
      ajv "^5.1.0"
      har-schema "^2.0.0"
  
  has-ansi@^2.0.0:
    version "2.0.0"
    resolved "https://registry.yarnpkg.com/has-ansi/-/has-ansi-2.0.0.tgz#34f5049ce1ecdf2b0649af3ef24e45ed35416d91"
    dependencies:
      ansi-regex "^2.0.0"
  
  has-flag@^1.0.0:
    version "1.0.0"
    resolved "https://registry.yarnpkg.com/has-flag/-/has-flag-1.0.0.tgz#9d9e793165ce017a00f00418c43f942a7b1d11fa"
  
  has-flag@^3.0.0:
    version "3.0.0"
    resolved "https://registry.yarnpkg.com/has-flag/-/has-flag-3.0.0.tgz#b5d454dc2199ae225699f3467e5a07f3b955bafd"
  
  he@1.1.1:
    version "1.1.1"
    resolved "https://registry.yarnpkg.com/he/-/he-1.1.1.tgz#93410fd21b009735151f8868c2f271f3427e23fd"
  
  http-signature@~1.2.0:
    version "1.2.0"
    resolved "https://registry.yarnpkg.com/http-signature/-/http-signature-1.2.0.tgz#9aecd925114772f3d95b65a60abb8f7c18fbace1"
    dependencies:
      assert-plus "^1.0.0"
      jsprim "^1.2.2"
      sshpk "^1.7.0"
  
  iconv-lite@^0.4.17:
    version "0.4.23"
    resolved "https://registry.yarnpkg.com/iconv-lite/-/iconv-lite-0.4.23.tgz#297871f63be507adcfbfca715d0cd0eed84e9a63"
    dependencies:
      safer-buffer ">= 2.1.2 < 3"
  
  ignore@^3.3.3:
    version "3.3.8"
    resolved "https://registry.yarnpkg.com/ignore/-/ignore-3.3.8.tgz#3f8e9c35d38708a3a7e0e9abb6c73e7ee7707b2b"
  
  imurmurhash@^0.1.4:
    version "0.1.4"
    resolved "https://registry.yarnpkg.com/imurmurhash/-/imurmurhash-0.1.4.tgz#9218b9b2b928a238b13dc4fb6b6d576f231453ea"
  
  inflight@^1.0.4:
    version "1.0.6"
    resolved "https://registry.yarnpkg.com/inflight/-/inflight-1.0.6.tgz#49bd6331d7d02d0c09bc910a1075ba8165b56df9"
    dependencies:
      once "^1.3.0"
      wrappy "1"
  
  inherits@2, inherits@^2.0.3, inherits@~2.0.3:
    version "2.0.3"
    resolved "https://registry.yarnpkg.com/inherits/-/inherits-2.0.3.tgz#633c2c83e3da42a502f52466022480f4208261de"
  
  inquirer@^3.0.6:
    version "3.3.0"
    resolved "https://registry.yarnpkg.com/inquirer/-/inquirer-3.3.0.tgz#9dd2f2ad765dcab1ff0443b491442a20ba227dc9"
    dependencies:
      ansi-escapes "^3.0.0"
      chalk "^2.0.0"
      cli-cursor "^2.1.0"
      cli-width "^2.0.0"
      external-editor "^2.0.4"
      figures "^2.0.0"
      lodash "^4.3.0"
      mute-stream "0.0.7"
      run-async "^2.2.0"
      rx-lite "^4.0.8"
      rx-lite-aggregates "^4.0.8"
      string-width "^2.1.0"
      strip-ansi "^4.0.0"
      through "^2.3.6"
  
  is-buffer@^1.1.5:
    version "1.1.6"
    resolved "https://registry.yarnpkg.com/is-buffer/-/is-buffer-1.1.6.tgz#efaa2ea9daa0d7ab2ea13a97b2b8ad51fefbe8be"
  
  is-fullwidth-code-point@^2.0.0:
    version "2.0.0"
    resolved "https://registry.yarnpkg.com/is-fullwidth-code-point/-/is-fullwidth-code-point-2.0.0.tgz#a3b30a5c4f199183167aaab93beefae3ddfb654f"
  
  is-path-cwd@^1.0.0:
    version "1.0.0"
    resolved "https://registry.yarnpkg.com/is-path-cwd/-/is-path-cwd-1.0.0.tgz#d225ec23132e89edd38fda767472e62e65f1106d"
  
  is-path-in-cwd@^1.0.0:
    version "1.0.1"
    resolved "https://registry.yarnpkg.com/is-path-in-cwd/-/is-path-in-cwd-1.0.1.tgz#5ac48b345ef675339bd6c7a48a912110b241cf52"
    dependencies:
      is-path-inside "^1.0.0"
  
  is-path-inside@^1.0.0:
    version "1.0.1"
    resolved "https://registry.yarnpkg.com/is-path-inside/-/is-path-inside-1.0.1.tgz#8ef5b7de50437a3fdca6b4e865ef7aa55cb48036"
    dependencies:
      path-is-inside "^1.0.1"
  
  is-promise@^2.1.0:
    version "2.1.0"
    resolved "https://registry.yarnpkg.com/is-promise/-/is-promise-2.1.0.tgz#79a2a9ece7f096e80f36d2b2f3bc16c1ff4bf3fa"
  
  is-resolvable@^1.0.0:
    version "1.1.0"
    resolved "https://registry.yarnpkg.com/is-resolvable/-/is-resolvable-1.1.0.tgz#fb18f87ce1feb925169c9a407c19318a3206ed88"
  
  is-typedarray@~1.0.0:
    version "1.0.0"
    resolved "https://registry.yarnpkg.com/is-typedarray/-/is-typedarray-1.0.0.tgz#e479c80858df0c1b11ddda6940f96011fcda4a9a"
  
  isarray@~1.0.0:
    version "1.0.0"
    resolved "https://registry.yarnpkg.com/isarray/-/isarray-1.0.0.tgz#bb935d48582cba168c06834957a54a3e07124f11"
  
  isexe@^2.0.0:
    version "2.0.0"
    resolved "https://registry.yarnpkg.com/isexe/-/isexe-2.0.0.tgz#e8fbf374dc556ff8947a10dcb0572d633f2cfa10"
  
  isstream@~0.1.2:
    version "0.1.2"
    resolved "https://registry.yarnpkg.com/isstream/-/isstream-0.1.2.tgz#47e63f7af55afa6f92e1500e690eb8b8529c099a"
  
  istanbul@^0.4.5:
    version "0.4.5"
    resolved "https://registry.yarnpkg.com/istanbul/-/istanbul-0.4.5.tgz#65c7d73d4c4da84d4f3ac310b918fb0b8033733b"
    dependencies:
      abbrev "1.0.x"
      async "1.x"
      escodegen "1.8.x"
      esprima "2.7.x"
      glob "^5.0.15"
      handlebars "^4.0.1"
      js-yaml "3.x"
      mkdirp "0.5.x"
      nopt "3.x"
      once "1.x"
      resolve "1.1.x"
      supports-color "^3.1.0"
      which "^1.1.1"
      wordwrap "^1.0.0"
  
  js-tokens@^3.0.2:
    version "3.0.2"
    resolved "https://registry.yarnpkg.com/js-tokens/-/js-tokens-3.0.2.tgz#9866df395102130e38f7f996bceb65443209c25b"
  
  js-yaml@3.x, js-yaml@^3.6.1, js-yaml@^3.9.1:
    version "3.12.0"
    resolved "https://registry.yarnpkg.com/js-yaml/-/js-yaml-3.12.0.tgz#eaed656ec8344f10f527c6bfa1b6e2244de167d1"
    dependencies:
      argparse "^1.0.7"
      esprima "^4.0.0"
  
  jsbn@~0.1.0:
    version "0.1.1"
    resolved "https://registry.yarnpkg.com/jsbn/-/jsbn-0.1.1.tgz#a5e654c2e5a2deb5f201d96cefbca80c0ef2f513"
  
  json-schema-traverse@^0.3.0:
    version "0.3.1"
    resolved "https://registry.yarnpkg.com/json-schema-traverse/-/json-schema-traverse-0.3.1.tgz#349a6d44c53a51de89b40805c5d5e59b417d3340"
  
  json-schema@0.2.3:
    version "0.2.3"
    resolved "https://registry.yarnpkg.com/json-schema/-/json-schema-0.2.3.tgz#b480c892e59a2f05954ce727bd3f2a4e882f9e13"
  
  json-stable-stringify-without-jsonify@^1.0.1:
    version "1.0.1"
    resolved "https://registry.yarnpkg.com/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz#9db7b59496ad3f3cfef30a75142d2d930ad72651"
  
  json-stringify-safe@~5.0.1:
    version "5.0.1"
    resolved "https://registry.yarnpkg.com/json-stringify-safe/-/json-stringify-safe-5.0.1.tgz#1296a2d58fd45f19a0f6ce01d65701e2c735b6eb"
  
  jsprim@^1.2.2:
    version "1.4.1"
    resolved "https://registry.yarnpkg.com/jsprim/-/jsprim-1.4.1.tgz#313e66bc1e5cc06e438bc1b7499c2e5c56acb6a2"
    dependencies:
      assert-plus "1.0.0"
      extsprintf "1.3.0"
      json-schema "0.2.3"
      verror "1.10.0"
  
  kind-of@^3.0.2:
    version "3.2.2"
    resolved "https://registry.yarnpkg.com/kind-of/-/kind-of-3.2.2.tgz#31ea21a734bab9bbb0f32466d893aea51e4a3c64"
    dependencies:
      is-buffer "^1.1.5"
  
  lazy-cache@^1.0.3:
    version "1.0.4"
    resolved "https://registry.yarnpkg.com/lazy-cache/-/lazy-cache-1.0.4.tgz#a1d78fc3a50474cb80845d3b3b6e1da49a446e8e"
  
  lcov-parse@^0.0.10:
    version "0.0.10"
    resolved "https://registry.yarnpkg.com/lcov-parse/-/lcov-parse-0.0.10.tgz#1b0b8ff9ac9c7889250582b70b71315d9da6d9a3"
  
  levn@^0.3.0, levn@~0.3.0:
    version "0.3.0"
    resolved "https://registry.yarnpkg.com/levn/-/levn-0.3.0.tgz#3b09924edf9f083c0490fdd4c0bc4421e04764ee"
    dependencies:
      prelude-ls "~1.1.2"
      type-check "~0.3.2"
  
  lodash@^4.17.4, lodash@^4.3.0:
    version "4.17.10"
    resolved "https://registry.yarnpkg.com/lodash/-/lodash-4.17.10.tgz#1b7793cf7259ea38fb3661d4d38b3260af8ae4e7"
  
  log-driver@^1.2.5:
    version "1.2.7"
    resolved "https://registry.yarnpkg.com/log-driver/-/log-driver-1.2.7.tgz#63b95021f0702fedfa2c9bb0a24e7797d71871d8"
  
  longest@^1.0.1:
    version "1.0.1"
    resolved "https://registry.yarnpkg.com/longest/-/longest-1.0.1.tgz#30a0b2da38f73770e8294a0d22e6625ed77d0097"
  
  lru-cache@^4.0.1:
    version "4.1.3"
    resolved "https://registry.yarnpkg.com/lru-cache/-/lru-cache-4.1.3.tgz#a1175cf3496dfc8436c156c334b4955992bce69c"
    dependencies:
      pseudomap "^1.0.2"
      yallist "^2.1.2"
  
  mime-db@~1.33.0:
    version "1.33.0"
    resolved "https://registry.yarnpkg.com/mime-db/-/mime-db-1.33.0.tgz#a3492050a5cb9b63450541e39d9788d2272783db"
  
  mime-types@^2.1.12, mime-types@~2.1.17:
    version "2.1.18"
    resolved "https://registry.yarnpkg.com/mime-types/-/mime-types-2.1.18.tgz#6f323f60a83d11146f831ff11fd66e2fe5503bb8"
    dependencies:
      mime-db "~1.33.0"
  
  mimic-fn@^1.0.0:
    version "1.2.0"
    resolved "https://registry.yarnpkg.com/mimic-fn/-/mimic-fn-1.2.0.tgz#820c86a39334640e99516928bd03fca88057d022"
  
  "minimatch@2 || 3", minimatch@3.0.4, minimatch@^3.0.2, minimatch@^3.0.4:
    version "3.0.4"
    resolved "https://registry.yarnpkg.com/minimatch/-/minimatch-3.0.4.tgz#5166e286457f03306064be5497e8dbb0c3d32083"
    dependencies:
      brace-expansion "^1.1.7"
  
  minimist@0.0.8:
    version "0.0.8"
    resolved "https://registry.yarnpkg.com/minimist/-/minimist-0.0.8.tgz#857fcabfc3397d2625b8228262e86aa7a011b05d"
  
  minimist@^1.2.0:
    version "1.2.0"
    resolved "https://registry.yarnpkg.com/minimist/-/minimist-1.2.0.tgz#a35008b20f41383eec1fb914f4cd5df79a264284"
  
  minimist@~0.0.1:
    version "0.0.10"
    resolved "https://registry.yarnpkg.com/minimist/-/minimist-0.0.10.tgz#de3f98543dbf96082be48ad1a0c7cda836301dcf"
  
  mkdirp@0.5.1, mkdirp@0.5.x, mkdirp@^0.5.1:
    version "0.5.1"
    resolved "https://registry.yarnpkg.com/mkdirp/-/mkdirp-0.5.1.tgz#30057438eac6cf7f8c4767f38648d6697d75c903"
    dependencies:
      minimist "0.0.8"
  
  mocha@^5.2.0:
    version "5.2.0"
    resolved "https://registry.yarnpkg.com/mocha/-/mocha-5.2.0.tgz#6d8ae508f59167f940f2b5b3c4a612ae50c90ae6"
    dependencies:
      browser-stdout "1.3.1"
      commander "2.15.1"
      debug "3.1.0"
      diff "3.5.0"
      escape-string-regexp "1.0.5"
      glob "7.1.2"
      growl "1.10.5"
      he "1.1.1"
      minimatch "3.0.4"
      mkdirp "0.5.1"
      supports-color "5.4.0"
  
  ms@2.0.0:
    version "2.0.0"
    resolved "https://registry.yarnpkg.com/ms/-/ms-2.0.0.tgz#5608aeadfc00be6c2901df5f9861788de0d597c8"
  
  mute-stream@0.0.7:
    version "0.0.7"
    resolved "https://registry.yarnpkg.com/mute-stream/-/mute-stream-0.0.7.tgz#3075ce93bc21b8fab43e1bc4da7e8115ed1e7bab"
  
  natural-compare@^1.4.0:
    version "1.4.0"
    resolved "https://registry.yarnpkg.com/natural-compare/-/natural-compare-1.4.0.tgz#4abebfeed7541f2c27acfb29bdbbd15c8d5ba4f7"
  
  nopt@3.x:
    version "3.0.6"
    resolved "https://registry.yarnpkg.com/nopt/-/nopt-3.0.6.tgz#c6465dbf08abcd4db359317f79ac68a646b28ff9"
    dependencies:
      abbrev "1"
  
  oauth-sign@~0.8.2:
    version "0.8.2"
    resolved "https://registry.yarnpkg.com/oauth-sign/-/oauth-sign-0.8.2.tgz#46a6ab7f0aead8deae9ec0565780b7d4efeb9d43"
  
  object-assign@^4.0.1:
    version "4.1.1"
    resolved "https://registry.yarnpkg.com/object-assign/-/object-assign-4.1.1.tgz#2109adc7965887cfc05cbbd442cac8bfbb360863"
  
  once@1.x, once@^1.3.0:
    version "1.4.0"
    resolved "https://registry.yarnpkg.com/once/-/once-1.4.0.tgz#583b1aa775961d4b113ac17d9c50baef9dd76bd1"
    dependencies:
      wrappy "1"
  
  onetime@^2.0.0:
    version "2.0.1"
    resolved "https://registry.yarnpkg.com/onetime/-/onetime-2.0.1.tgz#067428230fd67443b2794b22bba528b6867962d4"
    dependencies:
      mimic-fn "^1.0.0"
  
  optimist@^0.6.1:
    version "0.6.1"
    resolved "https://registry.yarnpkg.com/optimist/-/optimist-0.6.1.tgz#da3ea74686fa21a19a111c326e90eb15a0196686"
    dependencies:
      minimist "~0.0.1"
      wordwrap "~0.0.2"
  
  optionator@^0.8.1, optionator@^0.8.2:
    version "0.8.2"
    resolved "https://registry.yarnpkg.com/optionator/-/optionator-0.8.2.tgz#364c5e409d3f4d6301d6c0b4c05bba50180aeb64"
    dependencies:
      deep-is "~0.1.3"
      fast-levenshtein "~2.0.4"
      levn "~0.3.0"
      prelude-ls "~1.1.2"
      type-check "~0.3.2"
      wordwrap "~1.0.0"
  
  os-tmpdir@~1.0.2:
    version "1.0.2"
    resolved "https://registry.yarnpkg.com/os-tmpdir/-/os-tmpdir-1.0.2.tgz#bbe67406c79aa85c5cfec766fe5734555dfa1274"
  
  path-is-absolute@^1.0.0:
    version "1.0.1"
    resolved "https://registry.yarnpkg.com/path-is-absolute/-/path-is-absolute-1.0.1.tgz#174b9268735534ffbc7ace6bf53a5a9e1b5c5f5f"
  
  path-is-inside@^1.0.1, path-is-inside@^1.0.2:
    version "1.0.2"
    resolved "https://registry.yarnpkg.com/path-is-inside/-/path-is-inside-1.0.2.tgz#365417dede44430d1c11af61027facf074bdfc53"
  
  performance-now@^2.1.0:
    version "2.1.0"
    resolved "https://registry.yarnpkg.com/performance-now/-/performance-now-2.1.0.tgz#6309f4e0e5fa913ec1c69307ae364b4b377c9e7b"
  
  pify@^2.0.0:
    version "2.3.0"
    resolved "https://registry.yarnpkg.com/pify/-/pify-2.3.0.tgz#ed141a6ac043a849ea588498e7dca8b15330e90c"
  
  pinkie-promise@^2.0.0:
    version "2.0.1"
    resolved "https://registry.yarnpkg.com/pinkie-promise/-/pinkie-promise-2.0.1.tgz#2135d6dfa7a358c069ac9b178776288228450ffa"
    dependencies:
      pinkie "^2.0.0"
  
  pinkie@^2.0.0:
    version "2.0.4"
    resolved "https://registry.yarnpkg.com/pinkie/-/pinkie-2.0.4.tgz#72556b80cfa0d48a974e80e77248e80ed4f7f870"
  
  pluralize@^7.0.0:
    version "7.0.0"
    resolved "https://registry.yarnpkg.com/pluralize/-/pluralize-7.0.0.tgz#298b89df8b93b0221dbf421ad2b1b1ea23fc6777"
  
  prelude-ls@~1.1.2:
    version "1.1.2"
    resolved "https://registry.yarnpkg.com/prelude-ls/-/prelude-ls-1.1.2.tgz#21932a549f5e52ffd9a827f570e04be62a97da54"
  
  process-nextick-args@~2.0.0:
    version "2.0.0"
    resolved "https://registry.yarnpkg.com/process-nextick-args/-/process-nextick-args-2.0.0.tgz#a37d732f4271b4ab1ad070d35508e8290788ffaa"
  
  progress@^2.0.0:
    version "2.0.0"
    resolved "https://registry.yarnpkg.com/progress/-/progress-2.0.0.tgz#8a1be366bf8fc23db2bd23f10c6fe920b4389d1f"
  
  pseudomap@^1.0.2:
    version "1.0.2"
    resolved "https://registry.yarnpkg.com/pseudomap/-/pseudomap-1.0.2.tgz#f052a28da70e618917ef0a8ac34c1ae5a68286b3"
  
  punycode@^1.4.1:
    version "1.4.1"
    resolved "https://registry.yarnpkg.com/punycode/-/punycode-1.4.1.tgz#c0d5a63b2718800ad8e1eb0fa5269c84dd41845e"
  
  qs@~6.5.1:
    version "6.5.2"
    resolved "https://registry.yarnpkg.com/qs/-/qs-6.5.2.tgz#cb3ae806e8740444584ef154ce8ee98d403f3e36"
  
  readable-stream@^2.2.2:
    version "2.3.6"
    resolved "https://registry.yarnpkg.com/readable-stream/-/readable-stream-2.3.6.tgz#b11c27d88b8ff1fbe070643cf94b0c79ae1b0aaf"
    dependencies:
      core-util-is "~1.0.0"
      inherits "~2.0.3"
      isarray "~1.0.0"
      process-nextick-args "~2.0.0"
      safe-buffer "~5.1.1"
      string_decoder "~1.1.1"
      util-deprecate "~1.0.1"
  
  regexpp@^1.0.1:
    version "1.1.0"
    resolved "https://registry.yarnpkg.com/regexpp/-/regexpp-1.1.0.tgz#0e3516dd0b7904f413d2d4193dce4618c3a689ab"
  
  repeat-string@^1.5.2:
    version "1.6.1"
    resolved "https://registry.yarnpkg.com/repeat-string/-/repeat-string-1.6.1.tgz#8dcae470e1c88abc2d600fff4a776286da75e637"
  
  request@^2.79.0:
    version "2.87.0"
    resolved "https://registry.yarnpkg.com/request/-/request-2.87.0.tgz#32f00235cd08d482b4d0d68db93a829c0ed5756e"
    dependencies:
      aws-sign2 "~0.7.0"
      aws4 "^1.6.0"
      caseless "~0.12.0"
      combined-stream "~1.0.5"
      extend "~3.0.1"
      forever-agent "~0.6.1"
      form-data "~2.3.1"
      har-validator "~5.0.3"
      http-signature "~1.2.0"
      is-typedarray "~1.0.0"
      isstream "~0.1.2"
      json-stringify-safe "~5.0.1"
      mime-types "~2.1.17"
      oauth-sign "~0.8.2"
      performance-now "^2.1.0"
      qs "~6.5.1"
      safe-buffer "^5.1.1"
      tough-cookie "~2.3.3"
      tunnel-agent "^0.6.0"
      uuid "^3.1.0"
  
  require-uncached@^1.0.3:
    version "1.0.3"
    resolved "https://registry.yarnpkg.com/require-uncached/-/require-uncached-1.0.3.tgz#4e0d56d6c9662fd31e43011c4b95aa49955421d3"
    dependencies:
      caller-path "^0.1.0"
      resolve-from "^1.0.0"
  
  resolve-from@^1.0.0:
    version "1.0.1"
    resolved "https://registry.yarnpkg.com/resolve-from/-/resolve-from-1.0.1.tgz#26cbfe935d1aeeeabb29bc3fe5aeb01e93d44226"
  
  resolve@1.1.x:
    version "1.1.7"
    resolved "https://registry.yarnpkg.com/resolve/-/resolve-1.1.7.tgz#203114d82ad2c5ed9e8e0411b3932875e889e97b"
  
  restore-cursor@^2.0.0:
    version "2.0.0"
    resolved "https://registry.yarnpkg.com/restore-cursor/-/restore-cursor-2.0.0.tgz#9f7ee287f82fd326d4fd162923d62129eee0dfaf"
    dependencies:
      onetime "^2.0.0"
      signal-exit "^3.0.2"
  
  right-align@^0.1.1:
    version "0.1.3"
    resolved "https://registry.yarnpkg.com/right-align/-/right-align-0.1.3.tgz#61339b722fe6a3515689210d24e14c96148613ef"
    dependencies:
      align-text "^0.1.1"
  
  rimraf@^2.2.8:
    version "2.6.2"
    resolved "https://registry.yarnpkg.com/rimraf/-/rimraf-2.6.2.tgz#2ed8150d24a16ea8651e6d6ef0f47c4158ce7a36"
    dependencies:
      glob "^7.0.5"
  
  run-async@^2.2.0:
    version "2.3.0"
    resolved "https://registry.yarnpkg.com/run-async/-/run-async-2.3.0.tgz#0371ab4ae0bdd720d4166d7dfda64ff7a445a6c0"
    dependencies:
      is-promise "^2.1.0"
  
  rx-lite-aggregates@^4.0.8:
    version "4.0.8"
    resolved "https://registry.yarnpkg.com/rx-lite-aggregates/-/rx-lite-aggregates-4.0.8.tgz#753b87a89a11c95467c4ac1626c4efc4e05c67be"
    dependencies:
      rx-lite "*"
  
  rx-lite@*, rx-lite@^4.0.8:
    version "4.0.8"
    resolved "https://registry.yarnpkg.com/rx-lite/-/rx-lite-4.0.8.tgz#0b1e11af8bc44836f04a6407e92da42467b79444"
  
  safe-buffer@^5.0.1, safe-buffer@^5.1.1, safe-buffer@~5.1.0, safe-buffer@~5.1.1:
    version "5.1.2"
    resolved "https://registry.yarnpkg.com/safe-buffer/-/safe-buffer-5.1.2.tgz#991ec69d296e0313747d59bdfd2b745c35f8828d"
  
  "safer-buffer@>= 2.1.2 < 3", safer-buffer@^2.0.2:
    version "2.1.2"
    resolved "https://registry.yarnpkg.com/safer-buffer/-/safer-buffer-2.1.2.tgz#44fa161b0187b9549dd84bb91802f9bd8385cd6a"
  
  semver@^5.3.0:
    version "5.5.0"
    resolved "https://registry.yarnpkg.com/semver/-/semver-5.5.0.tgz#dc4bbc7a6ca9d916dee5d43516f0092b58f7b8ab"
  
  shebang-command@^1.2.0:
    version "1.2.0"
    resolved "https://registry.yarnpkg.com/shebang-command/-/shebang-command-1.2.0.tgz#44aac65b695b03398968c39f363fee5deafdf1ea"
    dependencies:
      shebang-regex "^1.0.0"
  
  shebang-regex@^1.0.0:
    version "1.0.0"
    resolved "https://registry.yarnpkg.com/shebang-regex/-/shebang-regex-1.0.0.tgz#da42f49740c0b42db2ca9728571cb190c98efea3"
  
  should-equal@^2.0.0:
    version "2.0.0"
    resolved "https://registry.yarnpkg.com/should-equal/-/should-equal-2.0.0.tgz#6072cf83047360867e68e98b09d71143d04ee0c3"
    dependencies:
      should-type "^1.4.0"
  
  should-format@^3.0.3:
    version "3.0.3"
    resolved "https://registry.yarnpkg.com/should-format/-/should-format-3.0.3.tgz#9bfc8f74fa39205c53d38c34d717303e277124f1"
    dependencies:
      should-type "^1.3.0"
      should-type-adaptors "^1.0.1"
  
  should-type-adaptors@^1.0.1:
    version "1.1.0"
    resolved "https://registry.yarnpkg.com/should-type-adaptors/-/should-type-adaptors-1.1.0.tgz#401e7f33b5533033944d5cd8bf2b65027792e27a"
    dependencies:
      should-type "^1.3.0"
      should-util "^1.0.0"
  
  should-type@^1.3.0, should-type@^1.4.0:
    version "1.4.0"
    resolved "https://registry.yarnpkg.com/should-type/-/should-type-1.4.0.tgz#0756d8ce846dfd09843a6947719dfa0d4cff5cf3"
  
  should-util@^1.0.0:
    version "1.0.0"
    resolved "https://registry.yarnpkg.com/should-util/-/should-util-1.0.0.tgz#c98cda374aa6b190df8ba87c9889c2b4db620063"
  
  should@^13.2.1:
    version "13.2.1"
    resolved "https://registry.yarnpkg.com/should/-/should-13.2.1.tgz#84e6ebfbb145c79e0ae42307b25b3f62dcaf574e"
    dependencies:
      should-equal "^2.0.0"
      should-format "^3.0.3"
      should-type "^1.4.0"
      should-type-adaptors "^1.0.1"
      should-util "^1.0.0"
  
  signal-exit@^3.0.2:
    version "3.0.2"
    resolved "https://registry.yarnpkg.com/signal-exit/-/signal-exit-3.0.2.tgz#b5fdc08f1287ea1178628e415e25132b73646c6d"
  
  slice-ansi@1.0.0:
    version "1.0.0"
    resolved "https://registry.yarnpkg.com/slice-ansi/-/slice-ansi-1.0.0.tgz#044f1a49d8842ff307aad6b505ed178bd950134d"
    dependencies:
      is-fullwidth-code-point "^2.0.0"
  
  source-map@^0.4.4:
    version "0.4.4"
    resolved "https://registry.yarnpkg.com/source-map/-/source-map-0.4.4.tgz#eba4f5da9c0dc999de68032d8b4f76173652036b"
    dependencies:
      amdefine ">=0.0.4"
  
  source-map@~0.2.0:
    version "0.2.0"
    resolved "https://registry.yarnpkg.com/source-map/-/source-map-0.2.0.tgz#dab73fbcfc2ba819b4de03bd6f6eaa48164b3f9d"
    dependencies:
      amdefine ">=0.0.4"
  
  source-map@~0.5.1:
    version "0.5.7"
    resolved "https://registry.yarnpkg.com/source-map/-/source-map-0.5.7.tgz#8a039d2d1021d22d1ea14c80d8ea468ba2ef3fcc"
  
  sprintf-js@~1.0.2:
    version "1.0.3"
    resolved "https://registry.yarnpkg.com/sprintf-js/-/sprintf-js-1.0.3.tgz#04e6926f662895354f3dd015203633b857297e2c"
  
  sshpk@^1.7.0:
    version "1.14.2"
    resolved "https://registry.yarnpkg.com/sshpk/-/sshpk-1.14.2.tgz#c6fc61648a3d9c4e764fd3fcdf4ea105e492ba98"
    dependencies:
      asn1 "~0.2.3"
      assert-plus "^1.0.0"
      dashdash "^1.12.0"
      getpass "^0.1.1"
      safer-buffer "^2.0.2"
    optionalDependencies:
      bcrypt-pbkdf "^1.0.0"
      ecc-jsbn "~0.1.1"
      jsbn "~0.1.0"
      tweetnacl "~0.14.0"
  
  string-width@^2.1.0, string-width@^2.1.1:
    version "2.1.1"
    resolved "https://registry.yarnpkg.com/string-width/-/string-width-2.1.1.tgz#ab93f27a8dc13d28cac815c462143a6d9012ae9e"
    dependencies:
      is-fullwidth-code-point "^2.0.0"
      strip-ansi "^4.0.0"
  
  string_decoder@~1.1.1:
    version "1.1.1"
    resolved "https://registry.yarnpkg.com/string_decoder/-/string_decoder-1.1.1.tgz#9cf1611ba62685d7030ae9e4ba34149c3af03fc8"
    dependencies:
      safe-buffer "~5.1.0"
  
  strip-ansi@^3.0.0:
    version "3.0.1"
    resolved "https://registry.yarnpkg.com/strip-ansi/-/strip-ansi-3.0.1.tgz#6a385fb8853d952d5ff05d0e8aaf94278dc63dcf"
    dependencies:
      ansi-regex "^2.0.0"
  
  strip-ansi@^4.0.0:
    version "4.0.0"
    resolved "https://registry.yarnpkg.com/strip-ansi/-/strip-ansi-4.0.0.tgz#a8479022eb1ac368a871389b635262c505ee368f"
    dependencies:
      ansi-regex "^3.0.0"
  
  strip-json-comments@~2.0.1:
    version "2.0.1"
    resolved "https://registry.yarnpkg.com/strip-json-comments/-/strip-json-comments-2.0.1.tgz#3c531942e908c2697c0ec344858c286c7ca0a60a"
  
  supports-color@5.4.0, supports-color@^5.3.0:
    version "5.4.0"
    resolved "https://registry.yarnpkg.com/supports-color/-/supports-color-5.4.0.tgz#1c6b337402c2137605efe19f10fec390f6faab54"
    dependencies:
      has-flag "^3.0.0"
  
  supports-color@^2.0.0:
    version "2.0.0"
    resolved "https://registry.yarnpkg.com/supports-color/-/supports-color-2.0.0.tgz#535d045ce6b6363fa40117084629995e9df324c7"
  
  supports-color@^3.1.0:
    version "3.2.3"
    resolved "https://registry.yarnpkg.com/supports-color/-/supports-color-3.2.3.tgz#65ac0504b3954171d8a64946b2ae3cbb8a5f54f6"
    dependencies:
      has-flag "^1.0.0"
  
  table@4.0.2:
    version "4.0.2"
    resolved "https://registry.yarnpkg.com/table/-/table-4.0.2.tgz#a33447375391e766ad34d3486e6e2aedc84d2e36"
    dependencies:
      ajv "^5.2.3"
      ajv-keywords "^2.1.0"
      chalk "^2.1.0"
      lodash "^4.17.4"
      slice-ansi "1.0.0"
      string-width "^2.1.1"
  
  text-table@~0.2.0:
    version "0.2.0"
    resolved "https://registry.yarnpkg.com/text-table/-/text-table-0.2.0.tgz#7f5ee823ae805207c00af2df4a84ec3fcfa570b4"
  
  through@^2.3.6:
    version "2.3.8"
    resolved "https://registry.yarnpkg.com/through/-/through-2.3.8.tgz#0dd4c9ffaabc357960b1b724115d7e0e86a2e1f5"
  
  tmp@^0.0.33:
    version "0.0.33"
    resolved "https://registry.yarnpkg.com/tmp/-/tmp-0.0.33.tgz#6d34335889768d21b2bcda0aa277ced3b1bfadf9"
    dependencies:
      os-tmpdir "~1.0.2"
  
  tough-cookie@~2.3.3:
    version "2.3.4"
    resolved "https://registry.yarnpkg.com/tough-cookie/-/tough-cookie-2.3.4.tgz#ec60cee38ac675063ffc97a5c18970578ee83655"
    dependencies:
      punycode "^1.4.1"
  
  tunnel-agent@^0.6.0:
    version "0.6.0"
    resolved "https://registry.yarnpkg.com/tunnel-agent/-/tunnel-agent-0.6.0.tgz#27a5dea06b36b04a0a9966774b290868f0fc40fd"
    dependencies:
      safe-buffer "^5.0.1"
  
  tweetnacl@^0.14.3, tweetnacl@~0.14.0:
    version "0.14.5"
    resolved "https://registry.yarnpkg.com/tweetnacl/-/tweetnacl-0.14.5.tgz#5ae68177f192d4456269d108afa93ff8743f4f64"
  
  type-check@~0.3.2:
    version "0.3.2"
    resolved "https://registry.yarnpkg.com/type-check/-/type-check-0.3.2.tgz#5884cab512cf1d355e3fb784f30804b2b520db72"
    dependencies:
      prelude-ls "~1.1.2"
  
  typedarray@^0.0.6:
    version "0.0.6"
    resolved "https://registry.yarnpkg.com/typedarray/-/typedarray-0.0.6.tgz#867ac74e3864187b1d3d47d996a78ec5c8830777"
  
  uglify-js@^2.6:
    version "2.8.29"
    resolved "https://registry.yarnpkg.com/uglify-js/-/uglify-js-2.8.29.tgz#29c5733148057bb4e1f75df35b7a9cb72e6a59dd"
    dependencies:
      source-map "~0.5.1"
      yargs "~3.10.0"
    optionalDependencies:
      uglify-to-browserify "~1.0.0"
  
  uglify-to-browserify@~1.0.0:
    version "1.0.2"
    resolved "https://registry.yarnpkg.com/uglify-to-browserify/-/uglify-to-browserify-1.0.2.tgz#6e0924d6bda6b5afe349e39a6d632850a0f882b7"
  
  util-deprecate@~1.0.1:
    version "1.0.2"
    resolved "https://registry.yarnpkg.com/util-deprecate/-/util-deprecate-1.0.2.tgz#450d4dc9fa70de732762fbd2d4a28981419a0ccf"
  
  uuid@^3.1.0:
    version "3.2.1"
    resolved "https://registry.yarnpkg.com/uuid/-/uuid-3.2.1.tgz#12c528bb9d58d0b9265d9a2f6f0fe8be17ff1f14"
  
  verror@1.10.0:
    version "1.10.0"
    resolved "https://registry.yarnpkg.com/verror/-/verror-1.10.0.tgz#3a105ca17053af55d6e270c1f8288682e18da400"
    dependencies:
      assert-plus "^1.0.0"
      core-util-is "1.0.2"
      extsprintf "^1.2.0"
  
  which@^1.1.1, which@^1.2.9:
    version "1.3.1"
    resolved "https://registry.yarnpkg.com/which/-/which-1.3.1.tgz#a45043d54f5805316da8d62f9f50918d3da70b0a"
    dependencies:
      isexe "^2.0.0"
  
  window-size@0.1.0:
    version "0.1.0"
    resolved "https://registry.yarnpkg.com/window-size/-/window-size-0.1.0.tgz#5438cd2ea93b202efa3a19fe8887aee7c94f9c9d"
  
  wordwrap@0.0.2:
    version "0.0.2"
    resolved "https://registry.yarnpkg.com/wordwrap/-/wordwrap-0.0.2.tgz#b79669bb42ecb409f83d583cad52ca17eaa1643f"
  
  wordwrap@^1.0.0, wordwrap@~1.0.0:
    version "1.0.0"
    resolved "https://registry.yarnpkg.com/wordwrap/-/wordwrap-1.0.0.tgz#27584810891456a4171c8d0226441ade90cbcaeb"
  
  wordwrap@~0.0.2:
    version "0.0.3"
    resolved "https://registry.yarnpkg.com/wordwrap/-/wordwrap-0.0.3.tgz#a3d5da6cd5c0bc0008d37234bbaf1bed63059107"
  
  wrappy@1:
    version "1.0.2"
    resolved "https://registry.yarnpkg.com/wrappy/-/wrappy-1.0.2.tgz#b5243d8f3ec1aa35f1364605bc0d1036e30ab69f"
  
  write@^0.2.1:
    version "0.2.1"
    resolved "https://registry.yarnpkg.com/write/-/write-0.2.1.tgz#5fc03828e264cea3fe91455476f7a3c566cb0757"
    dependencies:
      mkdirp "^0.5.1"
  
  yallist@^2.1.2:
    version "2.1.2"
    resolved "https://registry.yarnpkg.com/yallist/-/yallist-2.1.2.tgz#1c11f9218f076089a47dd512f93c6699a6a81d52"
  
  yargs@~3.10.0:
    version "3.10.0"
    resolved "https://registry.yarnpkg.com/yargs/-/yargs-3.10.0.tgz#f7ee7bd857dd7c1d2d38c0e74efbd681d1431fd1"
    dependencies:
      camelcase "^1.0.2"
      cliui "^2.1.0"
      decamelize "^1.0.0"
      window-size "0.1.0"

Trace: 
  Error: Command failed.
  Exit code: 1
  Command: sh
  Arguments: -c mocha --require coffeescript/register
  Directory: /src/qix-/node-is-arrayish
  Output:
  
      at ProcessTermError.MessageError (/Users/<USER>/.yarn/lib/cli.js:186:110)
      at new ProcessTermError (/Users/<USER>/.yarn/lib/cli.js:226:113)
      at ChildProcess.<anonymous> (/Users/<USER>/.yarn/lib/cli.js:30281:17)
      at ChildProcess.emit (events.js:127:13)
      at maybeClose (internal/child_process.js:933:16)
      at Process.ChildProcess._handle.onexit (internal/child_process.js:220:5)
