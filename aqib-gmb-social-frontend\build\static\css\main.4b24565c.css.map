{"version": 3, "file": "static/css/main.4b24565c.css", "mappings": "AASA,YAJE,uCAUF,CANA,UAEE,QAIF,CAEA,KACE,uCACF,CAGA,aAOE,kBAAmB,CAHnB,0BAA2B,CAF3B,WAAY,CAIZ,sBAAuB,CAHvB,iBAAkB,CAFlB,UAAW,CAIX,aAGF,CAEA,yBAEE,WAAY,CAEZ,QAAS,CAGT,iBAAkB,CADlB,gBAAiB,CAHjB,iBAAkB,CAElB,OAAQ,CAJR,UAOF,CAEA,MACE,iBAAqB,CACrB,iBAAkB,CAqBlB,sBAAuB,CACvB,6BAA8B,CAC9B,wBAAyB,CACzB,+BAAgC,CAEhC,oBAAqB,CACrB,8BAA+B,CAC/B,qBAAsB,CACtB,qBAAsB,CACtB,4BAA6B,CAC7B,8BAA+B,CAC/B,mBAAoB,CAEpB,uBAAwB,CACxB,qBAAsB,CACtB,yBAA0B,CAC1B,sBAAuB,CACvB,kBAAmB,CACnB,kBAAmB,CACnB,2BAA4B,CAC5B,2BAA4B,CAC5B,gBAAiB,CACjB,wBAAyB,CACzB,iBAAkB,CAClB,kBAAmB,CACnB,2BAA4B,CAC5B,gBACF,CAEA,UAEE,WAAY,CAEZ,eACF,CAEA,KACE,kCAA2C,CAA3C,yCACF,CAEA,WACE,WACF,CAEA,UACE,UACF,CAEA,aACE,SACF,CAEA,SACE,iBACF,CAEA,aACE,SACF,CAEA,aACE,YAGF,CAEA,QACE,WACF,CAEA,WACE,YAAa,CACb,mBACF,CAEA,MACE,gBACF,CAEA,MACE,mBACF,CAGA,SACE,qBAAmC,CAAnC,kCACF,CAEA,OACE,wBAAgC,CAAhC,+BAAgC,CAChC,wBAAyB,CACzB,2BACF,CAEA,gBACE,qBAAmC,CAAnC,kCAAmC,CACnC,YACF,CAEA,YACE,kBAAmB,CACnB,iCACF,CAEA,gBAME,kBAAmB,CAEnB,wBAAqC,CAArC,oCAAqC,CADrC,iBAAkB,CAElB,UAAwB,CAAxB,uBAAwB,CALxB,YAAa,CAFb,WAAY,CAGZ,sBAAuB,CAFvB,eAAwB,CAFxB,UASF,CAEA,0BACE,wBAAuC,CAAvC,sCAAuC,CAIvC,kBAAsB,CAFtB,2BAA6B,CAD7B,UAAwB,CAAxB,uBAAwB,CAIxB,eAAgB,CAFhB,YAAe,CAGf,yBACF,CAEA,gBACE,kCAA4C,CAA5C,0CAA4C,CAG5C,2BAA6B,CAF7B,yBAA2B,CAI3B,wBAA0B,CAH1B,2BAA6B,CAE7B,mCAEF,CAEA,qBACE,+BAA8C,CAA9C,4CAA8C,CAC9C,kCAA+C,CAA/C,6CAA+C,CAG/C,2BAA6B,CAF7B,uBAA2C,CAA3C,yCAA2C,CAC3C,sBAAwB,CAExB,yBACF,CAEA,kBACE,wBAAuC,CAAvC,sCACF,CAEA,YACE,+BAA8C,CAA9C,4CAA8C,CAQ9C,QAAW,CAPX,2BAA6B,CAM7B,2BAA6B,CAD7B,8BAA2C,CAJ3C,YAOF,CAEA,uBACE,mBACF,CAEA,WAKE,kBAAmB,CAFnB,aAAwB,CAAxB,uBAAwB,CAFxB,wBAA0B,CAC1B,yBAA2B,CAI3B,QACF,CAMA,kCACE,YACF,CAEA,8BACE,UACF,CAGA,cAEE,yBAA2B,CAC3B,uBAAmC,CAAnC,iCAAmC,CAFnC,YAGF,CAEA,eAWE,wBAAyB,CAVzB,YAAa,CAQb,mBAAoB,CACpB,iBAAkB,CALlB,iBAAkB,CAElB,OAAU,CACV,UAAW,CAFX,SAMF,CAEA,cAEE,iBAAkB,CADlB,iBAEF,CAEA,kBAME,kBAAmB,CAHnB,0BAA2B,CAK3B,kBAAmB,CADnB,aAA0B,CAA1B,yBAA0B,CAH1B,YAAa,CAFb,WAAY,CAGZ,sBAAuB,CAJvB,UAQF,CAEA,2BACE,uBAAgC,CAAhC,8BACF,CAEA,mBAKE,kBAAmB,CAGnB,wBAAqC,CAArC,oCAAqC,CAIrC,qBAAyB,CANzB,kBAAmB,CACnB,UAAwB,CAAxB,uBAAwB,CAJxB,YAAa,CAUb,cAAe,CAXf,WAAY,CAEZ,sBAAuB,CAKvB,iBAAkB,CAElB,UAAW,CADX,QAAS,CATT,UAaF,CAEA,aAEE,8BAAwC,CAAxC,uCAAwC,CACxC,aAAwB,CAAxB,uBAAwB,CAFxB,2BAGF,CAEA,uBACE,eACF,CASA,YAIE,qBAAmC,CAAnC,kCAAmC,CADnC,kBAAmB,CADnB,WAAY,CADZ,UAIF,CAGA,SACE,aAAsB,CAAtB,qBACF,CAEA,WACE,aAAsB,CAAtB,qBACF,CAIA,gBACE,wBAAqC,CAArC,oCAOF,CAEA,+BAHE,iBAAkB,CALlB,UAAwB,CAAxB,uBAAwB,CACxB,wBAA0B,CAC1B,yBAA2B,CAE3B,cAAiB,CADjB,yBAAkB,CAAlB,iBAaF,CARA,eACE,wBAAuC,CAAvC,sCAOF,CAEA,kBACE,wBAAiC,CAAjC,gCAAiC,CAMjC,iBAAkB,CAJlB,wBAA0B,CAC1B,yBAA2B,CAE3B,cAAiB,CADjB,yBAAkB,CAAlB,iBAGF,CAEA,4BARE,UAAwB,CAAxB,uBAoBF,CAZA,UAIE,wBAAuC,CAAvC,sCAAuC,CAOvC,cAAe,CALf,eAAgB,CAIhB,SAAY,CATZ,iBAAkB,CAElB,WAAY,CAMZ,iBAAkB,CAPlB,QAAS,CAKT,uBAAwB,CACxB,WAIF,CAGA,UACE,kCAA4C,CAA5C,0CAA4C,CAC5C,oBAAmC,CAAnC,iCACF,CAGA,aACE,kBACF,CAEA,kCAGE,wBAA6B,CAF7B,2BAA6B,CAC7B,WAEF,CAEA,aAKE,kBAAmB,CAHnB,wBAAiC,CAAjC,gCAAiC,CACjC,iBAAkB,CAClB,YAAa,CAHb,WAAY,CAMZ,eAAgB,CADhB,kBAEF,CAEA,iBACE,aAAuB,CAAvB,sBACF,CAEA,kCACE,wBACF,CAMA,mBAEE,kBAAmB,CADnB,YAEF,CAEA,eAKE,wBAAkC,CAAlC,iCAAkC,CADlC,iBAAkB,CAHlB,aAAc,CAKd,UAAW,CAHX,WAAY,CAIZ,gBAAiB,CALjB,UAMF,CAEA,qBACE,wBAAqC,CAArC,oCACF,CAEA,uBACE,wBAAiC,CAAjC,gCACF,CAEA,oBACE,wBAAuC,CAAvC,sCACF,CAGA,mBAGE,kBAAmB,CAFnB,YAAa,CACb,6BAA8B,CAE9B,aACF,CAEA,iCACE,eACF,CAEA,uBACE,YACF,CAEA,mBACE,qBAAsB,CAKtB,gBAAiB,CAHjB,QAAS,CACT,SAGF,CAEA,sBACE,qBAAsB,CACtB,WACF,CAEA,4CAEE,YAAa,CACb,eACF,CAEA,aAEE,qBAAmC,CAAnC,kCAAmC,CAKnC,QAAW,CADX,iBAAkB,CADlB,2BAAkD,CAJlD,aAOF,CAEA,sBAKE,wBAAoC,CAApC,mCAAoC,CADpC,UAAwB,CAAxB,uBAAwB,CAExB,eAAgB,CALhB,uBAAgB,CAAhB,eAAgB,CAChB,KAAM,CACN,SAIF,CAEA,mBACE,wBAAyB,CACzB,UACF,CAEA,gCAEE,gBACF,CAEA,gBACE,eACF,CAEA,UAEE,kBAAsB,CADtB,eAAgB,CAKhB,iBAAkB,CADlB,eAAgB,CADhB,cAAe,CADf,UAIF,CAEA,gBACE,4BACF,CAEA,+BAEE,aAAgB,CADhB,cAEF,CAEA,kBAGE,aAAgC,CAAhC,+BAAgC,CAFhC,wBAA0B,CAC1B,yBAEF,CAEA,oBAGE,aAAuB,CAAvB,sBAAuB,CAFvB,wBAA0B,CAC1B,yBAEF,CAEA,gBACE,kCAAkD,CAAlD,gDAWF,CAEA,wCAHE,wBAAoC,CAApC,mCAAoC,CAJpC,2BAA6B,CAL7B,oBAAmC,CAAnC,iCAAmC,CAQnC,cAAe,CADf,eAAgB,CANhB,WAAY,CAGZ,gBAAiB,CAFjB,eAAgB,CAChB,gBAAiB,CAGjB,yBAkBF,CAZA,wBACE,oCAAuD,CAAvD,qDAWF,CAEA,eACE,YACF,CAEA,mBACE,kBACF,CAEA,oCACE,mBAEE,wBAAgC,CAAhC,+BAAgC,CADhC,QAEF,CAEA,yBACE,YACF,CAEA,sBAIE,qBAAmC,CAAnC,kCAAmC,CADnC,4BAA6B,CAD7B,aAAc,CADd,kBAIF,CAEA,sBAIE,6BAA8B,CAH9B,aAAc,CAEd,cAAe,CADf,gBAGF,CAEA,iCACE,eACF,CAEA,6BACE,wBAAyB,CACzB,UAAW,CAEX,eAAiB,CADjB,wBAEF,CACF,CAEA,wBACE,yBACF,CAEA,cACE,mBAAsB,CACtB,yBACF,CAGA,UACE,kCAAgD,CAAhD,8CAEF,CAEA,mBAHE,yBASF,CANA,SACE,kCAAiD,CAAjD,+CAAiD,CACjD,wBAAoC,CAApC,mCAAoC,CACpC,2BAA6B,CAC7B,eAEF,CAMA,8CAHE,2BAOF,CAJA,wBAGE,uCAAyC,CADzC,wCAEF,CAGA,QACE,kCAA+C,CAA/C,6CAA+C,CAG/C,2BAA6B,CAD7B,yBAA2B,CAG3B,eAAgB,CADhB,yBAEF,CAEA,oBAPE,uBAYF,CALA,YACE,sCAAwC,CACxC,wBAA0B,CAC1B,yBAA2B,CAC3B,gBACF,CAEA,UACE,2BACF,CAEA,cACE,aACF,CAEA,kBACE,gBACF,CAQA,YACE,YAAa,CACb,6BACF,CAEA,cACE,YACF,CAGA,aACE,WACF,CAEA,gCAEE,wBAAyB,CADzB,gBAEF,CAEA,sCAEE,yBAA0B,CAC1B,kBAAmB,CAFnB,iBAGF,CAEA,0BACE,aAAwB,CAAxB,uBAAwB,CAExB,wBAA0B,CAD1B,yBAEF,CAEA,8BACE,cAAe,CACf,gBACF,CAEA,2BAGE,4BAA6B,CAF7B,YAAa,CACb,6BAA8B,CAE9B,YACF,CAEA,gDAEE,gBAAiB,CADjB,SAEF,CAEA,4BACE,wBAA0B,CAE1B,eAAgB,CADhB,2BAEF,CAOA,8CAHE,aAAuB,CAAvB,sBAAuB,CADvB,cASF,CALA,iCAIE,eAAgB,CADhB,gBAEF,CAEA,iCAEE,aAAwB,CAAxB,uBAAwB,CADxB,cAAe,CAEf,gBACF,CAEA,iCACE,iBACF,CAKA,oBAEE,UAAW,CADX,SAEF,CAEA,0BAEE,kBAAmB,CADnB,gCAEF,CAEA,0BACE,kBAAmB,CACnB,kBACF,CAEA,gCACE,kBACF,CAGA,OACE,sBACF,CAEA,QACE,kBACF,CAEA,MACE,cACF,CAEA,OACE,yBACF,CAEA,kBAEE,oBAAqB,CACrB,2BAA4B,CAK5B,oBAAqB,CAPrB,mBAAoB,CAKpB,UAAW,CACX,iBAAkB,CAHlB,eAAgB,CAKhB,kBAAmB,CAJnB,sBAKF,CAGA,oBAEE,WAAY,CADZ,iBAEF,CAEA,mBAGE,WAAY,CACZ,kBAAmB,CAFnB,YAGF,CAEA,wBACE,uBAAiC,CAAjC,+BAAiC,CACjC,eAEF,CAEA,0BACE,uBAAiC,CAAjC,+BAAiC,CACjC,eACF,CAEA,oBAIE,0BAAwC,CAAxC,uCAAwC,CAFxC,iBAAkB,CAGlB,aAAsB,CAAtB,qBAAsB,CAFtB,cAAe,CAFf,WAKF,CAEA,oBAME,kBAAmB,CAHnB,kBAAmB,CACnB,YAAa,CAGb,cAAe,CACf,eAAgB,CANhB,WAAY,CAGZ,sBAAuB,CAJvB,UAQF,CAEA,+BACE,0BAAwC,CAAxC,uCAAwC,CACxC,aAAsB,CAAtB,qBACF,CAEA,iCACE,0BAAwC,CAAxC,uCAAwC,CACxC,aAAsB,CAAtB,qBACF,CAEA,KACE,QACF,CAEA,cAOE,oBAAqB,CACrB,2BAA4B,CAF5B,mBAAoB,CAJpB,YAAa,CACb,gBAAiB,CACjB,eAAgB,CAChB,sBAAuB,CAJvB,UAAW,CAQX,qBACF,CAEA,aACE,kBACF,CAEA,iBASE,eAA0B,CAF1B,kBAAmB,CALnB,uCAA4C,CAE5C,iBAAmB,CADnB,eAAgB,CAGhB,uBAAyB,CADzB,gBAAiB,CAJjB,iBAAkB,CAOlB,eAEF,CAEA,UAME,kBAAmB,CACnB,kBAAmB,CAJnB,YAAa,CADb,WAAY,CAGZ,sBAAuB,CADvB,iBAAkB,CAHlB,UAOF,CAEA,cACE,cACF,CAGA,eACE,0BAA2B,CAC3B,aACF,CAEA,gBACE,0BAA2B,CAC3B,aACF,CAEA,cACE,0BAA2B,CAC3B,aACF,CAEA,gBACE,0BAA2B,CAC3B,aACF,CAEA,gBACE,0BAA2B,CAC3B,aACF,CAEA,WACE,cAAe,CACf,eACF,CAEA,eAGE,0BACF,CAEA,0BAJE,kBAAmB,CADnB,YAYF,CAPA,WAKE,WAAY,CAJZ,cAAe,CACf,eAAgB,CAEhB,mBAGF,CAEA,gBACE,YAAa,CACb,kBAAmB,CACnB,mBACF,CAEA,kCAEE,0BAA4C,CAA5C,2CAA4C,CAC5C,aAA4B,CAA5B,2BAA4B,CAC5B,eACF,CAEA,4BAEE,wBAA6B,CAC7B,aAA4B,CAA5B,2BAA4B,CAC5B,eACF,CAEA,cACE,gBACF,CAEA,qBAIE,wBAAuC,CAAvC,sCAAuC,CAHvC,UAAW,CAIX,WAAY,CACZ,UAAW,CAJX,iBAAkB,CAClB,SAIF,CAMA,oCAHE,kBAQF,CALA,WAEE,uBAAyB,CADzB,wBAA0B,CAE1B,yBAEF,CAEA,sBAEE,kBAAmB,CADnB,YAEF,CAEA,gBAIE,cAAe,CACf,yBACF,CAEA,iCANE,2BAA6B,CAD7B,eAAgB,CAEhB,yBAWF,CANA,iBAIE,cAAe,CACf,yBACF,CAGA,YAEE,eAAgB,CADhB,gBAEF,CAEA,eACE,qBAAyB,CACzB,wBAAoC,CAApC,mCACF,CAEA,QACE,eACF,CAEA,WACE,YAAa,CACb,mBACF,CAEA,gBAEE,kBAAmB,CADnB,YAAa,CAEb,gBACF,CAEA,QACE,iBACF,CAEA,aACE,uBAAuC,CAAvC,qCACF,CAEA,aACE,uBAAqC,CAArC,mCACF,CAEA,qBACE,yBAA4C,CAA5C,0CACF,CAEA,eACE,uBAA+B,CAA/B,6BACF,CAEA,uBACE,yBAAwC,CAAxC,sCACF,CAEA,MACE,QACF,CAEA,sBACE,YACF,CAEA,0BAEE,UAAW,CACX,kBAAmB,CAFnB,SAGF,CAEA,cACE,cAAe,CACf,eACF,CAEA,kBAEE,0BAAqC,CADrC,eAEF,CAMA,2BACE,eACF,CAEA,kBACE,2EACF,CAEA,kBACE,+BACF,CAEA,gBACE,YACF,CAGA,yBACE,WACE,wBACF,CAEA,gBACE,YACF,CAEA,gBACE,aACF,CAEA,uBACE,wBACF,CAEA,iBAEE,cAAe,CADf,kBAEF,CAEA,uBACE,YAAa,CACb,qBACF,CAEA,4BACE,uBACF,CAEA,cAEE,cAAe,CADf,WAEF,CAEA,KACE,sBACF,CAEA,+BAME,kBAAmB,CAHnB,kBAAmB,CACnB,YAAa,CAFb,WAAY,CAGZ,sBAAuB,CAJvB,UAMF,CAEA,mCAEE,QAAS,CADT,iBAEF,CAEA,qBACE,WAAY,CACZ,kBACF,CAEA,0BACE,QACF,CAEA,uCAEE,cAAe,CADf,YAEF,CAEA,YAEE,cAAe,CACf,cAAe,CAFf,WAGF,CAIA,gCACE,cACF,CACF,CCjrCA,WAGE,iBAAkB,CAFlB,kBAAqB,CACrB,iBAAkB,CAElB,eAAgB,CAChB,uLAAmI,CACnI,0JACF,CAGA,WAGE,iBAAkB,CAFlB,kBAAqB,CACrB,iBAAkB,CAElB,eAAgB,CAChB,qLAAiI,CACjI,gMACF,CAGA,WAGE,iBAAkB,CAFlB,kBAAqB,CACrB,iBAAkB,CAElB,eAAgB,CAChB,6KAAyH,CACzH,iKACF,CC5BA,UACE,aAAc,CACd,mBACF,CAEA,8CACE,UACE,2CACF,CACF,CAEA,YAKE,kBAAmB,CAJnB,wBAAyB,CAOzB,UAAY,CALZ,YAAa,CACb,qBAAsB,CAGtB,4BAA6B,CAD7B,sBAAuB,CAJvB,gBAOF,CAEA,UACE,aACF,CAKA,yBACE,GACE,sBACF,CACA,GACE,uBACF,CACF,CCpCA,aAMI,kBAAmB,CAJnB,oEAAqD,CAOrD,uBAA2B,CAC3B,2BAA4B,CAF5B,qBAAsB,CADtB,aAIJ,CACA,2BARI,YAAa,CADb,WAAY,CAEZ,sBAeJ,CARA,cAOI,qBAAsB,CAJtB,YAAe,CACf,eAIJ,CACA,aAGI,kBAAmB,CADnB,WAEJ,CACA,cAII,aAAc,CAFd,cAAe,CACf,eAAgB,CAEhB,kBACJ,CACA,uDAGI,kBACJ,CACA,2BAQI,kBAAmB,CALnB,wBAAyB,CACzB,iBAAkB,CAElB,YAAa,CACb,sBAAuB,CALvB,iBAAkB,CAGlB,cAIJ,CACA,8BAGI,wBAA0B,CAE1B,eAAgB,CAHhB,cAAe,CAEf,sBAEJ,CC3CA,kBAKI,iBAEJ,CClBA,iBAEI,iBACJ,CACA,oCAEI,YAAa,CACb,kBAAmB,CACnB,6BACJ,CCTA,6BAII,QAAW,CAFX,eAAgB,CAGhB,iBAAkB,CAClB,iBAAkB,CAHlB,WAIJ,CACA,eAGI,WAAY,CADZ,UAEJ,CACA,YAEI,aACJ,CACA,cAEI,aACJ,CACA,cAEI,aACJ,CACA,WAEI,aACJ,CACA,wBAEI,wBAA0B,CAE1B,kBAAmB,CADnB,yBAA2B,CAE3B,mBACJ,CACA,iCAEI,uBACJ,CACA,aAEI,gCACJ,CCXA,4CACI,yBAA0B,CAC1B,eACJ,CACA,YAEI,YAAa,CACb,mBACJ,CCxCA,sBAGI,iBAAkB,CAFlB,iBAAkB,CAClB,UAEJ,CAEA,0BACI,UACJ,CAEA,sCAII,qBAAyB,CAGzB,kBAAmB,CADnB,WAAY,CALZ,iBAAkB,CAElB,UAAW,CADX,QAAS,CAGT,UAGJ,CClBA,MACI,4BACJ,CAEA,YACI,qBACJ,CAEA,eAEI,2BAA4B,CAG5B,oBAAqB,CAErB,8BAA+B,CAN/B,mBAAoB,CAKpB,WAAY,CAHZ,eAAgB,CAChB,sBAIJ,CAEA,sBAII,aAAc,CADd,iBAAkB,CADlB,eAAgB,CADhB,WAIJ,CAEA,iBACI,WACJ,CAEA,+BACI,WAAY,CAEZ,kBAAmB,CADnB,gBAEJ,CAEA,4CACI,yBAA0B,CAC1B,kBACJ,CAEA,YAEI,YAAa,CADb,iBAAkB,CAGlB,gBAAqB,CADrB,kBAEJ,CAEA,sBACI,0BACJ,CCjDA,sBAGI,0BAA2B,CAC3B,kBAAmB,CACnB,WAAY,CACZ,aAAc,CAJd,eAAgB,CAKhB,sBAAuB,CACvB,iBACJ,CACA,0CAGI,WAAY,CAIZ,MAAO,CADP,aAAc,CAFd,iBAAkB,CAIlB,OAAQ,CAHR,SAAU,CAHV,UAOJ,CACA,wCAEI,aAAc,CACd,cAAe,CACf,kBACJ,CCzBA,4BAEI,mBACJ,CACA,gBAEI,2BACJ,CACA,aAGI,YAAa,CACb,gBAAiB,CAFjB,WAGJ,CAMA,sCAGI,WAAY,CADZ,UAEJ,CACA,mBAQI,kBAAmB,CAFnB,YAAa,CAJb,YAAa,CAKb,sBAAuB,CAHvB,aAAc,CADd,gBAAiB,CAEjB,iBAIJ,CACA,oBAEI,eACJ,CACA,gBAGI,WAAY,CACZ,gBAAiB,CAFjB,UAGJ,CC3CA,sBAEI,0BAA2B,CAI3B,kBAAmB,CAGnB,gCAA+C,CAL/C,aAAc,CADd,eAAgB,CAEhB,YAAa,CAKb,iBACJ,CACA,0CAGI,WAAY,CACZ,iBAAkB,CAFlB,UAGJ,CAKA,0CAEI,aAAc,CACd,cAAe,CACf,iBAAkB,CAClB,eAAgB,CAChB,kBACJ,CACA,iDAEI,uBAAyB,CACzB,yBACJ,CACA,0CAEI,YAAa,CACb,eACJ,CACA,kCAEI,cACJ,CACA,iCAEI,iBAAkB,CAElB,UAAW,CADX,SAEJ,CACA,qCAEI,UACJ,CCrDA,qBAKI,qBAAyB,CACzB,qBAAyB,CAFzB,kBAAmB,CADnB,WAAY,CAIZ,gBAAiB,CALjB,UAMJ,CACA,aAGI,QAAW,CACX,kBAAmB,CAFnB,iCAGJ,CACA,+BAII,kBAAmB,CAFnB,YAAa,CACb,6BAEJ,CCpBA,sBAEI,4BAA8B,CAD9B,YAEJ,CAEA,kBAGI,kBAAmB,CAFnB,YAAa,CACb,sBAEJ,CAEA,kBACI,YAAa,CACb,qBAAsB,CACtB,WAAY,CAEZ,sBAAuB,CADvB,iBAEJ,CAQA,0BAEI,WAAY,CACZ,kBAAmB,CAFnB,SAGJ,CAEA,aAEI,WAAY,CACZ,kBAAmB,CAFnB,SAGJ,CAEA,mBACI,cAAe,CACf,eACJ,CAEA,mBACI,aAAc,CACd,aACJ,CAEA,oBACI,aAAc,CAEd,cAAe,CADf,eAEJ,CAEA,4DAQI,kBAAmB,CAHnB,uCAAwC,CADxC,kBAAmB,CAOnB,qBAAsB,CALtB,YAAa,CAGb,UAAW,CANX,WAAY,CAIZ,sBAAuB,CAGvB,gBAAiB,CARjB,UAUJ,CAEA,8DAEI,UACJ,CAEA,8BACI,uCAAwC,CACxC,qBACJ,CAEA,+BACI,WACJ,CAEA,eAGI,kBAAmB,CADnB,YAAa,CADb,UAIJ,CCpFA,yBACE,YACF,CAEA,mBACE,kBACF,CAEA,kBAEE,iBAAkB,CADlB,WAAY,CAEZ,iBACF,CAEA,aACE,sBAAuB,CACvB,iBAAkB,CAGlB,cAAe,CAEf,kBAAmB,CAJnB,YAAa,CACb,iBAAkB,CAElB,uBAEF,CAEA,mBAEE,wBAAyB,CADzB,oBAEF,CAEA,sBAEE,wBAAyB,CADzB,oBAEF,CAEA,qBAGE,kBAAmB,CAFnB,YAAa,CACb,qBAAsB,CAEtB,QACF,CAEA,aAEE,aAAc,CADd,wBAEF,CAEA,aACE,UAAW,CACX,QACF,CAEA,eACE,yBACF,CAEA,aACE,eACF,CAEA,YAGE,cAAe,CADf,YAAa,CAGb,eAAgB,CAJhB,iBAAkB,CAGlB,iDAEF,CAEA,kBAEE,+BAA0C,CAD1C,0BAEF,CAEA,kBAME,kBAAmB,CAFnB,wBAAyB,CACzB,YAAa,CAHb,YAAa,CAKb,sBAAuB,CAJvB,gBAAiB,CAFjB,UAOF,CAQA,8CAJE,WAAY,CACZ,gBAAiB,CAFjB,UASF,CAEA,mBAGE,kBAAmB,CAGnB,wBAAyB,CACzB,UAAW,CANX,YAAa,CACb,qBAAsB,CAGtB,WAAY,CADZ,sBAIF,CAEA,wBACE,wBAA0B,CAC1B,iBACF,CAEA,oBAGE,YAAa,CACb,qBAAsB,CAFtB,WAAY,CAGZ,6BAA8B,CAJ9B,0BAKF,CAEA,gBACE,iBAAmB,CACnB,eAAgB,CAIhB,iBAAkB,CAFlB,eAAgB,CAChB,sBAAuB,CAFvB,kBAIF,CAEA,YAGE,kBAAmB,CAFnB,YAAa,CACb,6BAEF,CAEA,YAEE,UAAW,CADX,gBAEF,CAEA,iBACE,2BAA8B,CAC9B,qBACF,CAEA,eAIE,YAAa,CACb,OAAQ,CACR,SAAU,CALV,iBAAkB,CAElB,SAAU,CADV,OAAQ,CAKR,2BACF,CAEA,iCACE,SACF,CAEA,qBACE,oCAAqD,CACrD,oBAAsB,CAGtB,qBAAuB,CAFvB,wBAA0B,CAG1B,mBAAqB,CAFrB,oBAGF,CAEA,2BACE,+BACF,CAEA,eACE,uBACF,CAEA,qBACE,oCACF,CAEA,WAGE,UAAW,CADX,YAAa,CADb,iBAGF,CAEA,gBAEE,UAAW,CADX,wBAA0B,CAE1B,kBACF,CAEA,sBACE,YAAa,CACb,sBAAuB,CACvB,eACF,CAEA,oBACE,kBACF,CAEA,iBAEE,2BAA6B,CAD7B,2BAEF,CAEA,iBAQE,kBAAmB,CAFnB,sBAA0C,CAD1C,QAAS,CAET,YAAa,CAEb,sBAAuB,CANvB,MAAO,CAFP,iBAAkB,CAGlB,OAAQ,CAFR,KAAM,CAQN,SACF,CAEA,YACE,YACF,CAEA,iBACE,eACF,CAEA,iBACE,kBACF,CAGA,yBACE,YACE,YACF,CAEA,kBACE,YACF,CAEA,aACE,YACF,CAEA,aACE,wBACF,CACF,CAEA,yBACE,YACE,YACF,CAEA,kBACE,YACF,CAEA,aACE,YACF,CAEA,aACE,wBACF,CAEA,gBACE,gBACF,CACF,CC7QA,2BAGE,kBAAmB,CAGnB,wBAA6B,CAL7B,YAAa,CAGb,YAAa,CAFb,sBAAuB,CAOvB,MAAO,CAFP,cAAe,CACf,KAAM,CAHN,WAAY,CAKZ,YACF,CAEA,eAGE,iCAAkC,CAClC,4CAAiD,CAFjD,WAAY,CADZ,UAIF,CAGA,gBACE,GACE,uBACF,CAEA,IACE,yBACF,CAEA,GACE,wBACF,CACF,CAGA,yBACE,eAEE,WAAY,CADZ,UAEF,CACF,CAEA,yBACE,eAEE,WAAY,CADZ,UAEF,CACF,CChDA,WAGE,iBAAkB,CAFlB,kBAAqB,CACrB,iBAAkB,CAElB,eAAgB,CAChB,2LAAuI,CACvI,gFACF,CAGA,WAGE,iBAAkB,CAFlB,kBAAqB,CACrB,iBAAkB,CAElB,eAAgB,CAChB,mLAA+H,CAC/H,+DACF,CAGA,WAGE,iBAAkB,CAFlB,kBAAqB,CACrB,iBAAkB,CAElB,eAAgB,CAChB,qLAAiI,CACjI,oBACF,CAGA,WAGE,iBAAkB,CAFlB,kBAAqB,CACrB,iBAAkB,CAElB,eAAgB,CAChB,6KAAyH,CACzH,gFACF,CAGA,WAGE,iBAAkB,CAFlB,kBAAqB,CACrB,iBAAkB,CAElB,eAAgB,CAChB,2KAAuH,CACvH,iwBACF,CAGA,WAGE,iBAAkB,CAFlB,kBAAqB,CACrB,iBAAkB,CAElB,eAAgB,CAChB,iLAA6H,CAC7H,q4CACF,CAGA,WAGE,iBAAkB,CAFlB,kBAAqB,CACrB,iBAAkB,CAElB,eAAgB,CAChB,uLAAmI,CACnI,0JACF,CAGA,WAGE,iBAAkB,CAFlB,kBAAqB,CACrB,iBAAkB,CAElB,eAAgB,CAChB,qLAAiI,CACjI,gMACF,CAGA,WAGE,iBAAkB,CAFlB,kBAAqB,CACrB,iBAAkB,CAElB,eAAgB,CAChB,6KAAyH,CACzH,iKACF,CCvFA,WAGE,iBAAkB,CAFlB,kBAAqB,CACrB,iBAAkB,CAElB,eAAgB,CAChB,2LAAuI,CACvI,gFACF,CAGA,WAGE,iBAAkB,CAFlB,kBAAqB,CACrB,iBAAkB,CAElB,eAAgB,CAChB,mLAA+H,CAC/H,+DACF,CAGA,WAGE,iBAAkB,CAFlB,kBAAqB,CACrB,iBAAkB,CAElB,eAAgB,CAChB,qLAAiI,CACjI,oBACF,CAGA,WAGE,iBAAkB,CAFlB,kBAAqB,CACrB,iBAAkB,CAElB,eAAgB,CAChB,6KAAyH,CACzH,gFACF,CAGA,WAGE,iBAAkB,CAFlB,kBAAqB,CACrB,iBAAkB,CAElB,eAAgB,CAChB,2KAAuH,CACvH,iwBACF,CAGA,WAGE,iBAAkB,CAFlB,kBAAqB,CACrB,iBAAkB,CAElB,eAAgB,CAChB,iLAA6H,CAC7H,q4CACF,CAGA,WAGE,iBAAkB,CAFlB,kBAAqB,CACrB,iBAAkB,CAElB,eAAgB,CAChB,uLAAmI,CACnI,0JACF,CAGA,WAGE,iBAAkB,CAFlB,kBAAqB,CACrB,iBAAkB,CAElB,eAAgB,CAChB,qLAAiI,CACjI,gMACF,CAGA,WAGE,iBAAkB,CAFlB,kBAAqB,CACrB,iBAAkB,CAElB,eAAgB,CAChB,6KAAyH,CACzH,iKACF,CCvFA,WAGE,iBAAkB,CAFlB,kBAAqB,CACrB,iBAAkB,CAElB,eAAgB,CAChB,2LAAuI,CACvI,gFACF,CAGA,WAGE,iBAAkB,CAFlB,kBAAqB,CACrB,iBAAkB,CAElB,eAAgB,CAChB,mLAA+H,CAC/H,+DACF,CAGA,WAGE,iBAAkB,CAFlB,kBAAqB,CACrB,iBAAkB,CAElB,eAAgB,CAChB,qLAAiI,CACjI,oBACF,CAGA,WAGE,iBAAkB,CAFlB,kBAAqB,CACrB,iBAAkB,CAElB,eAAgB,CAChB,6KAAyH,CACzH,gFACF,CAGA,WAGE,iBAAkB,CAFlB,kBAAqB,CACrB,iBAAkB,CAElB,eAAgB,CAChB,2KAAuH,CACvH,iwBACF,CAGA,WAGE,iBAAkB,CAFlB,kBAAqB,CACrB,iBAAkB,CAElB,eAAgB,CAChB,iLAA6H,CAC7H,q4CACF,CAGA,WAGE,iBAAkB,CAFlB,kBAAqB,CACrB,iBAAkB,CAElB,eAAgB,CAChB,uLAAmI,CACnI,0JACF,CAGA,WAGE,iBAAkB,CAFlB,kBAAqB,CACrB,iBAAkB,CAElB,eAAgB,CAChB,qLAAiI,CACjI,gMACF,CAGA,WAGE,iBAAkB,CAFlB,kBAAqB,CACrB,iBAAkB,CAElB,eAAgB,CAChB,6KAAyH,CACzH,iKACF,CCvFA,WAGE,iBAAkB,CAFlB,kBAAqB,CACrB,iBAAkB,CAElB,eAAgB,CAChB,2LAAuI,CACvI,gFACF,CAGA,WAGE,iBAAkB,CAFlB,kBAAqB,CACrB,iBAAkB,CAElB,eAAgB,CAChB,mLAA+H,CAC/H,+DACF,CAGA,WAGE,iBAAkB,CAFlB,kBAAqB,CACrB,iBAAkB,CAElB,eAAgB,CAChB,qLAAiI,CACjI,oBACF,CAGA,WAGE,iBAAkB,CAFlB,kBAAqB,CACrB,iBAAkB,CAElB,eAAgB,CAChB,6KAAyH,CACzH,gFACF,CAGA,WAGE,iBAAkB,CAFlB,kBAAqB,CACrB,iBAAkB,CAElB,eAAgB,CAChB,2KAAuH,CACvH,iwBACF,CAGA,WAGE,iBAAkB,CAFlB,kBAAqB,CACrB,iBAAkB,CAElB,eAAgB,CAChB,iLAA6H,CAC7H,q4CACF,CAGA,WAGE,iBAAkB,CAFlB,kBAAqB,CACrB,iBAAkB,CAElB,eAAgB,CAChB,uLAAmI,CACnI,0JACF,CAGA,WAGE,iBAAkB,CAFlB,kBAAqB,CACrB,iBAAkB,CAElB,eAAgB,CAChB,qLAAiI,CACjI,gMACF,CAGA,WAGE,iBAAkB,CAFlB,kBAAqB,CACrB,iBAAkB,CAElB,eAAgB,CAChB,6KAAyH,CACzH,iKACF", "sources": ["index.css", "../node_modules/@fontsource/barlow/index.css", "App.css", "screens/signIn/signIn.screen.style.css", "screens/dashboard/dashboard.screen.style.css", "screens/userManagement/roles/roles.screen.style.css", "components/alertDialog/alertDialog.component.style.css", "screens/qanda/qanda.screen.style.css", "screens/businessManagement/manageBusiness/manageBusiness.screen.style.css", "screens/reviewManagement/manageReviews/manageReviews.screen.style.css", "components/createPost/createPostTemplates/cards/testimonialCard4/testimonialCard4.component.style.css", "components/createPost/createPost.component.style.css", "components/createPost/createPostTemplates/cards/testimonialCard6/testimonialCard6.component.style.css", "screens/businessManagement/businessSummary/businessSummary.screen.style.css", "screens/dashboardV2/dashboardV2.screen.style.css", "screens/manageAssets/manageAssets.screen.style.css", "components/loader/loader.component.css", "../node_modules/@fontsource/roboto/300.css", "../node_modules/@fontsource/roboto/400.css", "../node_modules/@fontsource/roboto/500.css", "../node_modules/@fontsource/roboto/700.css"], "sourcesContent": ["/* @import url('https://fonts.googleapis.com/css2?family=Barlow:wght@400;500;700&display=swap'); */\n/* @import url('https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap\" rel=\"stylesheet'); */\n\n\n* {\n  font-family: 'Barlow', sans-serif !important;\n}\n\n\nhtml,\nbody {\n  margin: 0;\n  font-family: '<PERSON>', sans-serif !important;\n  /* -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale; */\n}\n\ncode {\n  font-family: 'Barlow', sans-serif !important;\n}\n\n/*Page Loader Css Starts Here*/\n.page_loader {\n  width: 100%;\n  height: 100%;\n  position: absolute;\n  background-color: #21212160;\n  z-index: 99999;\n  justify-content: center;\n  align-items: center;\n}\n\n.page_loader .loaderIcon {\n  width: 50px;\n  height: 50px;\n  position: absolute;\n  left: 50%;\n  top: 50%;\n  margin-top: -25px;\n  margin-left: -25px;\n}\n\n:root {\n  --whiteColor: #ffffff;\n  --bgColor: #f4f4f4;\n  /* THEME--1 */\n  /* --primaryColor: #00B4E6;\n  --primaryColorAlpha: #3165B133;\n  --secondaryColor: #ff6e23;\n  --secondaryColorAlpha: #ff6e2333; */\n\n  /* THEME--2  */\n  /* --primaryColor: #00B4E6;\n  --primaryColorAlpha: #00B4E633;\n  --secondaryColor: #FFC700;\n  --secondaryColorAlpha: #FFC70033;  */\n\n  /* THEME--3  */\n\n  /* --primaryColor: #0C2A3F;\n  --primaryColorAlpha:#0C2A3F33 ;\n  --secondaryColor: #FF6E23;\n  --secondaryColorAlpha: #FF6E2333; */\n\n  /* THEME--4 */\n  --primaryColor: #309898;\n  --primaryColorAlpha: #30989833;\n  --secondaryColor: #f67e44;\n  --secondaryColorAlpha: #f67e4433;\n\n  --titleColor: #2F2F2F;\n  --btnColor: var(--primaryColor);\n  --grayBgColor: #EDEDED;\n  --borderColor: #EBEBEB;\n  --secondaryTextColor: #464255;\n  --tableHeader: var(--grayColor);\n  --grayColor: #757575;\n\n  --teritoryColor: #464255;\n  --fourthColor: #EBEBEB;\n  --fourthColorText: #524E60;\n  --notification: #FF5B5B;\n  --positive: #089D2B;\n  --negative: #CB1212;\n  --positiveOpacity: #089D2B36;\n  --negativeOpacity: #CB121236;\n  --rating: #E7B66B;\n  --ratingInactive: #757575;\n  --tabHead: #F5F6FA;\n  --searchBg: #FDFDFD;\n  --reviewDescription: #0D0C22;\n  --delete: #F24245;\n}\n\nhtml,\nbody {\n  height: 100%;\n  /* overflow-y: scroll; */\n  overflow-y: auto;\n}\n\nbody {\n  background-color: var(--bgColor) !important;\n}\n\n.height100 {\n  height: 100%;\n}\n\n.width100 {\n  width: 100%;\n}\n\n.navFullLogo {\n  width: 85%;\n}\n\n.navIcon {\n  text-align: center;\n}\n\n.navIcon img {\n  width: 70%;\n}\n\n.navLogoPart {\n  height: 120px;\n  /* margin-top: 20px; */\n  /* margin-bottom: 16px; */\n}\n\n.floatR {\n  float: right;\n}\n\n.columnEnd {\n  display: flex;\n  justify-content: end;\n}\n\n.lh42 {\n  line-height: 42px;\n}\n\n.pad0 {\n  padding: 0px !important;\n}\n\n\n.whiteBg {\n  background-color: var(--whiteColor);\n}\n\n.tabBg {\n  background-color: var(--tabHead);\n  border: 1px solid #d5d5d5;\n  border-radius: 14px 14px 0px 0px;\n}\n\n.commonFormPart {\n  background-color: var(--whiteColor);\n  padding: 16px;\n}\n\n.commonTabs {\n  border-radius: 14px;\n  box-shadow: 6px 6px 54px rgba(0, 0, 0, 0.05);\n}\n\n.commonTabsIcon {\n  width: 50px;\n  height: 50px;\n  margin: 0px 0px 12px 0px;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  border-radius: 5px;\n  background-color: var(--primaryColor);\n  color: var(--whiteColor);\n}\n\n.commonTabs .Mui-selected {\n  background-color: var(--secondaryColor);\n  color: var(--whiteColor);\n  border-radius: 8px !important;\n  margin: 7px 0px;\n  border: 0px !important;\n  font-weight: 600;\n  text-transform: capitalize;\n}\n\n.primaryFillBtn {\n  background-color: var(--btnColor) !important;\n  box-shadow: none !important;\n  padding: 17px 16px !important;\n  border-radius: 5px !important;\n  text-transform: capitalize !important;\n  font-size: 16px !important;\n}\n\n.secondaryOutlineBtn {\n  background-color: var(--whiteColor) !important;\n  border: 1px solid var(--borderColor) !important;\n  color: var(--secondaryTextColor) !important;\n  padding: 16px !important;\n  border-radius: 5px !important;\n  text-transform: capitalize;\n}\n\n.secondaryFillBtn {\n  background-color: var(--secondaryColor);\n}\n\n.commonCard {\n  background-color: var(--whiteColor) !important;\n  border-radius: 4px !important;\n  padding: 24px;\n  /* box-shadow: 0px 4px 4px -4px rgba(0, 0, 0, 0.75);\n  -webkit-box-shadow: 0px 4px 4px -4px rgba(0, 0, 0, 0.75);\n  -moz-box-shadow: 0px 4px 4px -4px rgba(0, 0, 0, 0.75); */\n  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.05);\n  border-radius: 5px !important;\n  border: 0px;\n}\n\n.commonCardDescription {\n  padding-bottom: 20px;\n}\n\n.pageTitle {\n  font-size: 32px !important;\n  font-weight: 600 !important;\n  color: var(--titleColor);\n  display: flex;\n  align-items: center;\n  margin: 0px;\n}\n\n.dashboardCard {\n  display: flex;\n}\n\n.d-flex {\n  display: flex;\n}\n\n.dashboardCard .dashboardIcon {\n  width: 85px;\n}\n\n/*Common Topbar Css*/\n.commonTopBar {\n  display: flex;\n  box-shadow: none !important;\n  color: var(--titleColor) !important;\n}\n\n.commonTopBarR {\n  display: flex;\n  /* justify-content: end;\n  flex-grow: 1;\n  align-items: center; */\n  position: absolute;\n  z-index: 9;\n  right: 0px;\n  width: 100%;\n  justify-content: end;\n  padding: 10px 12px;\n  background-color: #f4f4f4;\n}\n\n.notification {\n  position: relative;\n  margin-right: 32px;\n}\n\n.notificationIcon {\n  width: 40px;\n  height: 40px;\n  background-color: #ffffff6e;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  color: var(--notification);\n  border-radius: 12px;\n}\n\n.notificationIcon .iconBtn {\n  color: var(--bgColor) !important;\n}\n\n.notificationValue {\n  width: 24px;\n  height: 24px;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  border-radius: 20px;\n  color: var(--whiteColor);\n  background-color: var(--notification);\n  position: absolute;\n  top: -8px;\n  right: -8px;\n  border: 2px solid #ffffff;\n  font-size: 12px;\n}\n\n.profileName {\n  padding: 12px 12px 12px 18px;\n  border-right: 1px solid var(--grayColor);\n  color: var(--titleColor);\n}\n\n.profileName .userName {\n  font-weight: 600;\n}\n\n.profilePic {\n  width: 40px;\n  height: 40px;\n  border-radius: 20px;\n  border: 2px solid var(--whiteColor);\n}\n\n.profilePic {\n  width: 40px;\n  height: 40px;\n  border-radius: 20px;\n  border: 2px solid var(--whiteColor);\n}\n\n/*Status Css*/\n.success {\n  color: var(--positive);\n}\n\n.cancelled {\n  color: var(--negative);\n}\n\n/*Status Css*/\n/*Badges Css*/\n.badgeTextAdmin {\n  background-color: var(--primaryColor);\n  color: var(--whiteColor);\n  font-size: 13px !important;\n  font-weight: 300 !important;\n  width: max-content;\n  padding: 0px 18px;\n  border-radius: 3px;\n}\n\n.badgeTextUser {\n  background-color: var(--secondaryColor);\n  color: var(--whiteColor);\n  font-size: 13px !important;\n  font-weight: 300 !important;\n  width: max-content;\n  padding: 0px 18px;\n  border-radius: 3px;\n}\n\n.badgeTextManager {\n  background-color: var(--positive);\n  color: var(--whiteColor);\n  font-size: 13px !important;\n  font-weight: 300 !important;\n  width: max-content;\n  padding: 0px 18px;\n  border-radius: 3px;\n}\n\n.labelTag {\n  position: absolute;\n  top: 39px;\n  right: -45px;\n  background-color: var(--secondaryColor);\n  color: var(--whiteColor);\n  font-weight: 500;\n  transform: rotate(45deg);\n  width: 200px;\n  text-align: center;\n  padding: 0px;\n  font-size: 14px;\n}\n\n/*Badges Css*/\n.selected {\n  background-color: var(--positive) !important;\n  color: var(--whiteColor) !important;\n}\n\n/*Form Fields Css*/\n.commonInput {\n  margin-bottom: 20px;\n}\n\n.commonInput .MuiFilledInput-root {\n  border-radius: 8px !important;\n  height: 58px;\n  background-color: transparent;\n}\n\n.searchInput {\n  height: 40px;\n  background-color: var(--searchBg);\n  border-radius: 5px;\n  display: flex;\n  align-items: center;\n  padding-right: 12px;\n  max-width: 300px;\n}\n\n.placeHolderIcon {\n  color: var(--grayColor);\n}\n\n.searchInput .MuiFilledInput-root {\n  background-color: transparent;\n}\n\n.commonSelect {\n  /* background-color: #ffffff; */\n}\n\n.hightlightBoxText {\n  display: flex;\n  align-items: center;\n}\n\n.hightlightBox {\n  display: block;\n  width: 12px;\n  height: 12px;\n  border-radius: 3px;\n  background-color: var(--grayColor);\n  float: left;\n  margin-right: 8px;\n}\n\n.hightlightBox.admin {\n  background-color: var(--primaryColor);\n}\n\n.hightlightBox.manager {\n  background-color: var(--positive);\n}\n\n.hightlightBox.user {\n  background-color: var(--secondaryColor);\n}\n\n/*Table Css Starts Here*/\n.commonTableHeader {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin: 20px 0px;\n}\n\n.commonTableHeader .commonSelect {\n  max-width: 240px;\n}\n\n.commonTableActionBtns {\n  display: flex;\n}\n\n.commonTable table {\n  border: 1px solid #ccc;\n  width: 100%;\n  margin: 0;\n  padding: 0;\n  border-collapse: collapse;\n  border-spacing: 0;\n}\n\n.commonTable table tr {\n  border: 1px solid #ddd;\n  padding: 5px;\n}\n\n.commonTable table th,\n.commonTable table td {\n  padding: 10px;\n  text-align: left;\n}\n\n.commonTable {\n  overflow: auto;\n  background-color: var(--whiteColor);\n  -webkit-box-shadow: 0px 4px 5px 0px rgba(204, 204, 204, 1);\n  -moz-box-shadow: 0px 4px 5px 0px rgba(204, 204, 204, 1);\n  box-shadow: 0px 4px 5px 0px rgba(204, 204, 204, 1);\n  border-radius: 8px;\n  border: 0px;\n}\n\n.commonTable thead th {\n  position: sticky;\n  top: 0;\n  z-index: 1;\n  color: var(--whiteColor);\n  background-color: var(--tableHeader);\n  font-weight: 600;\n}\n\n.commonTable table {\n  border-collapse: collapse;\n  width: 100%;\n}\n\n.commonTable th,\n.commonTable td {\n  padding: 8px 16px;\n}\n\n.commonTable th {\n  background: #eee;\n}\n\n.emptyBtn {\n  box-shadow: none;\n  border: 0px !important;\n  width: 30px;\n  min-width: 30px;\n  min-height: 30px;\n  margin-right: 18px;\n}\n\n.emptyBtn:hover {\n  border-radius: 30px !important;\n}\n\n.emptyBtn .MuiButton-startIcon {\n  margin-right: 0px;\n  margin-left: 0px;\n}\n\n.tablePrimaryText {\n  font-size: 16px !important;\n  font-weight: 600 !important;\n  color: var(--secondaryTextColor);\n}\n\n.tableSecondaryText {\n  font-size: 13px !important;\n  font-weight: 400 !important;\n  color: var(--grayColor);\n}\n\n.tableActionBtn {\n  background-color: var(--secondaryColor) !important;\n  color: var(--whiteColor) !important;\n  height: 40px;\n  min-height: 40px;\n  padding: 8px 24px;\n  line-height: 40px;\n  border-radius: 5px !important;\n  text-transform: capitalize;\n  font-weight: 500;\n  font-size: 18px;\n  border: 1px solid var(--borderColor);\n}\n\n.tableActionBtnDisabled {\n  background-color: var(--secondaryColorAlpha) !important;\n  color: var(--whiteColor) !important;\n  height: 40px;\n  min-height: 40px;\n  padding: 8px 24px;\n  line-height: 40px;\n  border-radius: 5px !important;\n  text-transform: capitalize;\n  font-weight: 500;\n  font-size: 18px;\n  border: 1px solid var(--borderColor);\n}\n\n.tableUserInfo {\n  display: flex;\n}\n\n.tableUserInfo img {\n  padding-right: 12px;\n}\n\n@media screen and (max-width: 600px) {\n  .commonTable table {\n    border: 0;\n    background-color: var(--bgColor);\n  }\n\n  .commonTable table thead {\n    display: none;\n  }\n\n  .commonTable table tr {\n    margin-bottom: 10px;\n    display: block;\n    border-bottom: 2px solid #ddd;\n    background-color: var(--whiteColor);\n  }\n\n  .commonTable table td {\n    display: block;\n    text-align: right;\n    font-size: 13px;\n    border-bottom: 1px dotted #ccc;\n  }\n\n  .commonTable table td:last-child {\n    border-bottom: 0;\n  }\n\n  .commonTable table td:before {\n    content: attr(data-label);\n    float: left;\n    text-transform: uppercase;\n    font-weight: bold;\n  }\n}\n\n.gridResponsiveTextLeft {\n  text-align: left !important;\n}\n\n.errorMessage {\n  color: #f00 !important;\n  font-weight: 400 !important;\n}\n\n/*Responsive Table Css Ends Here*/\n.replyBtn {\n  background-color: var(--primaryColor) !important;\n  text-transform: capitalize;\n}\n\n.postBtn {\n  background-color: var(--teritoryColor) !important;\n  border: 2px solid var(--fourthColor);\n  border-radius: 5px !important;\n  box-shadow: none;\n  text-transform: capitalize;\n}\n\n.button-border-radius {\n  border-radius: 4px !important;\n}\n\n.text-box-border-radius {\n  border-radius: 4px !important;\n  border-bottom-right-radius: 4px !important;\n  border-bottom-left-radius: 4px !important;\n}\n\n\n.tagBtn {\n  background-color: var(--fourthColor) !important;\n  color: #524E60 !important;\n  box-shadow: none !important;\n  border-radius: 5px !important;\n  text-transform: capitalize;\n  font-weight: 600;\n}\n\n.reviewDate {\n  color: var(--fourthColorText) !important;\n  font-size: 12px !important;\n  font-weight: 600 !important;\n  text-align: right;\n}\n\n.tagChips {\n  margin-right: 12px !important;\n}\n\n.chipTagCount {\n  padding: 0px 4px;\n}\n\n.display-contents {\n  display: contents;\n}\n\n/*MUI OverWritten Css Starts Here*/\n/* .MuiAppBar-root\n{\n  z-index: -1 !important;\n} */\n/*MUI OverWritten Css Ends Here*/\n.headerMenu {\n  display: flex;\n  justify-content: space-between;\n}\n\n.commonDrawer {\n  z-index: 9999;\n}\n\n/*Common Modal Css Starts Here*/\n.commonModal {\n  height: 100%;\n}\n\n.commonModal .modal-modal-title {\n  padding: 8px 12px;\n  background-color: #EDEDED;\n}\n\n.commonModal .modal-modal-description {\n  padding: 24px 12px;\n  height: calc(100% - 108px);\n  overflow-y: visible;\n}\n\n.commonModal .commonTitle {\n  color: var(--titleColor);\n  font-weight: 600 !important;\n  font-size: 32px !important;\n}\n\n.commonModal .modal-sub-title {\n  font-size: 12px;\n  padding-left: 6px;\n}\n\n.commonModal .commonFooter {\n  display: flex;\n  justify-content: space-between;\n  border-top: 1px solid #EBEBEB;\n  padding: 12px 12px;\n}\n\n.createpost.createpost .modal-modal-description {\n  padding: 0px;\n  overflow: visible;\n}\n\n.createpost .primaryFillBtn {\n  font-size: 16px !important;\n  padding: 18px 16px !important;\n  font-weight: 500;\n}\n\n.commonLabel {\n  font-size: 12px;\n  color: var(--grayColor);\n}\n\n.managementPostCard .commonLabel {\n  font-size: 12px;\n  color: var(--grayColor);\n  padding-top: 12px;\n  font-weight: 600;\n}\n\n.managementPostCard .commonInput {\n  font-size: 12px;\n  color: var(--titleColor);\n  padding-top: 12px;\n}\n\n.managementPostCard .paddingLeft {\n  padding-left: 16px;\n}\n\n/*Common Modal Css Ends Here*/\n\n/*Custom Scroll Bar Css Starts Here*/\n::-webkit-scrollbar {\n  width: 6px;\n  height: 6px;\n}\n\n::-webkit-scrollbar-track {\n  box-shadow: inset 0 0 5px #ebebeb;\n  border-radius: 10px;\n}\n\n::-webkit-scrollbar-thumb {\n  background: #757575;\n  border-radius: 10px;\n}\n\n::-webkit-scrollbar-thumb:hover {\n  background: #757575;\n}\n\n/*Custom Scroll Bar Css Ends Here*/\n.marT0 {\n  margin-top: 0px !important;\n}\n\n.marB20 {\n  margin-bottom: 20px;\n}\n\n.fs20 {\n  font-size: 20px;\n}\n\nbutton {\n  border-radius: 0 !important;\n}\n\n.minified-content {\n  display: -webkit-box;\n  -webkit-line-clamp: 6;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  height: 9em;\n  line-height: 1.5em;\n  word-wrap: break-word;\n  text-align: justify;\n}\n\n/*Create Post Css*/\n.locationCreatePost {\n  padding: 16px 20px;\n  height: 100%;\n}\n\n.lceLeft,\n.lceRight {\n  padding: 24px;\n  height: 100%;\n  overflow-y: visible;\n}\n\n.errorCard .messageText {\n  color: var(--negative) !important;\n  font-weight: 600;\n\n}\n\n.successCard .messageText {\n  color: var(--positive) !important;\n  font-weight: 600;\n}\n\n.messageTextMissing {\n  padding: 5px;\n  border-radius: 5px;\n  font-size: 15px;\n  background-color: var(--negativeOpacity);\n  color: var(--negative);\n}\n\n.missingStatusCount {\n  width: 30px;\n  height: 30px;\n  border-radius: 20px;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  font-size: 20px;\n  font-weight: 600;\n}\n\n.errorCard .missingStatusCount {\n  background-color: var(--negativeOpacity);\n  color: var(--negative);\n}\n\n.successCard .missingStatusCount {\n  background-color: var(--positiveOpacity);\n  color: var(--positive);\n}\n\n.m-0 {\n  margin: 0px;\n}\n\n.textTruncate {\n  width: 100%;\n  height: 100px;\n  max-height: 100px;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  display: -webkit-box;\n  -webkit-line-clamp: 4;\n  -webkit-box-orient: vertical;\n  word-break: break-word;\n}\n\n.not-allowed {\n  cursor: not-allowed;\n}\n\n.pagination-Text {\n  padding-left: 10px;\n  font-family: 'Barlow', sans-serif !important;\n  font-weight: 400;\n  font-size: 0.875rem;\n  line-height: 1.43;\n  letter-spacing: 0.01071em;\n  display: table-cell;\n  text-align: left;\n  color: rgba(0, 0, 0, 0.87);\n}\n\n.cardIcon {\n  width: 38px;\n  height: 38px;\n  display: flex;\n  margin-right: 12px;\n  justify-content: center;\n  align-items: center;\n  border-radius: 38px;\n}\n\n.cardIcon svg {\n  font-size: 20px;\n}\n\n\n.greenBgCcolor {\n  background-color: #1ABC9C1a;\n  color: #1ABC9C;\n}\n\n.violetBgCcolor {\n  background-color: #8E44AD1a;\n  color: #8E44AD;\n}\n\n.darkBgCcolor {\n  background-color: #2C3E501a;\n  color: #2C3E50;\n}\n\n.orangeBgCcolor {\n  background-color: #F39C121a;\n  color: #F39C12;\n}\n\n.yellowBgCcolor {\n  background-color: #F1C40F1a;\n  color: #F1C40F;\n}\n\n.cardTitle {\n  font-size: 16px;\n  font-weight: 600;\n}\n\n.cardTitleIcon {\n  display: flex;\n  align-items: center;\n  justify-content: flex-start;\n}\n\n.cardCount {\n  font-size: 16px;\n  font-weight: 700;\n  display: flex;\n  justify-content: end;\n  flex-grow: 1;\n  align-items: center;\n}\n\n.commonCardHead {\n  display: flex;\n  flex-direction: row;\n  padding-bottom: 16px;\n}\n\n.selectedMenu,\n.selectedMenu:hover {\n  background-color: var(--secondaryColorAlpha);\n  color: var(--secondaryColor);\n  font-weight: 600;\n}\n\n.selectedMenu .selectedIcon {\n  /* background-color: var(--secondaryColorAlpha); */\n  background-color: transparent;\n  color: var(--secondaryColor);\n  font-weight: 600;\n}\n\n.selectedMenu {\n  margin-left: 12px;\n}\n\n.selectedMenu::before {\n  content: \"\";\n  position: absolute;\n  width: 6px;\n  background-color: var(--secondaryColor);\n  height: 48px;\n  left: -12px;\n}\n\n.commonCardBottomSpacing {\n  margin-bottom: 24px;\n}\n\n.subtitle2 {\n  font-size: 18px !important;\n  color: #A3A3A3 !important;\n  font-weight: 500 !important;\n  margin-bottom: 24px;\n}\n\n.commonVerticalCenter {\n  display: flex;\n  align-items: center;\n}\n\n.commonShapeBtn {\n  box-shadow: none;\n  border-radius: 5px !important;\n  text-transform: capitalize;\n  font-size: 16px;\n  min-height: 55px !important;\n}\n\n.updatesShapeBtn {\n  box-shadow: none;\n  border-radius: 5px !important;\n  text-transform: capitalize;\n  font-size: 14px;\n  min-height: 35px !important;\n}\n\n\n.chipsLabel {\n  line-height: 32px;\n  font-weight: 600;\n}\n\n.commonChipBtn {\n  background-color: #ffffff;\n  border: 2px solid var(--fourthColor);\n}\n\n.marT30 {\n  margin-top: 30px;\n}\n\n.titleIcon {\n  display: flex;\n  padding-bottom: 12px;\n}\n\n.titleIcon span {\n  display: flex;\n  align-items: center;\n  margin-right: 6px;\n}\n\n.padL32 {\n  padding-left: 32px;\n}\n\n.editIconBtn {\n  color: var(--secondaryColor) !important;\n}\n\n.viewIconBtn {\n  color: var(--primaryColor) !important;\n}\n\n.editIconBtnDisabled {\n  color: var(--secondaryColorAlpha) !important;\n}\n\n.deleteIconBtn {\n  color: var(--delete) !important;\n}\n\n.deleteIconBtnDisabled {\n  color: var(--negativeOpacity) !important;\n}\n\n.mar0 {\n  margin: 0px;\n}\n\n.sampleBusineesMobile {\n  height: 490px;\n}\n\n.sampleBusineesMobile img {\n  width: 97%;\n  height: 97%;\n  object-fit: contain;\n}\n\n.sectionTitle {\n  font-size: 28px;\n  font-weight: 500;\n}\n\n.commonBorderCard {\n  box-shadow: none;\n  border: 1px solid rgba(0, 0, 0, 0.12);\n}\n\n.boxShadowNone {\n  box-shadow: none;\n}\n\n.shadowCase {\n  box-shadow: none;\n}\n\n.shadowCase:hover {\n  box-shadow: 0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12);\n}\n\n.customColSpacing {\n  padding: 1rem 0 0 1rem !important;\n}\n\n.responsiveShow {\n  display: none;\n}\n\n/*Responsive Css Starts Here*/\n@media (max-width: 768px) {\n  .pageTitle {\n    font-size: 20px !important;\n  }\n\n  .responsiveHide {\n    display: none;\n  }\n\n  .responsiveShow {\n    display: block;\n  }\n\n  .commonTableActionBtns {\n    justify-content: flex-end;\n  }\n\n  .stackResponsive {\n    white-space: normal;\n    flex-wrap: wrap;\n  }\n\n  .stackResponsiveColumn {\n    display: flex;\n    flex-direction: column;\n  }\n\n  .stackResponsiveColumnRight {\n    margin: 12px 0px !important;\n  }\n\n  .commonSelect {\n    width: 180px;\n    min-width: 100%;\n  }\n\n  main {\n    padding: 16px !important;\n  }\n\n  .quickSortChips .commonChipBtn {\n    width: 52px;\n    height: 52px;\n    border-radius: 42px;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n  }\n\n  .quickSortChips .commonChipBtn svg {\n    position: relative;\n    left: 5px;\n  }\n\n  .responsivePostModal {\n    height: auto;\n    overflow-y: visible;\n  }\n\n  .responsivePostModalRight {\n    height: 0px;\n  }\n\n  .responsivePostModalRight .postPreview {\n    padding: 16px;\n    max-width: 100%;\n  }\n\n  .commonTabs {\n    width: 100px;\n    max-width: 100%;\n    min-width: 100%;\n  }\n\n\n  /*Default Overide Css Starts Here*/\n  .css-dm569u-MuiButton-startIcon {\n    margin-right: 0px;\n  }\n}\n\n/*Responsive Css Ends Here*/", "/* barlow-vietnamese-400-normal */\n@font-face {\n  font-family: 'Barlow';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 400;\n  src: url(./files/barlow-vietnamese-400-normal.woff2) format('woff2'), url(./files/barlow-vietnamese-400-normal.woff) format('woff');\n  unicode-range: U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB;\n}\n\n/* barlow-latin-ext-400-normal */\n@font-face {\n  font-family: 'Barlow';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 400;\n  src: url(./files/barlow-latin-ext-400-normal.woff2) format('woff2'), url(./files/barlow-latin-ext-400-normal.woff) format('woff');\n  unicode-range: U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF;\n}\n\n/* barlow-latin-400-normal */\n@font-face {\n  font-family: 'Barlow';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 400;\n  src: url(./files/barlow-latin-400-normal.woff2) format('woff2'), url(./files/barlow-latin-400-normal.woff) format('woff');\n  unicode-range: U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD;\n}", ".App-logo {\n  height: 40vmin;\n  pointer-events: none;\n}\n\n@media (prefers-reduced-motion: no-preference) {\n  .App-logo {\n    animation: App-logo-spin infinite 20s linear;\n  }\n}\n\n.App-header {\n  background-color: #282c34;\n  min-height: 100vh;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  font-size: calc(10px + 2vmin);\n  color: white;\n}\n\n.App-link {\n  color: #61dafb;\n}\n/* .MuiGrid-root\n{\n  margin: 0px !important;\n} */\n@keyframes App-logo-spin {\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n}\n", ".accountLeft \n{\n    background-image: url(../../assets/login/loginBg.png);\n    height: 100%;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    padding: 0% 12%;\n    background-size: cover;\n    background-position: center;\n    background-repeat: no-repeat;\n}\n.accountRight\n{\n    height: 100%;\n    padding: 0px 8%;\n    text-align: left;\n    display: flex;\n    justify-content: center;\n    flex-direction: column;\n}\n.accountLogo\n{\n    width: 280px;\n    margin-bottom: 36px;\n}\n.welcomeTitle\n{\n    font-size: 28px;\n    font-weight: 600;\n    color: #2F2F2F;\n    margin-bottom: 20px;\n}\n.MuiFilledInput-root::before,\n.MuiFilledInput-root::after\n{\n    border: 0px !important;\n}\n.accountRight .commonInput\n{\n    margin-bottom: 4px;\n    background-color: #ECECEC;\n    border-radius: 8px;\n    padding: 0px 16px;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n}\n.accountRight .primaryFillBtn\n{\n    margin-top: 8px;\n    font-size: 18px !important;\n    padding: 24px !important;\n    font-weight: 600;\n}\n/* .MuiGrid-item\n{\n    padding-left: 0px !important;\n} */", ".dashboardTopIconCard\n{\n    display: flex;\n    border-radius: 18px !important;\n}\n.dashboardTopIcon\n{\n    display: flex;\n    justify-content: center;\n    align-items: center;\n}\n.dashboardTopInfo\n{\n    display: flex;\n    flex-direction: column;\n    flex-grow: 1;\n    padding-left: 24px;\n    justify-content: center;\n}\n.dashboardTopCount\n{\n    font-size: 28px;\n    font-weight: bold;\n}\n.dashboardTopTitle\n{\n    color: #464255;\n    padding: 4px 0px;\n}\n.dashboardTopStatus\n{\n    color: #A3A3A3;\n    font-weight: 400;\n    font-size: 12px;\n}\n.dashboardTopStatusTypeSucess,\n.dashboardTopStatusTypeDanger\n{\n    width: 20px;\n    height: 20px;\n    border-radius: 10px;\n    background-color: var(--positiveOpacity);\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    float: left;\n    margin-right: 8px;\n    color: var(--positive);\n}\n.dashboardTopStatusType svg,\n.dashboardTopStatusTypeDanger svg\n{\n    width: 14px;\n}\n.dashboardTopStatusTypeDanger\n{\n    background-color: var(--negativeOpacity);\n    color: var(--negative);\n}\n.pieChartDiv > div:first-of-type\n{\n    height: 100%;\n}   \n.pieChartInner\n{\n    height: 90%;\n    display: flex;\n    align-items: center;\n\n}", ".commonRoleInput \n{\n    margin: 0px 4px 28px;\n}\n.commonRoleInput .MuiFormGroup-root\n{\n    display: flex;\n    flex-direction: row;\n    justify-content: space-between;\n}", ".alertModel .MuiDialog-paper\n{\n    max-width: 350px;\n    width: 350px;\n    margin: 0px;\n    padding: 32px 24px;\n    text-align: center;\n}\n.alertIcon svg\n{\n    width: 40px;\n    height: 40px;\n}\n.errorColor\n{\n    color: #F70000;\n}\n.warningColor\n{\n    color: #F7C500;\n}\n.successColor\n{\n    color: #139F4B;\n}\n.infoColor\n{\n    color: #00C3EC;\n}\n.alertDialogDescription\n{\n    font-size: 16px !important;\n    margin-top: 12px !important;\n    margin-bottom: 12px;\n    padding: 0px !important;\n}\n.alertDialogDescription .content\n{\n    color: #212121 !important;\n}\n.alertFooter\n{\n    justify-content: center !important;\n}\n.alertAction\n{\n\n}", ":root {\n    --postPreviewBgColor: #5D5DFF\n}\n\n.reviewCard {\n    flex-direction: column;\n}\n\n.reviewContent {\n    display: -webkit-box;\n    -webkit-box-orient: vertical;\n    overflow: hidden;\n    text-overflow: ellipsis;\n    -webkit-line-clamp: 2;\n    height: 48px;\n}\n\n.reviewContentDefault {\n    height: 48px;\n    font-weight: 600;\n    font-style: italic;\n    color: #b5b5b5;\n}\n\n.postPreviewEdit {\n    height: 100%;\n}\n\n.postPreviewEdit .MuiTabs-root {\n    height: 48px;\n}\n\n.postPreviewEdit .MuiTabs-root+.MuiBox-root {\n    height: calc(100% - 142px);\n    overflow-y: auto;\n}\n.qandaReply\n{\n    display: flex;\n    justify-content: end;\n}", ".businessAuthenticate {\n    position: relative;\n    width: 40px;\n    margin-right: 10px;\n}\n\n.businessAuthenticate img {\n    width: 25px;\n}\n\n.businessAuthenticate .businessStatus {\n    position: absolute;\n    top: 16px;\n    right: -8px;\n    background-color: #ffffff;\n    width: 24px;\n    height: 24px;\n    border-radius: 30px;\n}", ":root {\r\n    --postPreviewBgColor: #5D5DFF\r\n}\r\n\r\n.reviewCard {\r\n    flex-direction: column;\r\n}\r\n\r\n.reviewContent {\r\n    display: -webkit-box;\r\n    -webkit-box-orient: vertical;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n    -webkit-line-clamp: 2;\r\n    height: 48px;\r\n    color: var(--reviewDescription);\r\n}\r\n\r\n.reviewContentDefault {\r\n    height: 48px;\r\n    font-weight: 600;\r\n    font-style: italic;\r\n    color: #b5b5b5;\r\n}\r\n\r\n.postPreviewEdit {\r\n    height: 100%;\r\n}\r\n\r\n.postPreviewEdit .MuiTabs-root {\r\n    height: 48px;\r\n    margin-left: 20px;\r\n    margin-bottom: 10px;\r\n}\r\n\r\n.postPreviewEdit .MuiTabs-root+.MuiBox-root {\r\n    height: calc(100% - 164px);\r\n    overflow-y: visible;\r\n}\r\n\r\n.bindedTags {\r\n    overflow-x: scroll;\r\n    display: flex;\r\n    white-space: nowrap;\r\n    padding: 0px 0px 12px;\r\n}\r\n\r\n.bindedTags .tagChips {\r\n    margin-right: 4px !important;\r\n}\r\n\r\n\r\n/* .postPreview\r\n{\r\n    background-color: var(--postPreviewBgColor);\r\n    padding: 42px 20px 20px;\r\n} */", ".testimonialInnerFour\n{\n    max-width: 420px;\n    background-color: #ffffff61;\n    border-radius: 24px;\n    height: auto;\n    margin: 0 auto;\n    padding: 62px 24px 12px;\n    position: relative;\n}\n.testimonialInnerFour .MuiAvatar-circular\n{\n    width: 90px;\n    height: 90px;\n    position: absolute;\n    top: -45px;\n    margin: 0 auto;\n    left: 0;\n    right: 0;\n}\n.testimonialInnerFour .testimonialTitle\n{\n    color: #212121;\n    font-size: 24px;\n    margin-bottom: 12px;\n}", ".postPreviewInfoLeftTopCard \n{\n    padding: 16px 16px 0px;\n}\n.createPostLeft\n{\n    border-right: 1px solid #cccccc;\n}\n.postPreview \n{\n    width: 400px;\n    height: 400px;\n    margin: 30px auto;\n}\n.postPreviewInner1\n{\n    width: 100%;\n    height: 100%;\n}\n.postPreviewInner2\n{\n    width: 100%;\n    height: 100%;\n}\n.postTemplateBgBtn\n{\n    height: 150px;\n    min-height: 150px;\n    margin: 0 auto;\n    text-align: center;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n}\n.postPreviewActions\n{\n    margin-top: 32px;\n}\n.postTemplateBg\n{\n    width: 100%;\n    height: 100%;\n    object-fit: cover;\n}", ".testimonialInnerFive\n{\n    background-color: #ffffff1a;\n    max-width: 420px;\n    margin: 0 auto;\n    padding: 24px;\n    border-radius: 20px;\n    -webkit-box-shadow: 3px 4px 5px 0px rgba(179,179,179,1);\n    -moz-box-shadow: 3px 4px 5px 0px rgba(179,179,179,1);\n    box-shadow: 3px 4px 5px 0px rgba(179,179,179,1);\n    position: relative;\n}\n.testimonialInnerFive .MuiAvatar-circular\n{\n    width: 60px;\n    height: 60px;\n    margin-right: 12px;\n}\n.testimonialInnerFive .testmonialUserInfo\n{\n    display: flex;\n}\n.testimonialInnerFive .testmonialUserName\n{\n    color: #00416F;\n    font-size: 28px;\n    font-style: italic;\n    font-weight: 400;\n    letter-spacing: 4px;\n}\n.testimonialInnerFive .testmonialUserDesignation\n{\n    color: #24ABF1 !important;\n    font-weight: 500 !important;\n}\n.testimonialInnerFive .testmonialUserInfo\n{\n    display: flex;\n    text-align: left;\n}\n.testimonialInnerFive .ratingIcon\n{\n    font-size: 28px;\n}\n.testimonialInnerFive .quoteMark\n{\n    position: absolute;\n    top: -10px;\n    right: 40px;\n}\n.testimonialInnerFive .quoteMark img\n{\n    width: 80px;\n}", ".businessLogoPreview\n{\n    width: 90px;\n    height: 90px;\n    border-radius: 45px;\n    background-color: #ffffff;\n    border: 1px solid #cccccc; \n    margin-top: -60px;  \n}\n.missingCard\n{\n    box-shadow: 6px 6px 54px rgba(0, 0, 0, 0.05);\n    border: 0px;\n    border-radius: 16px;\n}\n.missingCard .missingCardInner\n{\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n}", ".dashboardTopIconCard {\n    display: flex;\n    border-radius: 18px !important;\n}\n\n.dashboardTopIcon {\n    display: flex;\n    justify-content: center;\n    align-items: center;\n}\n\n.dashboardTopInfo {\n    display: flex;\n    flex-direction: column;\n    flex-grow: 1;\n    padding-left: 40px;\n    justify-content: center;\n}\n\n.innerImage1 {\n    width: 60%;\n    height: auto;\n    object-fit: contain;\n}\n\n.innerImage2 {\n    width: 60%;\n    height: auto;\n    object-fit: contain;\n}\n\n.innerImage3 {\n    width: 57%;\n    height: auto;\n    object-fit: contain;\n}\n\n.dashboardTopCount {\n    font-size: 28px;\n    font-weight: bold;\n}\n\n.dashboardTopTitle {\n    color: #464255;\n    padding: 4px 0px;\n}\n\n.dashboardTopStatus {\n    color: #A3A3A3;\n    font-weight: 400;\n    font-size: 12px;\n}\n\n.dashboardTopStatusTypeSucess,\n.dashboardTopStatusTypeDanger {\n    width: 20px;\n    height: 20px;\n    border-radius: 10px;\n    background-color: var(--positiveOpacity);\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    float: left;\n    margin-right: 8px;\n    color: var(--positive);\n}\n\n.dashboardTopStatusType svg,\n.dashboardTopStatusTypeDanger svg {\n    width: 14px;\n}\n\n.dashboardTopStatusTypeDanger {\n    background-color: var(--negativeOpacity);\n    color: var(--negative);\n}\n\n.pieChartDiv>div:first-of-type {\n    height: 100%;\n}\n\n.pieChartInner {\n    height: 90%;\n    display: flex;\n    align-items: center;\n\n}", "/* Manage Assets Screen Styles */\n\n.manage-assets-container {\n  padding: 20px;\n}\n\n.storage-info-card {\n  margin-bottom: 24px;\n}\n\n.storage-progress {\n  height: 10px;\n  border-radius: 5px;\n  margin-bottom: 8px;\n}\n\n.upload-zone {\n  border: 2px dashed #ccc;\n  border-radius: 8px;\n  padding: 40px;\n  text-align: center;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  margin-bottom: 24px;\n}\n\n.upload-zone:hover {\n  border-color: #1976d2;\n  background-color: #f5f5f5;\n}\n\n.upload-zone.dragover {\n  border-color: #1976d2;\n  background-color: #e3f2fd;\n}\n\n.upload-zone-content {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 16px;\n}\n\n.upload-icon {\n  font-size: 48px !important;\n  color: #1976d2;\n}\n\n.upload-text {\n  color: #666;\n  margin: 0;\n}\n\n.upload-button {\n  margin-top: 16px !important;\n}\n\n.assets-grid {\n  margin-top: 24px;\n}\n\n.asset-card {\n  position: relative;\n  height: 200px;\n  cursor: pointer;\n  transition: transform 0.2s ease, box-shadow 0.2s ease;\n  overflow: hidden;\n}\n\n.asset-card:hover {\n  transform: translateY(-4px);\n  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);\n}\n\n.asset-card-media {\n  width: 100%;\n  height: 140px;\n  object-fit: cover;\n  background-color: #f5f5f5;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.asset-card-media img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n.asset-card-media video {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n.asset-placeholder {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 100%;\n  background-color: #f5f5f5;\n  color: #999;\n}\n\n.asset-placeholder-icon {\n  font-size: 48px !important;\n  margin-bottom: 8px;\n}\n\n.asset-card-content {\n  padding: 8px 12px !important;\n  height: 60px;\n  display: flex;\n  flex-direction: column;\n  justify-content: space-between;\n}\n\n.asset-filename {\n  font-size: 0.875rem;\n  font-weight: 500;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  margin-bottom: 4px;\n}\n\n.asset-info {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.asset-size {\n  font-size: 0.75rem;\n  color: #666;\n}\n\n.asset-type-chip {\n  font-size: 0.625rem !important;\n  height: 20px !important;\n}\n\n.asset-actions {\n  position: absolute;\n  top: 8px;\n  right: 8px;\n  display: flex;\n  gap: 4px;\n  opacity: 0;\n  transition: opacity 0.2s ease;\n}\n\n.asset-card:hover .asset-actions {\n  opacity: 1;\n}\n\n.asset-action-button {\n  background-color: rgba(255, 255, 255, 0.9) !important;\n  color: #333 !important;\n  min-width: 32px !important;\n  width: 32px !important;\n  height: 32px !important;\n  padding: 0 !important;\n}\n\n.asset-action-button:hover {\n  background-color: rgba(255, 255, 255, 1) !important;\n}\n\n.delete-button {\n  color: #d32f2f !important;\n}\n\n.delete-button:hover {\n  background-color: rgba(211, 47, 47, 0.1) !important;\n}\n\n.no-assets {\n  text-align: center;\n  padding: 40px;\n  color: #666;\n}\n\n.no-assets-icon {\n  font-size: 64px !important;\n  color: #ccc;\n  margin-bottom: 16px;\n}\n\n.pagination-container {\n  display: flex;\n  justify-content: center;\n  margin-top: 24px;\n}\n\n.business-selection {\n  margin-bottom: 24px;\n}\n\n.business-button {\n  margin-right: 12px !important;\n  margin-bottom: 8px !important;\n}\n\n.loading-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(255, 255, 255, 0.8);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 1;\n}\n\n.file-input {\n  display: none;\n}\n\n.upload-progress {\n  margin-top: 16px;\n}\n\n.storage-warning {\n  margin-bottom: 16px;\n}\n\n/* Responsive adjustments */\n@media (max-width: 768px) {\n  .asset-card {\n    height: 180px;\n  }\n  \n  .asset-card-media {\n    height: 120px;\n  }\n  \n  .upload-zone {\n    padding: 24px;\n  }\n  \n  .upload-icon {\n    font-size: 36px !important;\n  }\n}\n\n@media (max-width: 480px) {\n  .asset-card {\n    height: 160px;\n  }\n  \n  .asset-card-media {\n    height: 100px;\n  }\n  \n  .upload-zone {\n    padding: 16px;\n  }\n  \n  .upload-icon {\n    font-size: 32px !important;\n  }\n  \n  .asset-filename {\n    font-size: 0.75rem;\n  }\n}\n", "/* Simple Flipping LocoBiz Logo Loader */\n.rotating-loader-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 100vh;\n  width: 100vw;\n  background-color: transparent;\n  position: fixed;\n  top: 0;\n  left: 0;\n  z-index: 9999;\n}\n\n.rotating-logo {\n  width: 80px;\n  height: 80px;\n  animation: flip 1s linear infinite;\n  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));\n}\n\n/* Simple flip animation */\n@keyframes flip {\n  0% {\n    transform: rotateY(0deg);\n  }\n\n  50% {\n    transform: rotateY(180deg);\n  }\n\n  100% {\n    transform: rotateY(360deg);\n  }\n}\n\n/* Responsive design */\n@media (max-width: 768px) {\n  .rotating-logo {\n    width: 60px;\n    height: 60px;\n  }\n}\n\n@media (max-width: 480px) {\n  .rotating-logo {\n    width: 50px;\n    height: 50px;\n  }\n}", "/* roboto-cyrillic-ext-300-normal */\n@font-face {\n  font-family: 'Roboto';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 300;\n  src: url(./files/roboto-cyrillic-ext-300-normal.woff2) format('woff2'), url(./files/roboto-cyrillic-ext-300-normal.woff) format('woff');\n  unicode-range: U+0460-052F,U+1C80-1C8A,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F;\n}\n\n/* roboto-cyrillic-300-normal */\n@font-face {\n  font-family: 'Roboto';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 300;\n  src: url(./files/roboto-cyrillic-300-normal.woff2) format('woff2'), url(./files/roboto-cyrillic-300-normal.woff) format('woff');\n  unicode-range: U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116;\n}\n\n/* roboto-greek-ext-300-normal */\n@font-face {\n  font-family: 'Roboto';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 300;\n  src: url(./files/roboto-greek-ext-300-normal.woff2) format('woff2'), url(./files/roboto-greek-ext-300-normal.woff) format('woff');\n  unicode-range: U+1F00-1FFF;\n}\n\n/* roboto-greek-300-normal */\n@font-face {\n  font-family: 'Roboto';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 300;\n  src: url(./files/roboto-greek-300-normal.woff2) format('woff2'), url(./files/roboto-greek-300-normal.woff) format('woff');\n  unicode-range: U+0370-0377,U+037A-037F,U+0384-038A,U+038C,U+038E-03A1,U+03A3-03FF;\n}\n\n/* roboto-math-300-normal */\n@font-face {\n  font-family: 'Roboto';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 300;\n  src: url(./files/roboto-math-300-normal.woff2) format('woff2'), url(./files/roboto-math-300-normal.woff) format('woff');\n  unicode-range: U+0302-0303,U+0305,U+0307-0308,U+0310,U+0312,U+0315,U+031A,U+0326-0327,U+032C,U+032F-0330,U+0332-0333,U+0338,U+033A,U+0346,U+034D,U+0391-03A1,U+03A3-03A9,U+03B1-03C9,U+03D1,U+03D5-03D6,U+03F0-03F1,U+03F4-03F5,U+2016-2017,U+2034-2038,U+203C,U+2040,U+2043,U+2047,U+2050,U+2057,U+205F,U+2070-2071,U+2074-208E,U+2090-209C,U+20D0-20DC,U+20E1,U+20E5-20EF,U+2100-2112,U+2114-2115,U+2117-2121,U+2123-214F,U+2190,U+2192,U+2194-21AE,U+21B0-21E5,U+21F1-21F2,U+21F4-2211,U+2213-2214,U+2216-22FF,U+2308-230B,U+2310,U+2319,U+231C-2321,U+2336-237A,U+237C,U+2395,U+239B-23B7,U+23D0,U+23DC-23E1,U+2474-2475,U+25AF,U+25B3,U+25B7,U+25BD,U+25C1,U+25CA,U+25CC,U+25FB,U+266D-266F,U+27C0-27FF,U+2900-2AFF,U+2B0E-2B11,U+2B30-2B4C,U+2BFE,U+3030,U+FF5B,U+FF5D,U+1D400-1D7FF,U+1EE00-1EEFF;\n}\n\n/* roboto-symbols-300-normal */\n@font-face {\n  font-family: 'Roboto';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 300;\n  src: url(./files/roboto-symbols-300-normal.woff2) format('woff2'), url(./files/roboto-symbols-300-normal.woff) format('woff');\n  unicode-range: U+0001-000C,U+000E-001F,U+007F-009F,U+20DD-20E0,U+20E2-20E4,U+2150-218F,U+2190,U+2192,U+2194-2199,U+21AF,U+21E6-21F0,U+21F3,U+2218-2219,U+2299,U+22C4-22C6,U+2300-243F,U+2440-244A,U+2460-24FF,U+25A0-27BF,U+2800-28FF,U+2921-2922,U+2981,U+29BF,U+29EB,U+2B00-2BFF,U+4DC0-4DFF,U+FFF9-FFFB,U+10140-1018E,U+10190-1019C,U+101A0,U+101D0-101FD,U+102E0-102FB,U+10E60-10E7E,U+1D2C0-1D2D3,U+1D2E0-1D37F,U+1F000-1F0FF,U+1F100-1F1AD,U+1F1E6-1F1FF,U+1F30D-1F30F,U+1F315,U+1F31C,U+1F31E,U+1F320-1F32C,U+1F336,U+1F378,U+1F37D,U+1F382,U+1F393-1F39F,U+1F3A7-1F3A8,U+1F3AC-1F3AF,U+1F3C2,U+1F3C4-1F3C6,U+1F3CA-1F3CE,U+1F3D4-1F3E0,U+1F3ED,U+1F3F1-1F3F3,U+1F3F5-1F3F7,U+1F408,U+1F415,U+1F41F,U+1F426,U+1F43F,U+1F441-1F442,U+1F444,U+1F446-1F449,U+1F44C-1F44E,U+1F453,U+1F46A,U+1F47D,U+1F4A3,U+1F4B0,U+1F4B3,U+1F4B9,U+1F4BB,U+1F4BF,U+1F4C8-1F4CB,U+1F4D6,U+1F4DA,U+1F4DF,U+1F4E3-1F4E6,U+1F4EA-1F4ED,U+1F4F7,U+1F4F9-1F4FB,U+1F4FD-1F4FE,U+1F503,U+1F507-1F50B,U+1F50D,U+1F512-1F513,U+1F53E-1F54A,U+1F54F-1F5FA,U+1F610,U+1F650-1F67F,U+1F687,U+1F68D,U+1F691,U+1F694,U+1F698,U+1F6AD,U+1F6B2,U+1F6B9-1F6BA,U+1F6BC,U+1F6C6-1F6CF,U+1F6D3-1F6D7,U+1F6E0-1F6EA,U+1F6F0-1F6F3,U+1F6F7-1F6FC,U+1F700-1F7FF,U+1F800-1F80B,U+1F810-1F847,U+1F850-1F859,U+1F860-1F887,U+1F890-1F8AD,U+1F8B0-1F8BB,U+1F8C0-1F8C1,U+1F900-1F90B,U+1F93B,U+1F946,U+1F984,U+1F996,U+1F9E9,U+1FA00-1FA6F,U+1FA70-1FA7C,U+1FA80-1FA89,U+1FA8F-1FAC6,U+1FACE-1FADC,U+1FADF-1FAE9,U+1FAF0-1FAF8,U+1FB00-1FBFF;\n}\n\n/* roboto-vietnamese-300-normal */\n@font-face {\n  font-family: 'Roboto';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 300;\n  src: url(./files/roboto-vietnamese-300-normal.woff2) format('woff2'), url(./files/roboto-vietnamese-300-normal.woff) format('woff');\n  unicode-range: U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB;\n}\n\n/* roboto-latin-ext-300-normal */\n@font-face {\n  font-family: 'Roboto';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 300;\n  src: url(./files/roboto-latin-ext-300-normal.woff2) format('woff2'), url(./files/roboto-latin-ext-300-normal.woff) format('woff');\n  unicode-range: U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF;\n}\n\n/* roboto-latin-300-normal */\n@font-face {\n  font-family: 'Roboto';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 300;\n  src: url(./files/roboto-latin-300-normal.woff2) format('woff2'), url(./files/roboto-latin-300-normal.woff) format('woff');\n  unicode-range: U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD;\n}", "/* roboto-cyrillic-ext-400-normal */\n@font-face {\n  font-family: 'Roboto';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 400;\n  src: url(./files/roboto-cyrillic-ext-400-normal.woff2) format('woff2'), url(./files/roboto-cyrillic-ext-400-normal.woff) format('woff');\n  unicode-range: U+0460-052F,U+1C80-1C8A,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F;\n}\n\n/* roboto-cyrillic-400-normal */\n@font-face {\n  font-family: 'Roboto';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 400;\n  src: url(./files/roboto-cyrillic-400-normal.woff2) format('woff2'), url(./files/roboto-cyrillic-400-normal.woff) format('woff');\n  unicode-range: U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116;\n}\n\n/* roboto-greek-ext-400-normal */\n@font-face {\n  font-family: 'Roboto';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 400;\n  src: url(./files/roboto-greek-ext-400-normal.woff2) format('woff2'), url(./files/roboto-greek-ext-400-normal.woff) format('woff');\n  unicode-range: U+1F00-1FFF;\n}\n\n/* roboto-greek-400-normal */\n@font-face {\n  font-family: 'Roboto';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 400;\n  src: url(./files/roboto-greek-400-normal.woff2) format('woff2'), url(./files/roboto-greek-400-normal.woff) format('woff');\n  unicode-range: U+0370-0377,U+037A-037F,U+0384-038A,U+038C,U+038E-03A1,U+03A3-03FF;\n}\n\n/* roboto-math-400-normal */\n@font-face {\n  font-family: 'Roboto';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 400;\n  src: url(./files/roboto-math-400-normal.woff2) format('woff2'), url(./files/roboto-math-400-normal.woff) format('woff');\n  unicode-range: U+0302-0303,U+0305,U+0307-0308,U+0310,U+0312,U+0315,U+031A,U+0326-0327,U+032C,U+032F-0330,U+0332-0333,U+0338,U+033A,U+0346,U+034D,U+0391-03A1,U+03A3-03A9,U+03B1-03C9,U+03D1,U+03D5-03D6,U+03F0-03F1,U+03F4-03F5,U+2016-2017,U+2034-2038,U+203C,U+2040,U+2043,U+2047,U+2050,U+2057,U+205F,U+2070-2071,U+2074-208E,U+2090-209C,U+20D0-20DC,U+20E1,U+20E5-20EF,U+2100-2112,U+2114-2115,U+2117-2121,U+2123-214F,U+2190,U+2192,U+2194-21AE,U+21B0-21E5,U+21F1-21F2,U+21F4-2211,U+2213-2214,U+2216-22FF,U+2308-230B,U+2310,U+2319,U+231C-2321,U+2336-237A,U+237C,U+2395,U+239B-23B7,U+23D0,U+23DC-23E1,U+2474-2475,U+25AF,U+25B3,U+25B7,U+25BD,U+25C1,U+25CA,U+25CC,U+25FB,U+266D-266F,U+27C0-27FF,U+2900-2AFF,U+2B0E-2B11,U+2B30-2B4C,U+2BFE,U+3030,U+FF5B,U+FF5D,U+1D400-1D7FF,U+1EE00-1EEFF;\n}\n\n/* roboto-symbols-400-normal */\n@font-face {\n  font-family: 'Roboto';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 400;\n  src: url(./files/roboto-symbols-400-normal.woff2) format('woff2'), url(./files/roboto-symbols-400-normal.woff) format('woff');\n  unicode-range: U+0001-000C,U+000E-001F,U+007F-009F,U+20DD-20E0,U+20E2-20E4,U+2150-218F,U+2190,U+2192,U+2194-2199,U+21AF,U+21E6-21F0,U+21F3,U+2218-2219,U+2299,U+22C4-22C6,U+2300-243F,U+2440-244A,U+2460-24FF,U+25A0-27BF,U+2800-28FF,U+2921-2922,U+2981,U+29BF,U+29EB,U+2B00-2BFF,U+4DC0-4DFF,U+FFF9-FFFB,U+10140-1018E,U+10190-1019C,U+101A0,U+101D0-101FD,U+102E0-102FB,U+10E60-10E7E,U+1D2C0-1D2D3,U+1D2E0-1D37F,U+1F000-1F0FF,U+1F100-1F1AD,U+1F1E6-1F1FF,U+1F30D-1F30F,U+1F315,U+1F31C,U+1F31E,U+1F320-1F32C,U+1F336,U+1F378,U+1F37D,U+1F382,U+1F393-1F39F,U+1F3A7-1F3A8,U+1F3AC-1F3AF,U+1F3C2,U+1F3C4-1F3C6,U+1F3CA-1F3CE,U+1F3D4-1F3E0,U+1F3ED,U+1F3F1-1F3F3,U+1F3F5-1F3F7,U+1F408,U+1F415,U+1F41F,U+1F426,U+1F43F,U+1F441-1F442,U+1F444,U+1F446-1F449,U+1F44C-1F44E,U+1F453,U+1F46A,U+1F47D,U+1F4A3,U+1F4B0,U+1F4B3,U+1F4B9,U+1F4BB,U+1F4BF,U+1F4C8-1F4CB,U+1F4D6,U+1F4DA,U+1F4DF,U+1F4E3-1F4E6,U+1F4EA-1F4ED,U+1F4F7,U+1F4F9-1F4FB,U+1F4FD-1F4FE,U+1F503,U+1F507-1F50B,U+1F50D,U+1F512-1F513,U+1F53E-1F54A,U+1F54F-1F5FA,U+1F610,U+1F650-1F67F,U+1F687,U+1F68D,U+1F691,U+1F694,U+1F698,U+1F6AD,U+1F6B2,U+1F6B9-1F6BA,U+1F6BC,U+1F6C6-1F6CF,U+1F6D3-1F6D7,U+1F6E0-1F6EA,U+1F6F0-1F6F3,U+1F6F7-1F6FC,U+1F700-1F7FF,U+1F800-1F80B,U+1F810-1F847,U+1F850-1F859,U+1F860-1F887,U+1F890-1F8AD,U+1F8B0-1F8BB,U+1F8C0-1F8C1,U+1F900-1F90B,U+1F93B,U+1F946,U+1F984,U+1F996,U+1F9E9,U+1FA00-1FA6F,U+1FA70-1FA7C,U+1FA80-1FA89,U+1FA8F-1FAC6,U+1FACE-1FADC,U+1FADF-1FAE9,U+1FAF0-1FAF8,U+1FB00-1FBFF;\n}\n\n/* roboto-vietnamese-400-normal */\n@font-face {\n  font-family: 'Roboto';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 400;\n  src: url(./files/roboto-vietnamese-400-normal.woff2) format('woff2'), url(./files/roboto-vietnamese-400-normal.woff) format('woff');\n  unicode-range: U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB;\n}\n\n/* roboto-latin-ext-400-normal */\n@font-face {\n  font-family: 'Roboto';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 400;\n  src: url(./files/roboto-latin-ext-400-normal.woff2) format('woff2'), url(./files/roboto-latin-ext-400-normal.woff) format('woff');\n  unicode-range: U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF;\n}\n\n/* roboto-latin-400-normal */\n@font-face {\n  font-family: 'Roboto';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 400;\n  src: url(./files/roboto-latin-400-normal.woff2) format('woff2'), url(./files/roboto-latin-400-normal.woff) format('woff');\n  unicode-range: U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD;\n}", "/* roboto-cyrillic-ext-500-normal */\n@font-face {\n  font-family: 'Roboto';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 500;\n  src: url(./files/roboto-cyrillic-ext-500-normal.woff2) format('woff2'), url(./files/roboto-cyrillic-ext-500-normal.woff) format('woff');\n  unicode-range: U+0460-052F,U+1C80-1C8A,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F;\n}\n\n/* roboto-cyrillic-500-normal */\n@font-face {\n  font-family: 'Roboto';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 500;\n  src: url(./files/roboto-cyrillic-500-normal.woff2) format('woff2'), url(./files/roboto-cyrillic-500-normal.woff) format('woff');\n  unicode-range: U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116;\n}\n\n/* roboto-greek-ext-500-normal */\n@font-face {\n  font-family: 'Roboto';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 500;\n  src: url(./files/roboto-greek-ext-500-normal.woff2) format('woff2'), url(./files/roboto-greek-ext-500-normal.woff) format('woff');\n  unicode-range: U+1F00-1FFF;\n}\n\n/* roboto-greek-500-normal */\n@font-face {\n  font-family: 'Roboto';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 500;\n  src: url(./files/roboto-greek-500-normal.woff2) format('woff2'), url(./files/roboto-greek-500-normal.woff) format('woff');\n  unicode-range: U+0370-0377,U+037A-037F,U+0384-038A,U+038C,U+038E-03A1,U+03A3-03FF;\n}\n\n/* roboto-math-500-normal */\n@font-face {\n  font-family: 'Roboto';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 500;\n  src: url(./files/roboto-math-500-normal.woff2) format('woff2'), url(./files/roboto-math-500-normal.woff) format('woff');\n  unicode-range: U+0302-0303,U+0305,U+0307-0308,U+0310,U+0312,U+0315,U+031A,U+0326-0327,U+032C,U+032F-0330,U+0332-0333,U+0338,U+033A,U+0346,U+034D,U+0391-03A1,U+03A3-03A9,U+03B1-03C9,U+03D1,U+03D5-03D6,U+03F0-03F1,U+03F4-03F5,U+2016-2017,U+2034-2038,U+203C,U+2040,U+2043,U+2047,U+2050,U+2057,U+205F,U+2070-2071,U+2074-208E,U+2090-209C,U+20D0-20DC,U+20E1,U+20E5-20EF,U+2100-2112,U+2114-2115,U+2117-2121,U+2123-214F,U+2190,U+2192,U+2194-21AE,U+21B0-21E5,U+21F1-21F2,U+21F4-2211,U+2213-2214,U+2216-22FF,U+2308-230B,U+2310,U+2319,U+231C-2321,U+2336-237A,U+237C,U+2395,U+239B-23B7,U+23D0,U+23DC-23E1,U+2474-2475,U+25AF,U+25B3,U+25B7,U+25BD,U+25C1,U+25CA,U+25CC,U+25FB,U+266D-266F,U+27C0-27FF,U+2900-2AFF,U+2B0E-2B11,U+2B30-2B4C,U+2BFE,U+3030,U+FF5B,U+FF5D,U+1D400-1D7FF,U+1EE00-1EEFF;\n}\n\n/* roboto-symbols-500-normal */\n@font-face {\n  font-family: 'Roboto';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 500;\n  src: url(./files/roboto-symbols-500-normal.woff2) format('woff2'), url(./files/roboto-symbols-500-normal.woff) format('woff');\n  unicode-range: U+0001-000C,U+000E-001F,U+007F-009F,U+20DD-20E0,U+20E2-20E4,U+2150-218F,U+2190,U+2192,U+2194-2199,U+21AF,U+21E6-21F0,U+21F3,U+2218-2219,U+2299,U+22C4-22C6,U+2300-243F,U+2440-244A,U+2460-24FF,U+25A0-27BF,U+2800-28FF,U+2921-2922,U+2981,U+29BF,U+29EB,U+2B00-2BFF,U+4DC0-4DFF,U+FFF9-FFFB,U+10140-1018E,U+10190-1019C,U+101A0,U+101D0-101FD,U+102E0-102FB,U+10E60-10E7E,U+1D2C0-1D2D3,U+1D2E0-1D37F,U+1F000-1F0FF,U+1F100-1F1AD,U+1F1E6-1F1FF,U+1F30D-1F30F,U+1F315,U+1F31C,U+1F31E,U+1F320-1F32C,U+1F336,U+1F378,U+1F37D,U+1F382,U+1F393-1F39F,U+1F3A7-1F3A8,U+1F3AC-1F3AF,U+1F3C2,U+1F3C4-1F3C6,U+1F3CA-1F3CE,U+1F3D4-1F3E0,U+1F3ED,U+1F3F1-1F3F3,U+1F3F5-1F3F7,U+1F408,U+1F415,U+1F41F,U+1F426,U+1F43F,U+1F441-1F442,U+1F444,U+1F446-1F449,U+1F44C-1F44E,U+1F453,U+1F46A,U+1F47D,U+1F4A3,U+1F4B0,U+1F4B3,U+1F4B9,U+1F4BB,U+1F4BF,U+1F4C8-1F4CB,U+1F4D6,U+1F4DA,U+1F4DF,U+1F4E3-1F4E6,U+1F4EA-1F4ED,U+1F4F7,U+1F4F9-1F4FB,U+1F4FD-1F4FE,U+1F503,U+1F507-1F50B,U+1F50D,U+1F512-1F513,U+1F53E-1F54A,U+1F54F-1F5FA,U+1F610,U+1F650-1F67F,U+1F687,U+1F68D,U+1F691,U+1F694,U+1F698,U+1F6AD,U+1F6B2,U+1F6B9-1F6BA,U+1F6BC,U+1F6C6-1F6CF,U+1F6D3-1F6D7,U+1F6E0-1F6EA,U+1F6F0-1F6F3,U+1F6F7-1F6FC,U+1F700-1F7FF,U+1F800-1F80B,U+1F810-1F847,U+1F850-1F859,U+1F860-1F887,U+1F890-1F8AD,U+1F8B0-1F8BB,U+1F8C0-1F8C1,U+1F900-1F90B,U+1F93B,U+1F946,U+1F984,U+1F996,U+1F9E9,U+1FA00-1FA6F,U+1FA70-1FA7C,U+1FA80-1FA89,U+1FA8F-1FAC6,U+1FACE-1FADC,U+1FADF-1FAE9,U+1FAF0-1FAF8,U+1FB00-1FBFF;\n}\n\n/* roboto-vietnamese-500-normal */\n@font-face {\n  font-family: 'Roboto';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 500;\n  src: url(./files/roboto-vietnamese-500-normal.woff2) format('woff2'), url(./files/roboto-vietnamese-500-normal.woff) format('woff');\n  unicode-range: U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB;\n}\n\n/* roboto-latin-ext-500-normal */\n@font-face {\n  font-family: 'Roboto';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 500;\n  src: url(./files/roboto-latin-ext-500-normal.woff2) format('woff2'), url(./files/roboto-latin-ext-500-normal.woff) format('woff');\n  unicode-range: U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF;\n}\n\n/* roboto-latin-500-normal */\n@font-face {\n  font-family: 'Roboto';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 500;\n  src: url(./files/roboto-latin-500-normal.woff2) format('woff2'), url(./files/roboto-latin-500-normal.woff) format('woff');\n  unicode-range: U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD;\n}", "/* roboto-cyrillic-ext-700-normal */\n@font-face {\n  font-family: 'Roboto';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 700;\n  src: url(./files/roboto-cyrillic-ext-700-normal.woff2) format('woff2'), url(./files/roboto-cyrillic-ext-700-normal.woff) format('woff');\n  unicode-range: U+0460-052F,U+1C80-1C8A,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F;\n}\n\n/* roboto-cyrillic-700-normal */\n@font-face {\n  font-family: 'Roboto';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 700;\n  src: url(./files/roboto-cyrillic-700-normal.woff2) format('woff2'), url(./files/roboto-cyrillic-700-normal.woff) format('woff');\n  unicode-range: U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116;\n}\n\n/* roboto-greek-ext-700-normal */\n@font-face {\n  font-family: 'Roboto';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 700;\n  src: url(./files/roboto-greek-ext-700-normal.woff2) format('woff2'), url(./files/roboto-greek-ext-700-normal.woff) format('woff');\n  unicode-range: U+1F00-1FFF;\n}\n\n/* roboto-greek-700-normal */\n@font-face {\n  font-family: 'Roboto';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 700;\n  src: url(./files/roboto-greek-700-normal.woff2) format('woff2'), url(./files/roboto-greek-700-normal.woff) format('woff');\n  unicode-range: U+0370-0377,U+037A-037F,U+0384-038A,U+038C,U+038E-03A1,U+03A3-03FF;\n}\n\n/* roboto-math-700-normal */\n@font-face {\n  font-family: 'Roboto';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 700;\n  src: url(./files/roboto-math-700-normal.woff2) format('woff2'), url(./files/roboto-math-700-normal.woff) format('woff');\n  unicode-range: U+0302-0303,U+0305,U+0307-0308,U+0310,U+0312,U+0315,U+031A,U+0326-0327,U+032C,U+032F-0330,U+0332-0333,U+0338,U+033A,U+0346,U+034D,U+0391-03A1,U+03A3-03A9,U+03B1-03C9,U+03D1,U+03D5-03D6,U+03F0-03F1,U+03F4-03F5,U+2016-2017,U+2034-2038,U+203C,U+2040,U+2043,U+2047,U+2050,U+2057,U+205F,U+2070-2071,U+2074-208E,U+2090-209C,U+20D0-20DC,U+20E1,U+20E5-20EF,U+2100-2112,U+2114-2115,U+2117-2121,U+2123-214F,U+2190,U+2192,U+2194-21AE,U+21B0-21E5,U+21F1-21F2,U+21F4-2211,U+2213-2214,U+2216-22FF,U+2308-230B,U+2310,U+2319,U+231C-2321,U+2336-237A,U+237C,U+2395,U+239B-23B7,U+23D0,U+23DC-23E1,U+2474-2475,U+25AF,U+25B3,U+25B7,U+25BD,U+25C1,U+25CA,U+25CC,U+25FB,U+266D-266F,U+27C0-27FF,U+2900-2AFF,U+2B0E-2B11,U+2B30-2B4C,U+2BFE,U+3030,U+FF5B,U+FF5D,U+1D400-1D7FF,U+1EE00-1EEFF;\n}\n\n/* roboto-symbols-700-normal */\n@font-face {\n  font-family: 'Roboto';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 700;\n  src: url(./files/roboto-symbols-700-normal.woff2) format('woff2'), url(./files/roboto-symbols-700-normal.woff) format('woff');\n  unicode-range: U+0001-000C,U+000E-001F,U+007F-009F,U+20DD-20E0,U+20E2-20E4,U+2150-218F,U+2190,U+2192,U+2194-2199,U+21AF,U+21E6-21F0,U+21F3,U+2218-2219,U+2299,U+22C4-22C6,U+2300-243F,U+2440-244A,U+2460-24FF,U+25A0-27BF,U+2800-28FF,U+2921-2922,U+2981,U+29BF,U+29EB,U+2B00-2BFF,U+4DC0-4DFF,U+FFF9-FFFB,U+10140-1018E,U+10190-1019C,U+101A0,U+101D0-101FD,U+102E0-102FB,U+10E60-10E7E,U+1D2C0-1D2D3,U+1D2E0-1D37F,U+1F000-1F0FF,U+1F100-1F1AD,U+1F1E6-1F1FF,U+1F30D-1F30F,U+1F315,U+1F31C,U+1F31E,U+1F320-1F32C,U+1F336,U+1F378,U+1F37D,U+1F382,U+1F393-1F39F,U+1F3A7-1F3A8,U+1F3AC-1F3AF,U+1F3C2,U+1F3C4-1F3C6,U+1F3CA-1F3CE,U+1F3D4-1F3E0,U+1F3ED,U+1F3F1-1F3F3,U+1F3F5-1F3F7,U+1F408,U+1F415,U+1F41F,U+1F426,U+1F43F,U+1F441-1F442,U+1F444,U+1F446-1F449,U+1F44C-1F44E,U+1F453,U+1F46A,U+1F47D,U+1F4A3,U+1F4B0,U+1F4B3,U+1F4B9,U+1F4BB,U+1F4BF,U+1F4C8-1F4CB,U+1F4D6,U+1F4DA,U+1F4DF,U+1F4E3-1F4E6,U+1F4EA-1F4ED,U+1F4F7,U+1F4F9-1F4FB,U+1F4FD-1F4FE,U+1F503,U+1F507-1F50B,U+1F50D,U+1F512-1F513,U+1F53E-1F54A,U+1F54F-1F5FA,U+1F610,U+1F650-1F67F,U+1F687,U+1F68D,U+1F691,U+1F694,U+1F698,U+1F6AD,U+1F6B2,U+1F6B9-1F6BA,U+1F6BC,U+1F6C6-1F6CF,U+1F6D3-1F6D7,U+1F6E0-1F6EA,U+1F6F0-1F6F3,U+1F6F7-1F6FC,U+1F700-1F7FF,U+1F800-1F80B,U+1F810-1F847,U+1F850-1F859,U+1F860-1F887,U+1F890-1F8AD,U+1F8B0-1F8BB,U+1F8C0-1F8C1,U+1F900-1F90B,U+1F93B,U+1F946,U+1F984,U+1F996,U+1F9E9,U+1FA00-1FA6F,U+1FA70-1FA7C,U+1FA80-1FA89,U+1FA8F-1FAC6,U+1FACE-1FADC,U+1FADF-1FAE9,U+1FAF0-1FAF8,U+1FB00-1FBFF;\n}\n\n/* roboto-vietnamese-700-normal */\n@font-face {\n  font-family: 'Roboto';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 700;\n  src: url(./files/roboto-vietnamese-700-normal.woff2) format('woff2'), url(./files/roboto-vietnamese-700-normal.woff) format('woff');\n  unicode-range: U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB;\n}\n\n/* roboto-latin-ext-700-normal */\n@font-face {\n  font-family: 'Roboto';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 700;\n  src: url(./files/roboto-latin-ext-700-normal.woff2) format('woff2'), url(./files/roboto-latin-ext-700-normal.woff) format('woff');\n  unicode-range: U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF;\n}\n\n/* roboto-latin-700-normal */\n@font-face {\n  font-family: 'Roboto';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 700;\n  src: url(./files/roboto-latin-700-normal.woff2) format('woff2'), url(./files/roboto-latin-700-normal.woff) format('woff');\n  unicode-range: U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD;\n}"], "names": [], "sourceRoot": ""}