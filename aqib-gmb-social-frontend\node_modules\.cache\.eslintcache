[{"C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\index.tsx": "1", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\reportWebVitals.ts": "2", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\App.tsx": "3", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\theme.tsx": "4", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\store\\index.tsx": "5", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\context\\toast.context.tsx": "6", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\context\\loading.context.tsx": "7", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\actions\\auth.actions.tsx": "8", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\constants\\toastSeverity.constant.tsx": "9", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\forgotPassword\\forgotPassword.screen.tsx": "10", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\signIn\\signIn.screen.tsx": "11", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\dashboard\\dashboard.screen.tsx": "12", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\qanda\\qanda.screen.tsx": "13", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\help\\help.screen.tsx": "14", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\analytics\\analytics.screen.tsx": "15", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\createSocialPost\\createSocialPost.screen.tsx": "16", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\dashboardV2\\dashboardV2.screen.tsx": "17", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessCategory\\businessCategory.screen.tsx": "18", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessCategory\\servicesDemo.screen.tsx": "19", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessCategory\\demo.screen.tsx": "20", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\geoGrid\\geoGrid.screen.tsx": "21", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\userManagement\\roles\\roles.screen.tsx": "22", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\userManagement\\users\\users.screen.tsx": "23", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessManagement\\manageBusiness\\manageBusiness.screen.tsx": "24", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessManagement\\businessSummary\\businessSummary.screen.tsx": "25", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessManagement\\localBusiness\\localBusiness.screen.tsx": "26", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessManagement\\callback\\callback.screen.tsx": "27", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\reviewManagement\\manageReviews\\manageReviews.screen.tsx": "28", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\reducers\\userPreferences.reducer.tsx": "29", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\reducers\\auth.reducer.tsx": "30", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\posts\\listing\\PostListing.screen.tsx": "31", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\loader\\loader.component.tsx": "32", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\unAuthorized\\notFoundPage.component.tsx": "33", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\notFoundPage\\notFoundPage.component.tsx": "34", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\constants\\endPoints.constant.tsx": "35", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\constants\\reducer.constant.tsx": "36", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\httpHelper.service.tsx": "37", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\helperService.tsx": "38", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\constants\\dbConstant.constant.tsx": "39", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\constants\\message.constant.tsx": "40", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\leftMenu\\leftMenu.component.tsx": "41", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\ApplicationHelperService.tsx": "42", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\dashboardV2\\websiteClicksChart.tsx": "43", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\dashboardV2\\platformBreakdownChart.tsx": "44", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\charts\\registeredEmployees.charts.tsx": "45", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\charts\\activeJobs.charts.tsx": "46", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\charts\\pie.charts.tsx": "47", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\homeChartCard\\homeChartCard.component.tsx": "48", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\qanda\\qanda.service.tsx": "49", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\business\\business.service.tsx": "50", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\location\\location.service.tsx": "51", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\replyQuestionAnswer\\replyQuestionAnswer.component.tsx": "52", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\genericDrawer\\genericDrawer.component.tsx": "53", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\revenueChartDashboard\\revenueChartDashboard.component.tsx": "54", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\constants\\application.constant.tsx": "55", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\locationChips\\locationChips.component.tsx": "56", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\dateFilter\\dateFilter.component.tsx": "57", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\locationMetrics\\locationMetrics.service.tsx": "58", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\createSocialPost\\components\\InfoCard.screen.tsx": "59", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\createSocialPost\\components\\submitPost.component.tsx": "60", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\dashboardV2\\searchBreakdown.tsx": "61", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\dashboardV2\\businessProfileInteractionsChart.tsx": "62", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\posts\\posts.service.tsx": "63", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessCategory\\components\\addBusinessCategory.component.tsx": "64", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessCategory\\components\\servicesDisplay.component.tsx": "65", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessCategory\\components\\addServices.component.tsx": "66", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessCategory\\components\\BusinessHours.tsx": "67", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessCategory\\components\\AddSpecialHours.tsx": "68", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\editBusinessName\\editBusinessName.component.tsx": "69", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\FromTheBusiness.tsx": "70", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\AddChildren.tsx": "71", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\MoreAccessibility.tsx": "72", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\Amenities.tsx": "73", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\ServiceOptions.tsx": "74", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\CrowdComponent.tsx": "75", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\ParkingComponent.tsx": "76", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\PaymentsComponent.tsx": "77", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\PlanningComponent.tsx": "78", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\geoGrid\\GeoGridControls.component.tsx": "79", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\geoGrid\\GeoGridMap.component.tsx": "80", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\geoGrid\\geoGrid.service.tsx": "81", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\geoGrid\\GeoGridSettings.component.tsx": "82", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\roles\\roles.service.tsx": "83", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\user\\user.service.tsx": "84", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\confirmModel\\confirmModel.component.tsx": "85", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\addEditUser\\addEditUser.component.tsx": "86", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\auth\\auth.service.tsx": "87", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\alertDialog\\alertDialog.component.tsx": "88", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\noRowsFound\\noRowsFound.component.tsx": "89", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\logosPhotosDisplay\\logsPhotosDisplay.component.tsx": "90", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\categoryDisplay\\categoryDisplay.component.tsx": "91", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\LinearProgressWithLabel\\LinearProgressWithLabel.component.tsx": "92", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\addEditBusiness\\addEditBusiness.component.tsx": "93", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\serviceItemsDisplay\\serviceItemsDisplay.component.tsx": "94", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\regularHoursTable\\regularHoursTable.component.tsx": "95", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\serviceAreaList\\serviceAreaList.component.tsx": "96", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\iconOnAvailability\\iconOnAvailability.component.tsx": "97", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\userAvatarWIthName\\userAvatarWIthName.component.tsx": "98", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\replyReviews\\replyReviews.component.tsx": "99", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\createPost\\createPost.component.tsx": "100", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\createTags\\createTags.component.tsx": "101", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\ratingsStar\\ratingsStar.component.tsx": "102", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\review\\review.service.tsx": "103", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\posts\\listing\\postCard\\postCard.screen.tsx": "104", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\posts\\updatesSection\\updatesSection.screen.tsx": "105", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\actions\\userPreferences.actions.tsx": "106", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\menuListItemNested\\menuListItemNested.component.tsx": "107", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\exportButton\\exportButton.component.tsx": "108", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\header\\header.component.tsx": "109", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\mediaGallery\\mediaGallery.component.tsx": "110", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\userAvatar\\userAvatar.component.tsx": "111", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\editElements\\editElements.component.tsx": "112", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\colorPalette\\colorPalette.component.tsx": "113", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\createPost\\createPostTemplates\\cards\\testimonialCard1\\testimonialCard1.component.tsx": "114", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\createPost\\createPostTemplates\\cards\\testimonialCard3\\testimonialCard3.component.tsx": "115", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\createPost\\createPostTemplates\\cards\\testimonialCard2\\testimonialCard2.component.tsx": "116", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\createPost\\createPostTemplates\\cards\\testimonialCard4\\testimonialCard4.component.tsx": "117", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\createPost\\createPostTemplates\\cards\\testimonialCard5\\testimonialCard5.component.tsx": "118", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\createPost\\createPostTemplates\\cards\\testimonialCard6\\testimonialCard6.component.tsx": "119", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\excelExport.service.ts": "120", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\context\\preferences.context.tsx": "121", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\utils\\googleMaps.utils.ts": "122", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\reviewManagement\\reviewSettings\\reviewSettings.screen.tsx": "123", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\reviewSettings\\reviewSettings.service.tsx": "124", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\reviewManagement\\reviewSettings\\components\\autoReplySettings.component.tsx": "125", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\reviewManagement\\reviewSettings\\components\\createEditTemplate.component.tsx": "126", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\manageAssets\\manageAssets.screen.tsx": "127", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\manageAssets\\manageAssets.service.ts": "128", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\fileGallery\\fileGallery.component.tsx": "129", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\fileUpload\\fileUpload.component.tsx": "130", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\fileViewer\\fileViewer.component.tsx": "131", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\confirmDelete\\confirmDelete.component.tsx": "132", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\utils\\fileUtils.ts": "133", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\manageAssets\\components\\confirmDelete.component.tsx": "134", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\manageAssets\\components\\fileUpload.component.tsx": "135", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\manageAssets\\components\\fileViewer.component.tsx": "136", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\manageAssets\\components\\fileGallery.component.tsx": "137", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\createSocialPost\\components\\gallerySelection.component.tsx": "138"}, {"size": 1322, "mtime": 1748969368507, "results": "139", "hashOfConfig": "140"}, {"size": 425, "mtime": 1748969368711, "results": "141", "hashOfConfig": "140"}, {"size": 12429, "mtime": 1748969367924, "results": "142", "hashOfConfig": "140"}, {"size": 1924, "mtime": 1748969177420, "results": "143", "hashOfConfig": "140"}, {"size": 1216, "mtime": 1748969371143, "results": "144", "hashOfConfig": "140"}, {"size": 438, "mtime": 1748969368495, "results": "145", "hashOfConfig": "140"}, {"size": 152, "mtime": 1748969368485, "results": "146", "hashOfConfig": "140"}, {"size": 2954, "mtime": 1748969367929, "results": "147", "hashOfConfig": "140"}, {"size": 112, "mtime": 1748969368479, "results": "148", "hashOfConfig": "140"}, {"size": 2586, "mtime": 1748969370932, "results": "149", "hashOfConfig": "140"}, {"size": 10740, "mtime": 1748969371025, "results": "150", "hashOfConfig": "140"}, {"size": 13928, "mtime": 1748969370904, "results": "151", "hashOfConfig": "140"}, {"size": 21620, "mtime": 1748969371012, "results": "152", "hashOfConfig": "140"}, {"size": 220, "mtime": 1748969370943, "results": "153", "hashOfConfig": "140"}, {"size": 72602, "mtime": 1748969179368, "results": "154", "hashOfConfig": "140"}, {"size": 78637, "mtime": 1749125517360, "results": "155", "hashOfConfig": "140"}, {"size": 23584, "mtime": 1748969370918, "results": "156", "hashOfConfig": "140"}, {"size": 4224, "mtime": 1748969368724, "results": "157", "hashOfConfig": "140"}, {"size": 2557, "mtime": 1748969370870, "results": "158", "hashOfConfig": "140"}, {"size": 92285, "mtime": 1748969370866, "results": "159", "hashOfConfig": "140"}, {"size": 12587, "mtime": 1748969370938, "results": "160", "hashOfConfig": "140"}, {"size": 37116, "mtime": 1748969179991, "results": "161", "hashOfConfig": "140"}, {"size": 22457, "mtime": 1748969180011, "results": "162", "hashOfConfig": "140"}, {"size": 25334, "mtime": 1748969370888, "results": "163", "hashOfConfig": "140"}, {"size": 56467, "mtime": 1748969179480, "results": "164", "hashOfConfig": "140"}, {"size": 30332, "mtime": 1748969179542, "results": "165", "hashOfConfig": "140"}, {"size": 4452, "mtime": 1748969370879, "results": "166", "hashOfConfig": "140"}, {"size": 43075, "mtime": 1748969179942, "results": "167", "hashOfConfig": "140"}, {"size": 951, "mtime": 1748969368707, "results": "168", "hashOfConfig": "140"}, {"size": 1730, "mtime": 1748969368697, "results": "169", "hashOfConfig": "140"}, {"size": 18225, "mtime": 1748969179861, "results": "170", "hashOfConfig": "140"}, {"size": 347, "mtime": 1748969178541, "results": "171", "hashOfConfig": "140"}, {"size": 1818, "mtime": 1748969368442, "results": "172", "hashOfConfig": "140"}, {"size": 1814, "mtime": 1748969368320, "results": "173", "hashOfConfig": "140"}, {"size": 5728, "mtime": 1749064599635, "results": "174", "hashOfConfig": "140"}, {"size": 370, "mtime": 1748969368473, "results": "175", "hashOfConfig": "140"}, {"size": 8994, "mtime": 1748969371088, "results": "176", "hashOfConfig": "140"}, {"size": 1053, "mtime": 1748969371080, "results": "177", "hashOfConfig": "140"}, {"size": 307, "mtime": 1748969368464, "results": "178", "hashOfConfig": "140"}, {"size": 1953, "mtime": 1748969178987, "results": "179", "hashOfConfig": "140"}, {"size": 21434, "mtime": 1749108279836, "results": "180", "hashOfConfig": "140"}, {"size": 3091, "mtime": 1748969371041, "results": "181", "hashOfConfig": "140"}, {"size": 3899, "mtime": 1748969179700, "results": "182", "hashOfConfig": "140"}, {"size": 4028, "mtime": 1748969179684, "results": "183", "hashOfConfig": "140"}, {"size": 1918, "mtime": 1748969367988, "results": "184", "hashOfConfig": "140"}, {"size": 1129, "mtime": 1748969367971, "results": "185", "hashOfConfig": "140"}, {"size": 977, "mtime": 1748969367982, "results": "186", "hashOfConfig": "140"}, {"size": 2779, "mtime": 1748969178435, "results": "187", "hashOfConfig": "140"}, {"size": 1388, "mtime": 1748969371120, "results": "188", "hashOfConfig": "140"}, {"size": 2038, "mtime": 1748969371058, "results": "189", "hashOfConfig": "140"}, {"size": 1631, "mtime": 1748969371101, "results": "190", "hashOfConfig": "140"}, {"size": 4825, "mtime": 1748969368417, "results": "191", "hashOfConfig": "140"}, {"size": 971, "mtime": 1748969368089, "results": "192", "hashOfConfig": "140"}, {"size": 4015, "mtime": 1748969178808, "results": "193", "hashOfConfig": "140"}, {"size": 2046, "mtime": 1748969368459, "results": "194", "hashOfConfig": "140"}, {"size": 3524, "mtime": 1748969368215, "results": "195", "hashOfConfig": "140"}, {"size": 6531, "mtime": 1748969178236, "results": "196", "hashOfConfig": "140"}, {"size": 1027, "mtime": 1748969371107, "results": "197", "hashOfConfig": "140"}, {"size": 3176, "mtime": 1748969179606, "results": "198", "hashOfConfig": "140"}, {"size": 42714, "mtime": 1749023567819, "results": "199", "hashOfConfig": "140"}, {"size": 7778, "mtime": 1748969370923, "results": "200", "hashOfConfig": "140"}, {"size": 4054, "mtime": 1748969179667, "results": "201", "hashOfConfig": "140"}, {"size": 2915, "mtime": 1749064644177, "results": "202", "hashOfConfig": "140"}, {"size": 1521, "mtime": 1748969370847, "results": "203", "hashOfConfig": "140"}, {"size": 7403, "mtime": 1748969370859, "results": "204", "hashOfConfig": "140"}, {"size": 8129, "mtime": 1748969370854, "results": "205", "hashOfConfig": "140"}, {"size": 12230, "mtime": 1748969368737, "results": "206", "hashOfConfig": "140"}, {"size": 9701, "mtime": 1748969368730, "results": "207", "hashOfConfig": "140"}, {"size": 7411, "mtime": 1748969368059, "results": "208", "hashOfConfig": "140"}, {"size": 7645, "mtime": 1748969370963, "results": "209", "hashOfConfig": "140"}, {"size": 7764, "mtime": 1748969370948, "results": "210", "hashOfConfig": "140"}, {"size": 8438, "mtime": 1748969370968, "results": "211", "hashOfConfig": "140"}, {"size": 9418, "mtime": 1748969370953, "results": "212", "hashOfConfig": "140"}, {"size": 8390, "mtime": 1748969370988, "results": "213", "hashOfConfig": "140"}, {"size": 7694, "mtime": 1748969370958, "results": "214", "hashOfConfig": "140"}, {"size": 8842, "mtime": 1748969370973, "results": "215", "hashOfConfig": "140"}, {"size": 8910, "mtime": 1748969370978, "results": "216", "hashOfConfig": "140"}, {"size": 9753, "mtime": 1748969370983, "results": "217", "hashOfConfig": "140"}, {"size": 16883, "mtime": 1748969368096, "results": "218", "hashOfConfig": "140"}, {"size": 8642, "mtime": 1748969368161, "results": "219", "hashOfConfig": "140"}, {"size": 3621, "mtime": 1748969371074, "results": "220", "hashOfConfig": "140"}, {"size": 6848, "mtime": 1748969368169, "results": "221", "hashOfConfig": "140"}, {"size": 723, "mtime": 1748969371134, "results": "222", "hashOfConfig": "140"}, {"size": 2655, "mtime": 1748969180196, "results": "223", "hashOfConfig": "140"}, {"size": 2179, "mtime": 1748969177971, "results": "224", "hashOfConfig": "140"}, {"size": 62073, "mtime": 1748969177860, "results": "225", "hashOfConfig": "140"}, {"size": 847, "mtime": 1748969371051, "results": "226", "hashOfConfig": "140"}, {"size": 3505, "mtime": 1748969177878, "results": "227", "hashOfConfig": "140"}, {"size": 825, "mtime": 1748969178665, "results": "228", "hashOfConfig": "140"}, {"size": 4702, "mtime": 1748969368221, "results": "229", "hashOfConfig": "140"}, {"size": 3568, "mtime": 1748969367959, "results": "230", "hashOfConfig": "140"}, {"size": 1109, "mtime": 1748969367946, "results": "231", "hashOfConfig": "140"}, {"size": 9176, "mtime": 1748969177843, "results": "232", "hashOfConfig": "140"}, {"size": 2262, "mtime": 1748969368435, "results": "233", "hashOfConfig": "140"}, {"size": 3305, "mtime": 1748969178736, "results": "234", "hashOfConfig": "140"}, {"size": 1607, "mtime": 1748969368429, "results": "235", "hashOfConfig": "140"}, {"size": 583, "mtime": 1748969368196, "results": "236", "hashOfConfig": "140"}, {"size": 463, "mtime": 1748969368454, "results": "237", "hashOfConfig": "140"}, {"size": 6596, "mtime": 1748969178782, "results": "238", "hashOfConfig": "140"}, {"size": 14893, "mtime": 1748969368007, "results": "239", "hashOfConfig": "140"}, {"size": 8516, "mtime": 1748969178205, "results": "240", "hashOfConfig": "140"}, {"size": 1147, "mtime": 1748969368411, "results": "241", "hashOfConfig": "140"}, {"size": 2167, "mtime": 1748969371126, "results": "242", "hashOfConfig": "140"}, {"size": 12145, "mtime": 1748970904670, "results": "243", "hashOfConfig": "140"}, {"size": 3329, "mtime": 1748969371000, "results": "244", "hashOfConfig": "140"}, {"size": 485, "mtime": 1748969367939, "results": "245", "hashOfConfig": "140"}, {"size": 5996, "mtime": 1748969368238, "results": "246", "hashOfConfig": "140"}, {"size": 2617, "mtime": 1748969368072, "results": "247", "hashOfConfig": "140"}, {"size": 3305, "mtime": 1748969368191, "results": "248", "hashOfConfig": "140"}, {"size": 2809, "mtime": 1748969368226, "results": "249", "hashOfConfig": "140"}, {"size": 1348, "mtime": 1748969368448, "results": "250", "hashOfConfig": "140"}, {"size": 5636, "mtime": 1748969368063, "results": "251", "hashOfConfig": "140"}, {"size": 9514, "mtime": 1748969367995, "results": "252", "hashOfConfig": "140"}, {"size": 2827, "mtime": 1748969368012, "results": "253", "hashOfConfig": "140"}, {"size": 4537, "mtime": 1748969368026, "results": "254", "hashOfConfig": "140"}, {"size": 2097, "mtime": 1748969368019, "results": "255", "hashOfConfig": "140"}, {"size": 3140, "mtime": 1748969368038, "results": "256", "hashOfConfig": "140"}, {"size": 3321, "mtime": 1748969368045, "results": "257", "hashOfConfig": "140"}, {"size": 3000, "mtime": 1748969368053, "results": "258", "hashOfConfig": "140"}, {"size": 13705, "mtime": 1748969371067, "results": "259", "hashOfConfig": "140"}, {"size": 163, "mtime": 1748969368490, "results": "260", "hashOfConfig": "140"}, {"size": 4815, "mtime": 1748969371157, "results": "261", "hashOfConfig": "140"}, {"size": 16593, "mtime": 1748842579937, "results": "262", "hashOfConfig": "140"}, {"size": 4220, "mtime": 1748804384121, "results": "263", "hashOfConfig": "140"}, {"size": 15590, "mtime": 1748806340914, "results": "264", "hashOfConfig": "140"}, {"size": 11543, "mtime": 1748843169061, "results": "265", "hashOfConfig": "140"}, {"size": 15944, "mtime": 1748941966068, "results": "266", "hashOfConfig": "140"}, {"size": 3056, "mtime": 1748931206364, "results": "267", "hashOfConfig": "140"}, {"size": 8554, "mtime": 1748931169711, "results": "268", "hashOfConfig": "140"}, {"size": 10503, "mtime": 1748931116654, "results": "269", "hashOfConfig": "140"}, {"size": 8369, "mtime": 1748931329681, "results": "270", "hashOfConfig": "140"}, {"size": 2576, "mtime": 1748887662915, "results": "271", "hashOfConfig": "140"}, {"size": 2526, "mtime": 1748931004554, "results": "272", "hashOfConfig": "140"}, {"size": 1469, "mtime": 1748934891273, "results": "273", "hashOfConfig": "140"}, {"size": 7042, "mtime": 1748934815415, "results": "274", "hashOfConfig": "140"}, {"size": 7395, "mtime": 1748937395633, "results": "275", "hashOfConfig": "140"}, {"size": 8292, "mtime": 1748937372532, "results": "276", "hashOfConfig": "140"}, {"size": 12263, "mtime": 1749119666354, "results": "277", "hashOfConfig": "140"}, {"filePath": "278", "messages": "279", "suppressedMessages": "280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "qj6s6v", {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "284", "messages": "285", "suppressedMessages": "286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 19, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "287", "messages": "288", "suppressedMessages": "289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "290", "messages": "291", "suppressedMessages": "292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "293", "messages": "294", "suppressedMessages": "295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "296", "messages": "297", "suppressedMessages": "298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "299", "messages": "300", "suppressedMessages": "301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "302", "messages": "303", "suppressedMessages": "304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "305", "messages": "306", "suppressedMessages": "307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "308", "messages": "309", "suppressedMessages": "310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "311", "messages": "312", "suppressedMessages": "313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "314", "messages": "315", "suppressedMessages": "316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "317", "messages": "318", "suppressedMessages": "319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "320", "messages": "321", "suppressedMessages": "322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "323", "messages": "324", "suppressedMessages": "325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 21, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "326", "messages": "327", "suppressedMessages": "328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 22, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "329", "messages": "330", "suppressedMessages": "331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "332", "messages": "333", "suppressedMessages": "334", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "335", "messages": "336", "suppressedMessages": "337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "338", "messages": "339", "suppressedMessages": "340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "341", "messages": "342", "suppressedMessages": "343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "344", "messages": "345", "suppressedMessages": "346", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "347", "messages": "348", "suppressedMessages": "349", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "350", "messages": "351", "suppressedMessages": "352", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "353", "messages": "354", "suppressedMessages": "355", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "356", "messages": "357", "suppressedMessages": "358", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "359", "messages": "360", "suppressedMessages": "361", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 29, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "362", "messages": "363", "suppressedMessages": "364", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "365", "messages": "366", "suppressedMessages": "367", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "368", "messages": "369", "suppressedMessages": "370", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "371", "messages": "372", "suppressedMessages": "373", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "374", "messages": "375", "suppressedMessages": "376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "377", "messages": "378", "suppressedMessages": "379", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "380", "messages": "381", "suppressedMessages": "382", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "383", "messages": "384", "suppressedMessages": "385", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "386", "messages": "387", "suppressedMessages": "388", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "389", "messages": "390", "suppressedMessages": "391", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "392", "messages": "393", "suppressedMessages": "394", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "395", "messages": "396", "suppressedMessages": "397", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "398", "messages": "399", "suppressedMessages": "400", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 18, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "401", "messages": "402", "suppressedMessages": "403", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "404", "messages": "405", "suppressedMessages": "406", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "407", "messages": "408", "suppressedMessages": "409", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "410", "messages": "411", "suppressedMessages": "412", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "413", "messages": "414", "suppressedMessages": "415", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "416", "messages": "417", "suppressedMessages": "418", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "419", "messages": "420", "suppressedMessages": "421", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "422", "messages": "423", "suppressedMessages": "424", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "425", "messages": "426", "suppressedMessages": "427", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "428", "messages": "429", "suppressedMessages": "430", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "431", "messages": "432", "suppressedMessages": "433", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "434", "messages": "435", "suppressedMessages": "436", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "437", "messages": "438", "suppressedMessages": "439", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "440", "messages": "441", "suppressedMessages": "442", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "443", "messages": "444", "suppressedMessages": "445", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "446", "messages": "447", "suppressedMessages": "448", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "449", "messages": "450", "suppressedMessages": "451", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "452", "messages": "453", "suppressedMessages": "454", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "455", "messages": "456", "suppressedMessages": "457", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 33, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "458", "messages": "459", "suppressedMessages": "460", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "461", "messages": "462", "suppressedMessages": "463", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "464", "messages": "465", "suppressedMessages": "466", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "467", "messages": "468", "suppressedMessages": "469", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 20, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "470", "messages": "471", "suppressedMessages": "472", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "473", "messages": "474", "suppressedMessages": "475", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "476", "messages": "477", "suppressedMessages": "478", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "479", "messages": "480", "suppressedMessages": "481", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "482", "messages": "483", "suppressedMessages": "484", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "485", "messages": "486", "suppressedMessages": "487", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "488", "messages": "489", "suppressedMessages": "490", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "491", "messages": "492", "suppressedMessages": "493", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "494", "messages": "495", "suppressedMessages": "496", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "497", "messages": "498", "suppressedMessages": "499", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "500", "messages": "501", "suppressedMessages": "502", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "503", "messages": "504", "suppressedMessages": "505", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "506", "messages": "507", "suppressedMessages": "508", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "509", "messages": "510", "suppressedMessages": "511", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "512", "messages": "513", "suppressedMessages": "514", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "515", "messages": "516", "suppressedMessages": "517", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "518", "messages": "519", "suppressedMessages": "520", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "521", "messages": "522", "suppressedMessages": "523", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "524", "messages": "525", "suppressedMessages": "526", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "527", "messages": "528", "suppressedMessages": "529", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "530", "messages": "531", "suppressedMessages": "532", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "533", "messages": "534", "suppressedMessages": "535", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "536", "messages": "537", "suppressedMessages": "538", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "539", "messages": "540", "suppressedMessages": "541", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "542", "messages": "543", "suppressedMessages": "544", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "545", "messages": "546", "suppressedMessages": "547", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "548", "messages": "549", "suppressedMessages": "550", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "551", "messages": "552", "suppressedMessages": "553", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "554", "messages": "555", "suppressedMessages": "556", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "557", "messages": "558", "suppressedMessages": "559", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "560", "messages": "561", "suppressedMessages": "562", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "563", "messages": "564", "suppressedMessages": "565", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "566", "messages": "567", "suppressedMessages": "568", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "569", "messages": "570", "suppressedMessages": "571", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "572", "messages": "573", "suppressedMessages": "574", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "575", "messages": "576", "suppressedMessages": "577", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "578", "messages": "579", "suppressedMessages": "580", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "581", "messages": "582", "suppressedMessages": "583", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "584", "messages": "585", "suppressedMessages": "586", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "587", "messages": "588", "suppressedMessages": "589", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "590", "messages": "591", "suppressedMessages": "592", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "593", "messages": "594", "suppressedMessages": "595", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "596", "messages": "597", "suppressedMessages": "598", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "599", "messages": "600", "suppressedMessages": "601", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "602", "messages": "603", "suppressedMessages": "604", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "605", "messages": "606", "suppressedMessages": "607", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "608", "messages": "609", "suppressedMessages": "610", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "611", "messages": "612", "suppressedMessages": "613", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "614", "messages": "615", "suppressedMessages": "616", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "617", "messages": "618", "suppressedMessages": "619", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "620", "messages": "621", "suppressedMessages": "622", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "623", "messages": "624", "suppressedMessages": "625", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "626", "messages": "627", "suppressedMessages": "628", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "629", "messages": "630", "suppressedMessages": "631", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "632", "messages": "633", "suppressedMessages": "634", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "635", "messages": "636", "suppressedMessages": "637", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "638", "messages": "639", "suppressedMessages": "640", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "641", "messages": "642", "suppressedMessages": "643", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "644", "messages": "645", "suppressedMessages": "646", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "647", "messages": "648", "suppressedMessages": "649", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "650", "messages": "651", "suppressedMessages": "652", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "653", "messages": "654", "suppressedMessages": "655", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "656", "messages": "657", "suppressedMessages": "658", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "659", "messages": "660", "suppressedMessages": "661", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "662", "messages": "663", "suppressedMessages": "664", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "665", "messages": "666", "suppressedMessages": "667", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "668", "messages": "669", "suppressedMessages": "670", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "671", "messages": "672", "suppressedMessages": "673", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "674", "messages": "675", "suppressedMessages": "676", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "677", "messages": "678", "suppressedMessages": "679", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "680", "messages": "681", "suppressedMessages": "682", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "683", "messages": "684", "suppressedMessages": "685", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "686", "messages": "687", "suppressedMessages": "688", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "689", "messages": "690", "suppressedMessages": "691", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\reportWebVitals.ts", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\App.tsx", ["692", "693", "694", "695", "696", "697", "698", "699", "700", "701", "702", "703", "704", "705", "706", "707", "708", "709", "710"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\theme.tsx", ["711"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\store\\index.tsx", ["712"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\context\\toast.context.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\context\\loading.context.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\actions\\auth.actions.tsx", ["713", "714", "715", "716", "717", "718"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\constants\\toastSeverity.constant.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\forgotPassword\\forgotPassword.screen.tsx", ["719"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\signIn\\signIn.screen.tsx", ["720", "721", "722", "723", "724", "725", "726", "727", "728"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\dashboard\\dashboard.screen.tsx", ["729"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\qanda\\qanda.screen.tsx", ["730", "731", "732", "733", "734", "735", "736", "737", "738", "739", "740", "741", "742"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\help\\help.screen.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\analytics\\analytics.screen.tsx", ["743", "744", "745", "746"], ["747"], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\createSocialPost\\createSocialPost.screen.tsx", ["748", "749", "750", "751", "752", "753", "754", "755", "756", "757", "758", "759", "760", "761", "762", "763", "764", "765", "766", "767", "768"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\dashboardV2\\dashboardV2.screen.tsx", ["769", "770", "771", "772", "773", "774", "775", "776", "777", "778", "779", "780", "781", "782", "783", "784", "785", "786", "787", "788", "789", "790"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessCategory\\businessCategory.screen.tsx", ["791"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessCategory\\servicesDemo.screen.tsx", ["792", "793", "794"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessCategory\\demo.screen.tsx", ["795", "796", "797", "798", "799", "800"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\geoGrid\\geoGrid.screen.tsx", ["801", "802", "803", "804", "805"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\userManagement\\roles\\roles.screen.tsx", ["806", "807", "808", "809", "810", "811", "812", "813", "814", "815"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\userManagement\\users\\users.screen.tsx", ["816", "817", "818", "819", "820", "821", "822", "823", "824", "825", "826", "827", "828"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessManagement\\manageBusiness\\manageBusiness.screen.tsx", ["829", "830", "831", "832", "833", "834", "835", "836", "837", "838", "839", "840", "841"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessManagement\\businessSummary\\businessSummary.screen.tsx", ["842", "843", "844", "845", "846", "847", "848", "849", "850", "851", "852", "853", "854", "855", "856", "857", "858"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessManagement\\localBusiness\\localBusiness.screen.tsx", ["859", "860", "861", "862", "863", "864", "865", "866", "867", "868"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessManagement\\callback\\callback.screen.tsx", ["869", "870", "871", "872"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\reviewManagement\\manageReviews\\manageReviews.screen.tsx", ["873", "874", "875", "876", "877", "878", "879", "880", "881", "882", "883", "884", "885", "886", "887", "888", "889", "890", "891", "892", "893", "894", "895", "896", "897", "898", "899", "900", "901"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\reducers\\userPreferences.reducer.tsx", ["902", "903", "904", "905", "906", "907", "908"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\reducers\\auth.reducer.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\posts\\listing\\PostListing.screen.tsx", ["909", "910", "911", "912", "913", "914", "915", "916", "917", "918", "919", "920", "921", "922", "923"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\loader\\loader.component.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\unAuthorized\\notFoundPage.component.tsx", ["924"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\notFoundPage\\notFoundPage.component.tsx", ["925"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\constants\\endPoints.constant.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\constants\\reducer.constant.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\httpHelper.service.tsx", ["926", "927"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\helperService.tsx", ["928", "929"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\constants\\dbConstant.constant.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\constants\\message.constant.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\leftMenu\\leftMenu.component.tsx", ["930", "931", "932", "933", "934", "935", "936", "937", "938", "939", "940", "941", "942", "943", "944", "945", "946", "947"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\ApplicationHelperService.tsx", ["948", "949"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\dashboardV2\\websiteClicksChart.tsx", ["950", "951"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\dashboardV2\\platformBreakdownChart.tsx", ["952"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\charts\\registeredEmployees.charts.tsx", ["953", "954", "955"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\charts\\activeJobs.charts.tsx", ["956"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\charts\\pie.charts.tsx", ["957", "958"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\homeChartCard\\homeChartCard.component.tsx", ["959", "960"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\qanda\\qanda.service.tsx", ["961"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\business\\business.service.tsx", ["962"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\location\\location.service.tsx", ["963"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\replyQuestionAnswer\\replyQuestionAnswer.component.tsx", ["964", "965", "966"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\genericDrawer\\genericDrawer.component.tsx", ["967", "968", "969", "970"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\revenueChartDashboard\\revenueChartDashboard.component.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\constants\\application.constant.tsx", ["971"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\locationChips\\locationChips.component.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\dateFilter\\dateFilter.component.tsx", ["972"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\locationMetrics\\locationMetrics.service.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\createSocialPost\\components\\InfoCard.screen.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\createSocialPost\\components\\submitPost.component.tsx", ["973", "974", "975", "976", "977", "978", "979", "980", "981", "982", "983", "984", "985", "986", "987", "988", "989", "990", "991", "992", "993", "994", "995", "996", "997", "998", "999", "1000", "1001", "1002", "1003", "1004", "1005"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\dashboardV2\\searchBreakdown.tsx", ["1006", "1007", "1008", "1009", "1010"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\dashboardV2\\businessProfileInteractionsChart.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\posts\\posts.service.tsx", ["1011", "1012", "1013"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessCategory\\components\\addBusinessCategory.component.tsx", ["1014", "1015", "1016", "1017", "1018", "1019", "1020", "1021", "1022", "1023", "1024", "1025", "1026", "1027", "1028", "1029", "1030", "1031", "1032", "1033"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessCategory\\components\\servicesDisplay.component.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessCategory\\components\\addServices.component.tsx", ["1034"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessCategory\\components\\BusinessHours.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessCategory\\components\\AddSpecialHours.tsx", ["1035", "1036", "1037", "1038", "1039"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\editBusinessName\\editBusinessName.component.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\FromTheBusiness.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\AddChildren.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\MoreAccessibility.tsx", ["1040"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\Amenities.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\ServiceOptions.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\CrowdComponent.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\ParkingComponent.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\PaymentsComponent.tsx", ["1041"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\PlanningComponent.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\geoGrid\\GeoGridControls.component.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\geoGrid\\GeoGridMap.component.tsx", ["1042", "1043"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\geoGrid\\geoGrid.service.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\geoGrid\\GeoGridSettings.component.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\roles\\roles.service.tsx", ["1044", "1045"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\user\\user.service.tsx", ["1046", "1047", "1048"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\confirmModel\\confirmModel.component.tsx", ["1049", "1050"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\addEditUser\\addEditUser.component.tsx", ["1051", "1052", "1053", "1054", "1055", "1056", "1057", "1058", "1059", "1060", "1061", "1062"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\auth\\auth.service.tsx", ["1063", "1064"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\alertDialog\\alertDialog.component.tsx", ["1065"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\noRowsFound\\noRowsFound.component.tsx", ["1066", "1067", "1068"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\logosPhotosDisplay\\logsPhotosDisplay.component.tsx", ["1069", "1070", "1071"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\categoryDisplay\\categoryDisplay.component.tsx", ["1072", "1073", "1074", "1075"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\LinearProgressWithLabel\\LinearProgressWithLabel.component.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\addEditBusiness\\addEditBusiness.component.tsx", ["1076", "1077", "1078", "1079", "1080", "1081", "1082", "1083", "1084", "1085", "1086"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\serviceItemsDisplay\\serviceItemsDisplay.component.tsx", ["1087", "1088", "1089", "1090", "1091"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\regularHoursTable\\regularHoursTable.component.tsx", ["1092", "1093", "1094", "1095", "1096", "1097", "1098", "1099", "1100", "1101", "1102"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\serviceAreaList\\serviceAreaList.component.tsx", ["1103", "1104", "1105"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\iconOnAvailability\\iconOnAvailability.component.tsx", ["1106", "1107", "1108"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\userAvatarWIthName\\userAvatarWIthName.component.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\replyReviews\\replyReviews.component.tsx", ["1109", "1110", "1111", "1112"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\createPost\\createPost.component.tsx", ["1113", "1114", "1115", "1116", "1117", "1118", "1119", "1120", "1121", "1122", "1123", "1124", "1125"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\createTags\\createTags.component.tsx", ["1126", "1127", "1128", "1129", "1130", "1131"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\ratingsStar\\ratingsStar.component.tsx", ["1132", "1133", "1134"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\review\\review.service.tsx", ["1135"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\posts\\listing\\postCard\\postCard.screen.tsx", ["1136", "1137", "1138"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\posts\\updatesSection\\updatesSection.screen.tsx", ["1139", "1140"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\actions\\userPreferences.actions.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\menuListItemNested\\menuListItemNested.component.tsx", ["1141", "1142", "1143"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\exportButton\\exportButton.component.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\header\\header.component.tsx", ["1144", "1145", "1146", "1147", "1148", "1149", "1150", "1151", "1152", "1153"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\mediaGallery\\mediaGallery.component.tsx", ["1154", "1155"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\userAvatar\\userAvatar.component.tsx", ["1156"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\editElements\\editElements.component.tsx", ["1157", "1158", "1159"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\colorPalette\\colorPalette.component.tsx", ["1160", "1161", "1162"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\createPost\\createPostTemplates\\cards\\testimonialCard1\\testimonialCard1.component.tsx", ["1163"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\createPost\\createPostTemplates\\cards\\testimonialCard3\\testimonialCard3.component.tsx", ["1164", "1165", "1166", "1167"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\createPost\\createPostTemplates\\cards\\testimonialCard2\\testimonialCard2.component.tsx", ["1168", "1169", "1170"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\createPost\\createPostTemplates\\cards\\testimonialCard4\\testimonialCard4.component.tsx", ["1171", "1172", "1173", "1174"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\createPost\\createPostTemplates\\cards\\testimonialCard5\\testimonialCard5.component.tsx", ["1175", "1176", "1177", "1178", "1179"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\createPost\\createPostTemplates\\cards\\testimonialCard6\\testimonialCard6.component.tsx", ["1180", "1181", "1182", "1183"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\excelExport.service.ts", ["1184"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\context\\preferences.context.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\utils\\googleMaps.utils.ts", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\reviewManagement\\reviewSettings\\reviewSettings.screen.tsx", ["1185", "1186", "1187", "1188", "1189", "1190", "1191", "1192", "1193", "1194", "1195", "1196", "1197"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\reviewSettings\\reviewSettings.service.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\reviewManagement\\reviewSettings\\components\\autoReplySettings.component.tsx", ["1198", "1199"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\reviewManagement\\reviewSettings\\components\\createEditTemplate.component.tsx", ["1200", "1201"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\manageAssets\\manageAssets.screen.tsx", ["1202", "1203", "1204", "1205", "1206", "1207", "1208", "1209"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\manageAssets\\manageAssets.service.ts", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\fileGallery\\fileGallery.component.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\fileUpload\\fileUpload.component.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\fileViewer\\fileViewer.component.tsx", ["1210"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\confirmDelete\\confirmDelete.component.tsx", ["1211"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\utils\\fileUtils.ts", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\manageAssets\\components\\confirmDelete.component.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\manageAssets\\components\\fileUpload.component.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\manageAssets\\components\\fileViewer.component.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\manageAssets\\components\\fileGallery.component.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\createSocialPost\\components\\gallerySelection.component.tsx", ["1212", "1213", "1214", "1215", "1216", "1217"], [], {"ruleId": "1218", "severity": 1, "message": "1219", "line": 3, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 3, "endColumn": 13}, {"ruleId": "1218", "severity": 1, "message": "1222", "line": 9, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 9, "endColumn": 12}, {"ruleId": "1218", "severity": 1, "message": "1223", "line": 11, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 11, "endColumn": 14}, {"ruleId": "1218", "severity": 1, "message": "1224", "line": 12, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 12, "endColumn": 17}, {"ruleId": "1218", "severity": 1, "message": "1225", "line": 13, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 13, "endColumn": 22}, {"ruleId": "1218", "severity": 1, "message": "1226", "line": 16, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 16, "endColumn": 28}, {"ruleId": "1218", "severity": 1, "message": "1227", "line": 19, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 19, "endColumn": 15}, {"ruleId": "1218", "severity": 1, "message": "1228", "line": 61, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 61, "endColumn": 24}, {"ruleId": "1218", "severity": 1, "message": "1229", "line": 61, "column": 26, "nodeType": "1220", "messageId": "1221", "endLine": 61, "endColumn": 43}, {"ruleId": "1230", "severity": 1, "message": "1231", "line": 77, "column": 5, "nodeType": "1232", "endLine": 77, "endColumn": 14, "suggestions": "1233"}, {"ruleId": "1230", "severity": 1, "message": "1234", "line": 85, "column": 5, "nodeType": "1232", "endLine": 85, "endColumn": 14, "suggestions": "1235"}, {"ruleId": "1218", "severity": 1, "message": "1236", "line": 96, "column": 9, "nodeType": "1220", "messageId": "1221", "endLine": 96, "endColumn": 26}, {"ruleId": "1230", "severity": 1, "message": "1237", "line": 100, "column": 5, "nodeType": "1232", "endLine": 100, "endColumn": 21, "suggestions": "1238"}, {"ruleId": "1230", "severity": 1, "message": "1239", "line": 128, "column": 6, "nodeType": "1232", "endLine": 128, "endColumn": 25, "suggestions": "1240"}, {"ruleId": "1230", "severity": 1, "message": "1241", "line": 139, "column": 6, "nodeType": "1232", "endLine": 139, "endColumn": 22, "suggestions": "1242"}, {"ruleId": "1230", "severity": 1, "message": "1243", "line": 163, "column": 5, "nodeType": "1232", "endLine": 163, "endColumn": 11, "suggestions": "1244"}, {"ruleId": "1230", "severity": 1, "message": "1243", "line": 170, "column": 5, "nodeType": "1232", "endLine": 170, "endColumn": 11, "suggestions": "1245"}, {"ruleId": "1230", "severity": 1, "message": "1246", "line": 177, "column": 5, "nodeType": "1232", "endLine": 177, "endColumn": 18, "suggestions": "1247"}, {"ruleId": "1230", "severity": 1, "message": "1248", "line": 189, "column": 5, "nodeType": "1232", "endLine": 189, "endColumn": 33, "suggestions": "1249"}, {"ruleId": "1218", "severity": 1, "message": "1250", "line": 3, "column": 24, "nodeType": "1220", "messageId": "1221", "endLine": 3, "endColumn": 38}, {"ruleId": "1218", "severity": 1, "message": "1251", "line": 6, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 6, "endColumn": 21}, {"ruleId": "1218", "severity": 1, "message": "1252", "line": 1, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 1, "endColumn": 15}, {"ruleId": "1218", "severity": 1, "message": "1219", "line": 4, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 4, "endColumn": 20}, {"ruleId": "1218", "severity": 1, "message": "1253", "line": 4, "column": 22, "nodeType": "1220", "messageId": "1221", "endLine": 4, "endColumn": 31}, {"ruleId": "1218", "severity": 1, "message": "1254", "line": 12, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 12, "endColumn": 13}, {"ruleId": "1218", "severity": 1, "message": "1255", "line": 14, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 14, "endColumn": 19}, {"ruleId": "1218", "severity": 1, "message": "1256", "line": 18, "column": 7, "nodeType": "1220", "messageId": "1221", "endLine": 18, "endColumn": 32}, {"ruleId": "1218", "severity": 1, "message": "1257", "line": 11, "column": 13, "nodeType": "1220", "messageId": "1221", "endLine": 11, "endColumn": 16}, {"ruleId": "1218", "severity": 1, "message": "1258", "line": 12, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 12, "endColumn": 12}, {"ruleId": "1218", "severity": 1, "message": "1259", "line": 16, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 16, "endColumn": 19}, {"ruleId": "1218", "severity": 1, "message": "1260", "line": 20, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 20, "endColumn": 19}, {"ruleId": "1218", "severity": 1, "message": "1261", "line": 21, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 21, "endColumn": 18}, {"ruleId": "1218", "severity": 1, "message": "1262", "line": 38, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 38, "endColumn": 26}, {"ruleId": "1218", "severity": 1, "message": "1263", "line": 42, "column": 27, "nodeType": "1220", "messageId": "1221", "endLine": 42, "endColumn": 34}, {"ruleId": "1218", "severity": 1, "message": "1264", "line": 45, "column": 9, "nodeType": "1220", "messageId": "1221", "endLine": 45, "endColumn": 32}, {"ruleId": "1218", "severity": 1, "message": "1265", "line": 51, "column": 9, "nodeType": "1220", "messageId": "1221", "endLine": 51, "endColumn": 30}, {"ruleId": "1230", "severity": 1, "message": "1266", "line": 79, "column": 6, "nodeType": "1232", "endLine": 79, "endColumn": 8, "suggestions": "1267"}, {"ruleId": "1218", "severity": 1, "message": "1268", "line": 27, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 27, "endColumn": 29}, {"ruleId": "1218", "severity": 1, "message": "1269", "line": 17, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 17, "endColumn": 10}, {"ruleId": "1218", "severity": 1, "message": "1270", "line": 18, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 18, "endColumn": 9}, {"ruleId": "1218", "severity": 1, "message": "1271", "line": 64, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 64, "endColumn": 19}, {"ruleId": "1218", "severity": 1, "message": "1272", "line": 64, "column": 21, "nodeType": "1220", "messageId": "1221", "endLine": 64, "endColumn": 33}, {"ruleId": "1218", "severity": 1, "message": "1273", "line": 67, "column": 9, "nodeType": "1220", "messageId": "1221", "endLine": 67, "endColumn": 25}, {"ruleId": "1218", "severity": 1, "message": "1256", "line": 69, "column": 9, "nodeType": "1220", "messageId": "1221", "endLine": 69, "endColumn": 34}, {"ruleId": "1218", "severity": 1, "message": "1263", "line": 71, "column": 27, "nodeType": "1220", "messageId": "1221", "endLine": 71, "endColumn": 34}, {"ruleId": "1218", "severity": 1, "message": "1274", "line": 73, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 73, "endColumn": 34}, {"ruleId": "1218", "severity": 1, "message": "1275", "line": 81, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 81, "endColumn": 25}, {"ruleId": "1218", "severity": 1, "message": "1276", "line": 95, "column": 25, "nodeType": "1220", "messageId": "1221", "endLine": 95, "endColumn": 41}, {"ruleId": "1277", "severity": 1, "message": "1278", "line": 134, "column": 56, "nodeType": "1279", "messageId": "1280", "endLine": 134, "endColumn": 58}, {"ruleId": "1277", "severity": 1, "message": "1278", "line": 139, "column": 57, "nodeType": "1279", "messageId": "1280", "endLine": 139, "endColumn": 59}, {"ruleId": "1230", "severity": 1, "message": "1281", "line": 146, "column": 6, "nodeType": "1232", "endLine": 146, "endColumn": 8, "suggestions": "1282"}, {"ruleId": "1218", "severity": 1, "message": "1269", "line": 7, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 7, "endColumn": 17}, {"ruleId": "1218", "severity": 1, "message": "1283", "line": 13, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 13, "endColumn": 21}, {"ruleId": "1218", "severity": 1, "message": "1284", "line": 140, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 140, "endColumn": 30}, {"ruleId": "1277", "severity": 1, "message": "1278", "line": 306, "column": 56, "nodeType": "1279", "messageId": "1280", "endLine": 306, "endColumn": 58}, {"ruleId": "1230", "severity": 1, "message": "1285", "line": 151, "column": 6, "nodeType": "1232", "endLine": 151, "endColumn": 8, "suggestions": "1286", "suppressions": "1287"}, {"ruleId": "1218", "severity": 1, "message": "1288", "line": 8, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 8, "endColumn": 17}, {"ruleId": "1218", "severity": 1, "message": "1289", "line": 38, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 38, "endColumn": 21}, {"ruleId": "1218", "severity": 1, "message": "1290", "line": 40, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 40, "endColumn": 22}, {"ruleId": "1218", "severity": 1, "message": "1291", "line": 62, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 62, "endColumn": 23}, {"ruleId": "1218", "severity": 1, "message": "1292", "line": 89, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 89, "endColumn": 34}, {"ruleId": "1218", "severity": 1, "message": "1293", "line": 100, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 100, "endColumn": 21}, {"ruleId": "1218", "severity": 1, "message": "1294", "line": 101, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 101, "endColumn": 21}, {"ruleId": "1218", "severity": 1, "message": "1295", "line": 237, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 237, "endColumn": 14}, {"ruleId": "1218", "severity": 1, "message": "1296", "line": 237, "column": 16, "nodeType": "1220", "messageId": "1221", "endLine": 237, "endColumn": 23}, {"ruleId": "1218", "severity": 1, "message": "1297", "line": 238, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 238, "endColumn": 26}, {"ruleId": "1218", "severity": 1, "message": "1298", "line": 238, "column": 28, "nodeType": "1220", "messageId": "1221", "endLine": 238, "endColumn": 47}, {"ruleId": "1218", "severity": 1, "message": "1299", "line": 253, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 253, "endColumn": 29}, {"ruleId": "1230", "severity": 1, "message": "1300", "line": 314, "column": 6, "nodeType": "1232", "endLine": 314, "endColumn": 8, "suggestions": "1301"}, {"ruleId": "1218", "severity": 1, "message": "1302", "line": 316, "column": 9, "nodeType": "1220", "messageId": "1221", "endLine": 316, "endColumn": 26}, {"ruleId": "1218", "severity": 1, "message": "1303", "line": 318, "column": 11, "nodeType": "1220", "messageId": "1221", "endLine": 318, "endColumn": 18}, {"ruleId": "1218", "severity": 1, "message": "1304", "line": 595, "column": 11, "nodeType": "1220", "messageId": "1221", "endLine": 595, "endColumn": 19}, {"ruleId": "1218", "severity": 1, "message": "1305", "line": 953, "column": 11, "nodeType": "1220", "messageId": "1221", "endLine": 953, "endColumn": 27}, {"ruleId": "1277", "severity": 1, "message": "1306", "line": 1554, "column": 71, "nodeType": "1279", "messageId": "1280", "endLine": 1554, "endColumn": 73}, {"ruleId": "1277", "severity": 1, "message": "1278", "line": 1569, "column": 42, "nodeType": "1279", "messageId": "1280", "endLine": 1569, "endColumn": 44}, {"ruleId": "1307", "severity": 1, "message": "1308", "line": 1800, "column": 19, "nodeType": "1309", "endLine": 1811, "endColumn": 21}, {"ruleId": "1307", "severity": 1, "message": "1308", "line": 1858, "column": 17, "nodeType": "1309", "endLine": 1875, "endColumn": 19}, {"ruleId": "1218", "severity": 1, "message": "1310", "line": 9, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 9, "endColumn": 34}, {"ruleId": "1218", "severity": 1, "message": "1311", "line": 10, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 10, "endColumn": 25}, {"ruleId": "1218", "severity": 1, "message": "1312", "line": 11, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 11, "endColumn": 18}, {"ruleId": "1218", "severity": 1, "message": "1313", "line": 14, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 14, "endColumn": 17}, {"ruleId": "1218", "severity": 1, "message": "1314", "line": 15, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 15, "endColumn": 23}, {"ruleId": "1218", "severity": 1, "message": "1315", "line": 16, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 16, "endColumn": 26}, {"ruleId": "1218", "severity": 1, "message": "1316", "line": 17, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 17, "endColumn": 20}, {"ruleId": "1218", "severity": 1, "message": "1317", "line": 18, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 18, "endColumn": 27}, {"ruleId": "1218", "severity": 1, "message": "1318", "line": 19, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 19, "endColumn": 16}, {"ruleId": "1218", "severity": 1, "message": "1319", "line": 21, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 21, "endColumn": 30}, {"ruleId": "1218", "severity": 1, "message": "1320", "line": 22, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 22, "endColumn": 32}, {"ruleId": "1218", "severity": 1, "message": "1283", "line": 26, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 26, "endColumn": 21}, {"ruleId": "1218", "severity": 1, "message": "1321", "line": 31, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 31, "endColumn": 24}, {"ruleId": "1218", "severity": 1, "message": "1322", "line": 31, "column": 26, "nodeType": "1220", "messageId": "1221", "endLine": 31, "endColumn": 31}, {"ruleId": "1218", "severity": 1, "message": "1223", "line": 40, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 40, "endColumn": 14}, {"ruleId": "1218", "severity": 1, "message": "1323", "line": 41, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 41, "endColumn": 33}, {"ruleId": "1218", "severity": 1, "message": "1324", "line": 43, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 43, "endColumn": 25}, {"ruleId": "1218", "severity": 1, "message": "1325", "line": 66, "column": 21, "nodeType": "1220", "messageId": "1221", "endLine": 66, "endColumn": 29}, {"ruleId": "1218", "severity": 1, "message": "1263", "line": 72, "column": 27, "nodeType": "1220", "messageId": "1221", "endLine": 72, "endColumn": 34}, {"ruleId": "1230", "severity": 1, "message": "1326", "line": 116, "column": 6, "nodeType": "1232", "endLine": 116, "endColumn": 8, "suggestions": "1327"}, {"ruleId": "1230", "severity": 1, "message": "1328", "line": 125, "column": 6, "nodeType": "1232", "endLine": 125, "endColumn": 26, "suggestions": "1329"}, {"ruleId": "1230", "severity": 1, "message": "1330", "line": 138, "column": 6, "nodeType": "1232", "endLine": 138, "endColumn": 45, "suggestions": "1331"}, {"ruleId": "1218", "severity": 1, "message": "1332", "line": 9, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 9, "endColumn": 13}, {"ruleId": "1218", "severity": 1, "message": "1332", "line": 9, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 9, "endColumn": 13}, {"ruleId": "1218", "severity": 1, "message": "1333", "line": 10, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 10, "endColumn": 9}, {"ruleId": "1218", "severity": 1, "message": "1334", "line": 13, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 13, "endColumn": 15}, {"ruleId": "1218", "severity": 1, "message": "1335", "line": 25, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 25, "endColumn": 23}, {"ruleId": "1218", "severity": 1, "message": "1336", "line": 33, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 33, "endColumn": 23}, {"ruleId": "1218", "severity": 1, "message": "1337", "line": 33, "column": 33, "nodeType": "1220", "messageId": "1221", "endLine": 33, "endColumn": 41}, {"ruleId": "1218", "severity": 1, "message": "1338", "line": 34, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 34, "endColumn": 16}, {"ruleId": "1218", "severity": 1, "message": "1339", "line": 36, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 36, "endColumn": 20}, {"ruleId": "1218", "severity": 1, "message": "1340", "line": 37, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 37, "endColumn": 22}, {"ruleId": "1218", "severity": 1, "message": "1341", "line": 1, "column": 38, "nodeType": "1220", "messageId": "1221", "endLine": 1, "endColumn": 49}, {"ruleId": "1218", "severity": 1, "message": "1342", "line": 4, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 4, "endColumn": 12}, {"ruleId": "1218", "severity": 1, "message": "1343", "line": 67, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 67, "endColumn": 19}, {"ruleId": "1218", "severity": 1, "message": "1344", "line": 67, "column": 21, "nodeType": "1220", "messageId": "1221", "endLine": 67, "endColumn": 33}, {"ruleId": "1230", "severity": 1, "message": "1345", "line": 79, "column": 6, "nodeType": "1232", "endLine": 79, "endColumn": 19, "suggestions": "1346"}, {"ruleId": "1218", "severity": 1, "message": "1347", "line": 14, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 14, "endColumn": 13}, {"ruleId": "1218", "severity": 1, "message": "1348", "line": 25, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 25, "endColumn": 18}, {"ruleId": "1218", "severity": 1, "message": "1349", "line": 26, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 26, "endColumn": 16}, {"ruleId": "1218", "severity": 1, "message": "1350", "line": 31, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 31, "endColumn": 25}, {"ruleId": "1218", "severity": 1, "message": "1270", "line": 47, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 47, "endColumn": 16}, {"ruleId": "1218", "severity": 1, "message": "1227", "line": 53, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 53, "endColumn": 15}, {"ruleId": "1218", "severity": 1, "message": "1263", "line": 70, "column": 27, "nodeType": "1220", "messageId": "1221", "endLine": 70, "endColumn": 34}, {"ruleId": "1230", "severity": 1, "message": "1351", "line": 104, "column": 6, "nodeType": "1232", "endLine": 104, "endColumn": 8, "suggestions": "1352"}, {"ruleId": "1230", "severity": 1, "message": "1353", "line": 108, "column": 6, "nodeType": "1232", "endLine": 108, "endColumn": 8, "suggestions": "1354"}, {"ruleId": "1218", "severity": 1, "message": "1304", "line": 223, "column": 13, "nodeType": "1220", "messageId": "1221", "endLine": 223, "endColumn": 21}, {"ruleId": "1218", "severity": 1, "message": "1355", "line": 20, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 20, "endColumn": 21}, {"ruleId": "1218", "severity": 1, "message": "1356", "line": 21, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 21, "endColumn": 28}, {"ruleId": "1218", "severity": 1, "message": "1357", "line": 34, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 34, "endColumn": 21}, {"ruleId": "1218", "severity": 1, "message": "1270", "line": 49, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 49, "endColumn": 16}, {"ruleId": "1218", "severity": 1, "message": "1358", "line": 49, "column": 18, "nodeType": "1220", "messageId": "1221", "endLine": 49, "endColumn": 22}, {"ruleId": "1218", "severity": 1, "message": "1359", "line": 49, "column": 31, "nodeType": "1220", "messageId": "1221", "endLine": 49, "endColumn": 36}, {"ruleId": "1218", "severity": 1, "message": "1360", "line": 53, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 53, "endColumn": 23}, {"ruleId": "1218", "severity": 1, "message": "1263", "line": 91, "column": 27, "nodeType": "1220", "messageId": "1221", "endLine": 91, "endColumn": 34}, {"ruleId": "1218", "severity": 1, "message": "1361", "line": 96, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 96, "endColumn": 20}, {"ruleId": "1230", "severity": 1, "message": "1362", "line": 120, "column": 6, "nodeType": "1232", "endLine": 120, "endColumn": 23, "suggestions": "1363"}, {"ruleId": "1218", "severity": 1, "message": "1364", "line": 217, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 217, "endColumn": 21}, {"ruleId": "1218", "severity": 1, "message": "1365", "line": 219, "column": 9, "nodeType": "1220", "messageId": "1221", "endLine": 219, "endColumn": 25}, {"ruleId": "1218", "severity": 1, "message": "1366", "line": 224, "column": 9, "nodeType": "1220", "messageId": "1221", "endLine": 224, "endColumn": 32}, {"ruleId": "1218", "severity": 1, "message": "1270", "line": 18, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 18, "endColumn": 16}, {"ruleId": "1218", "severity": 1, "message": "1367", "line": 21, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 21, "endColumn": 25}, {"ruleId": "1218", "severity": 1, "message": "1368", "line": 22, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 22, "endColumn": 30}, {"ruleId": "1218", "severity": 1, "message": "1369", "line": 30, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 30, "endColumn": 29}, {"ruleId": "1218", "severity": 1, "message": "1370", "line": 44, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 44, "endColumn": 17}, {"ruleId": "1218", "severity": 1, "message": "1356", "line": 51, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 51, "endColumn": 28}, {"ruleId": "1218", "severity": 1, "message": "1371", "line": 59, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 59, "endColumn": 20}, {"ruleId": "1218", "severity": 1, "message": "1372", "line": 61, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 61, "endColumn": 29}, {"ruleId": "1218", "severity": 1, "message": "1373", "line": 62, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 62, "endColumn": 23}, {"ruleId": "1218", "severity": 1, "message": "1263", "line": 86, "column": 27, "nodeType": "1220", "messageId": "1221", "endLine": 86, "endColumn": 34}, {"ruleId": "1218", "severity": 1, "message": "1374", "line": 98, "column": 23, "nodeType": "1220", "messageId": "1221", "endLine": 98, "endColumn": 37}, {"ruleId": "1218", "severity": 1, "message": "1361", "line": 102, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 102, "endColumn": 20}, {"ruleId": "1230", "severity": 1, "message": "1375", "line": 108, "column": 6, "nodeType": "1232", "endLine": 108, "endColumn": 8, "suggestions": "1376"}, {"ruleId": "1218", "severity": 1, "message": "1377", "line": 17, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 17, "endColumn": 12}, {"ruleId": "1218", "severity": 1, "message": "1378", "line": 18, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 18, "endColumn": 16}, {"ruleId": "1218", "severity": 1, "message": "1379", "line": 19, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 19, "endColumn": 14}, {"ruleId": "1218", "severity": 1, "message": "1380", "line": 29, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 29, "endColumn": 23}, {"ruleId": "1218", "severity": 1, "message": "1381", "line": 40, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 40, "endColumn": 20}, {"ruleId": "1218", "severity": 1, "message": "1382", "line": 45, "column": 23, "nodeType": "1220", "messageId": "1221", "endLine": 45, "endColumn": 34}, {"ruleId": "1218", "severity": 1, "message": "1373", "line": 56, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 56, "endColumn": 23}, {"ruleId": "1218", "severity": 1, "message": "1383", "line": 57, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 57, "endColumn": 26}, {"ruleId": "1218", "severity": 1, "message": "1384", "line": 64, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 64, "endColumn": 27}, {"ruleId": "1218", "severity": 1, "message": "1385", "line": 70, "column": 7, "nodeType": "1220", "messageId": "1221", "endLine": 70, "endColumn": 12}, {"ruleId": "1218", "severity": 1, "message": "1386", "line": 72, "column": 11, "nodeType": "1220", "messageId": "1221", "endLine": 72, "endColumn": 26}, {"ruleId": "1218", "severity": 1, "message": "1263", "line": 87, "column": 27, "nodeType": "1220", "messageId": "1221", "endLine": 87, "endColumn": 34}, {"ruleId": "1218", "severity": 1, "message": "1387", "line": 107, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 107, "endColumn": 18}, {"ruleId": "1218", "severity": 1, "message": "1388", "line": 107, "column": 20, "nodeType": "1220", "messageId": "1221", "endLine": 107, "endColumn": 31}, {"ruleId": "1230", "severity": 1, "message": "1389", "line": 121, "column": 6, "nodeType": "1232", "endLine": 121, "endColumn": 8, "suggestions": "1390"}, {"ruleId": "1230", "severity": 1, "message": "1391", "line": 223, "column": 6, "nodeType": "1232", "endLine": 223, "endColumn": 23, "suggestions": "1392"}, {"ruleId": "1307", "severity": 1, "message": "1308", "line": 678, "column": 31, "nodeType": "1309", "endLine": 682, "endColumn": 33}, {"ruleId": "1218", "severity": 1, "message": "1270", "line": 20, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 20, "endColumn": 9}, {"ruleId": "1218", "severity": 1, "message": "1393", "line": 67, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 67, "endColumn": 17}, {"ruleId": "1218", "severity": 1, "message": "1394", "line": 97, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 97, "endColumn": 26}, {"ruleId": "1218", "severity": 1, "message": "1395", "line": 97, "column": 28, "nodeType": "1220", "messageId": "1221", "endLine": 97, "endColumn": 47}, {"ruleId": "1218", "severity": 1, "message": "1263", "line": 102, "column": 27, "nodeType": "1220", "messageId": "1221", "endLine": 102, "endColumn": 34}, {"ruleId": "1218", "severity": 1, "message": "1325", "line": 106, "column": 21, "nodeType": "1220", "messageId": "1221", "endLine": 106, "endColumn": 29}, {"ruleId": "1218", "severity": 1, "message": "1374", "line": 119, "column": 23, "nodeType": "1220", "messageId": "1221", "endLine": 119, "endColumn": 37}, {"ruleId": "1230", "severity": 1, "message": "1266", "line": 133, "column": 6, "nodeType": "1232", "endLine": 133, "endColumn": 8, "suggestions": "1396"}, {"ruleId": "1230", "severity": 1, "message": "1397", "line": 137, "column": 6, "nodeType": "1232", "endLine": 137, "endColumn": 23, "suggestions": "1398"}, {"ruleId": "1230", "severity": 1, "message": "1399", "line": 146, "column": 6, "nodeType": "1232", "endLine": 146, "endColumn": 8, "suggestions": "1400"}, {"ruleId": "1218", "severity": 1, "message": "1401", "line": 12, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 12, "endColumn": 15}, {"ruleId": "1218", "severity": 1, "message": "1402", "line": 13, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 13, "endColumn": 16}, {"ruleId": "1218", "severity": 1, "message": "1403", "line": 19, "column": 6, "nodeType": "1220", "messageId": "1221", "endLine": 19, "endColumn": 19}, {"ruleId": "1230", "severity": 1, "message": "1404", "line": 57, "column": 6, "nodeType": "1232", "endLine": 57, "endColumn": 20, "suggestions": "1405"}, {"ruleId": "1218", "severity": 1, "message": "1406", "line": 27, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 27, "endColumn": 22}, {"ruleId": "1218", "severity": 1, "message": "1349", "line": 28, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 28, "endColumn": 16}, {"ruleId": "1218", "severity": 1, "message": "1407", "line": 32, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 32, "endColumn": 14}, {"ruleId": "1218", "severity": 1, "message": "1408", "line": 41, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 41, "endColumn": 23}, {"ruleId": "1218", "severity": 1, "message": "1409", "line": 53, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 53, "endColumn": 35}, {"ruleId": "1218", "severity": 1, "message": "1410", "line": 57, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 57, "endColumn": 16}, {"ruleId": "1218", "severity": 1, "message": "1411", "line": 80, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 80, "endColumn": 18}, {"ruleId": "1218", "severity": 1, "message": "1412", "line": 84, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 84, "endColumn": 12}, {"ruleId": "1218", "severity": 1, "message": "1413", "line": 90, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 90, "endColumn": 26}, {"ruleId": "1218", "severity": 1, "message": "1332", "line": 91, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 91, "endColumn": 18}, {"ruleId": "1218", "severity": 1, "message": "1414", "line": 92, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 92, "endColumn": 24}, {"ruleId": "1218", "severity": 1, "message": "1415", "line": 106, "column": 9, "nodeType": "1220", "messageId": "1221", "endLine": 106, "endColumn": 19}, {"ruleId": "1218", "severity": 1, "message": "1416", "line": 107, "column": 9, "nodeType": "1220", "messageId": "1221", "endLine": 107, "endColumn": 19}, {"ruleId": "1218", "severity": 1, "message": "1417", "line": 108, "column": 9, "nodeType": "1220", "messageId": "1221", "endLine": 108, "endColumn": 23}, {"ruleId": "1218", "severity": 1, "message": "1418", "line": 109, "column": 9, "nodeType": "1220", "messageId": "1221", "endLine": 109, "endColumn": 22}, {"ruleId": "1218", "severity": 1, "message": "1273", "line": 114, "column": 9, "nodeType": "1220", "messageId": "1221", "endLine": 114, "endColumn": 25}, {"ruleId": "1218", "severity": 1, "message": "1263", "line": 118, "column": 27, "nodeType": "1220", "messageId": "1221", "endLine": 118, "endColumn": 34}, {"ruleId": "1218", "severity": 1, "message": "1275", "line": 123, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 123, "endColumn": 25}, {"ruleId": "1218", "severity": 1, "message": "1419", "line": 128, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 128, "endColumn": 20}, {"ruleId": "1218", "severity": 1, "message": "1276", "line": 139, "column": 25, "nodeType": "1220", "messageId": "1221", "endLine": 139, "endColumn": 41}, {"ruleId": "1218", "severity": 1, "message": "1361", "line": 141, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 141, "endColumn": 20}, {"ruleId": "1277", "severity": 1, "message": "1278", "line": 179, "column": 56, "nodeType": "1279", "messageId": "1280", "endLine": 179, "endColumn": 58}, {"ruleId": "1277", "severity": 1, "message": "1278", "line": 184, "column": 57, "nodeType": "1279", "messageId": "1280", "endLine": 184, "endColumn": 59}, {"ruleId": "1230", "severity": 1, "message": "1420", "line": 192, "column": 6, "nodeType": "1232", "endLine": 192, "endColumn": 8, "suggestions": "1421"}, {"ruleId": "1218", "severity": 1, "message": "1422", "line": 207, "column": 9, "nodeType": "1220", "messageId": "1221", "endLine": 207, "endColumn": 20}, {"ruleId": "1277", "severity": 1, "message": "1306", "line": 217, "column": 36, "nodeType": "1279", "messageId": "1280", "endLine": 217, "endColumn": 38}, {"ruleId": "1218", "severity": 1, "message": "1423", "line": 278, "column": 9, "nodeType": "1220", "messageId": "1221", "endLine": 278, "endColumn": 20}, {"ruleId": "1277", "severity": 1, "message": "1278", "line": 390, "column": 60, "nodeType": "1279", "messageId": "1280", "endLine": 390, "endColumn": 62}, {"ruleId": "1218", "severity": 1, "message": "1424", "line": 499, "column": 9, "nodeType": "1220", "messageId": "1221", "endLine": 499, "endColumn": 21}, {"ruleId": "1218", "severity": 1, "message": "1425", "line": 2, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 2, "endColumn": 17}, {"ruleId": "1218", "severity": 1, "message": "1426", "line": 3, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 3, "endColumn": 15}, {"ruleId": "1218", "severity": 1, "message": "1427", "line": 4, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 4, "endColumn": 14}, {"ruleId": "1218", "severity": 1, "message": "1428", "line": 5, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 5, "endColumn": 13}, {"ruleId": "1218", "severity": 1, "message": "1429", "line": 6, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 6, "endColumn": 20}, {"ruleId": "1218", "severity": 1, "message": "1430", "line": 10, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 10, "endColumn": 39}, {"ruleId": "1218", "severity": 1, "message": "1431", "line": 11, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 11, "endColumn": 36}, {"ruleId": "1218", "severity": 1, "message": "1432", "line": 7, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 7, "endColumn": 7}, {"ruleId": "1218", "severity": 1, "message": "1433", "line": 8, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 8, "endColumn": 14}, {"ruleId": "1218", "severity": 1, "message": "1393", "line": 11, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 11, "endColumn": 12}, {"ruleId": "1218", "severity": 1, "message": "1322", "line": 14, "column": 19, "nodeType": "1220", "messageId": "1221", "endLine": 14, "endColumn": 24}, {"ruleId": "1218", "severity": 1, "message": "1409", "line": 28, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 28, "endColumn": 28}, {"ruleId": "1218", "severity": 1, "message": "1270", "line": 32, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 32, "endColumn": 9}, {"ruleId": "1218", "severity": 1, "message": "1434", "line": 36, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 36, "endColumn": 13}, {"ruleId": "1218", "severity": 1, "message": "1435", "line": 37, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 37, "endColumn": 8}, {"ruleId": "1218", "severity": 1, "message": "1436", "line": 51, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 51, "endColumn": 21}, {"ruleId": "1218", "severity": 1, "message": "1437", "line": 57, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 57, "endColumn": 15}, {"ruleId": "1218", "severity": 1, "message": "1276", "line": 78, "column": 25, "nodeType": "1220", "messageId": "1221", "endLine": 78, "endColumn": 41}, {"ruleId": "1218", "severity": 1, "message": "1263", "line": 83, "column": 27, "nodeType": "1220", "messageId": "1221", "endLine": 83, "endColumn": 34}, {"ruleId": "1230", "severity": 1, "message": "1438", "line": 118, "column": 6, "nodeType": "1232", "endLine": 118, "endColumn": 8, "suggestions": "1439"}, {"ruleId": "1218", "severity": 1, "message": "1440", "line": 179, "column": 9, "nodeType": "1220", "messageId": "1221", "endLine": 179, "endColumn": 18}, {"ruleId": "1277", "severity": 1, "message": "1306", "line": 368, "column": 65, "nodeType": "1279", "messageId": "1280", "endLine": 368, "endColumn": 67}, {"ruleId": "1230", "severity": 1, "message": "1441", "line": 28, "column": 6, "nodeType": "1232", "endLine": 28, "endColumn": 16, "suggestions": "1442"}, {"ruleId": "1230", "severity": 1, "message": "1441", "line": 28, "column": 6, "nodeType": "1232", "endLine": 28, "endColumn": 16, "suggestions": "1443"}, {"ruleId": "1218", "severity": 1, "message": "1444", "line": 2, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 2, "endColumn": 19}, {"ruleId": "1277", "severity": 1, "message": "1306", "line": 29, "column": 45, "nodeType": "1279", "messageId": "1280", "endLine": 29, "endColumn": 47}, {"ruleId": "1445", "severity": 1, "message": "1446", "line": 7, "column": 24, "nodeType": "1447", "messageId": "1448", "endLine": 7, "endColumn": 25, "suggestions": "1449"}, {"ruleId": "1445", "severity": 1, "message": "1446", "line": 7, "column": 40, "nodeType": "1447", "messageId": "1448", "endLine": 7, "endColumn": 41, "suggestions": "1450"}, {"ruleId": "1218", "severity": 1, "message": "1451", "line": 6, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 6, "endColumn": 15}, {"ruleId": "1218", "severity": 1, "message": "1452", "line": 9, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 9, "endColumn": 18}, {"ruleId": "1218", "severity": 1, "message": "1269", "line": 10, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 10, "endColumn": 15}, {"ruleId": "1218", "severity": 1, "message": "1453", "line": 12, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 12, "endColumn": 16}, {"ruleId": "1218", "severity": 1, "message": "1223", "line": 14, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 14, "endColumn": 14}, {"ruleId": "1218", "severity": 1, "message": "1454", "line": 15, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 15, "endColumn": 12}, {"ruleId": "1218", "severity": 1, "message": "1455", "line": 16, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 16, "endColumn": 16}, {"ruleId": "1218", "severity": 1, "message": "1456", "line": 22, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 22, "endColumn": 26}, {"ruleId": "1218", "severity": 1, "message": "1457", "line": 28, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 28, "endColumn": 18}, {"ruleId": "1218", "severity": 1, "message": "1458", "line": 31, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 31, "endColumn": 28}, {"ruleId": "1218", "severity": 1, "message": "1459", "line": 32, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 32, "endColumn": 34}, {"ruleId": "1218", "severity": 1, "message": "1460", "line": 33, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 33, "endColumn": 22}, {"ruleId": "1218", "severity": 1, "message": "1461", "line": 41, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 41, "endColumn": 26}, {"ruleId": "1218", "severity": 1, "message": "1462", "line": 92, "column": 7, "nodeType": "1220", "messageId": "1221", "endLine": 92, "endColumn": 13}, {"ruleId": "1218", "severity": 1, "message": "1463", "line": 156, "column": 9, "nodeType": "1220", "messageId": "1221", "endLine": 156, "endColumn": 14}, {"ruleId": "1230", "severity": 1, "message": "1464", "line": 169, "column": 6, "nodeType": "1232", "endLine": 169, "endColumn": 8, "suggestions": "1465"}, {"ruleId": "1218", "severity": 1, "message": "1466", "line": 171, "column": 9, "nodeType": "1220", "messageId": "1221", "endLine": 171, "endColumn": 28}, {"ruleId": "1218", "severity": 1, "message": "1467", "line": 340, "column": 9, "nodeType": "1220", "messageId": "1221", "endLine": 340, "endColumn": 39}, {"ruleId": "1445", "severity": 1, "message": "1446", "line": 9, "column": 24, "nodeType": "1447", "messageId": "1448", "endLine": 9, "endColumn": 25, "suggestions": "1468"}, {"ruleId": "1445", "severity": 1, "message": "1446", "line": 9, "column": 40, "nodeType": "1447", "messageId": "1448", "endLine": 9, "endColumn": 41, "suggestions": "1469"}, {"ruleId": "1218", "severity": 1, "message": "1470", "line": 1, "column": 17, "nodeType": "1220", "messageId": "1221", "endLine": 1, "endColumn": 26}, {"ruleId": "1218", "severity": 1, "message": "1471", "line": 1, "column": 28, "nodeType": "1220", "messageId": "1221", "endLine": 1, "endColumn": 35}, {"ruleId": "1218", "severity": 1, "message": "1269", "line": 4, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 4, "endColumn": 17}, {"ruleId": "1218", "severity": 1, "message": "1432", "line": 4, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 4, "endColumn": 14}, {"ruleId": "1218", "severity": 1, "message": "1433", "line": 4, "column": 16, "nodeType": "1220", "messageId": "1221", "endLine": 4, "endColumn": 27}, {"ruleId": "1218", "severity": 1, "message": "1472", "line": 8, "column": 9, "nodeType": "1220", "messageId": "1221", "endLine": 8, "endColumn": 15}, {"ruleId": "1218", "severity": 1, "message": "1473", "line": 2, "column": 16, "nodeType": "1220", "messageId": "1221", "endLine": 2, "endColumn": 25}, {"ruleId": "1218", "severity": 1, "message": "1474", "line": 2, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 2, "endColumn": 13}, {"ruleId": "1218", "severity": 1, "message": "1473", "line": 2, "column": 15, "nodeType": "1220", "messageId": "1221", "endLine": 2, "endColumn": 24}, {"ruleId": "1218", "severity": 1, "message": "1432", "line": 3, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 3, "endColumn": 7}, {"ruleId": "1218", "severity": 1, "message": "1433", "line": 4, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 4, "endColumn": 14}, {"ruleId": "1218", "severity": 1, "message": "1444", "line": 1, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 1, "endColumn": 19}, {"ruleId": "1218", "severity": 1, "message": "1444", "line": 1, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 1, "endColumn": 19}, {"ruleId": "1218", "severity": 1, "message": "1444", "line": 1, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 1, "endColumn": 19}, {"ruleId": "1218", "severity": 1, "message": "1475", "line": 11, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 11, "endColumn": 21}, {"ruleId": "1218", "severity": 1, "message": "1263", "line": 32, "column": 27, "nodeType": "1220", "messageId": "1221", "endLine": 32, "endColumn": 34}, {"ruleId": "1218", "severity": 1, "message": "1304", "line": 42, "column": 11, "nodeType": "1220", "messageId": "1221", "endLine": 42, "endColumn": 19}, {"ruleId": "1218", "severity": 1, "message": "1407", "line": 1, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 1, "endColumn": 14}, {"ruleId": "1218", "severity": 1, "message": "1358", "line": 2, "column": 23, "nodeType": "1220", "messageId": "1221", "endLine": 2, "endColumn": 27}, {"ruleId": "1218", "severity": 1, "message": "1322", "line": 2, "column": 29, "nodeType": "1220", "messageId": "1221", "endLine": 2, "endColumn": 34}, {"ruleId": "1218", "severity": 1, "message": "1359", "line": 2, "column": 36, "nodeType": "1220", "messageId": "1221", "endLine": 2, "endColumn": 41}, {"ruleId": "1218", "severity": 1, "message": "1476", "line": 1, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 1, "endColumn": 15}, {"ruleId": "1230", "severity": 1, "message": "1477", "line": 63, "column": 6, "nodeType": "1232", "endLine": 63, "endColumn": 8, "suggestions": "1478"}, {"ruleId": "1218", "severity": 1, "message": "1347", "line": 4, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 4, "endColumn": 8}, {"ruleId": "1218", "severity": 1, "message": "1479", "line": 8, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 8, "endColumn": 15}, {"ruleId": "1218", "severity": 1, "message": "1333", "line": 25, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 25, "endColumn": 9}, {"ruleId": "1218", "severity": 1, "message": "1480", "line": 26, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 26, "endColumn": 9}, {"ruleId": "1218", "severity": 1, "message": "1332", "line": 27, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 27, "endColumn": 13}, {"ruleId": "1218", "severity": 1, "message": "1451", "line": 28, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 28, "endColumn": 10}, {"ruleId": "1218", "severity": 1, "message": "1481", "line": 48, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 48, "endColumn": 18}, {"ruleId": "1218", "severity": 1, "message": "1482", "line": 50, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 50, "endColumn": 17}, {"ruleId": "1218", "severity": 1, "message": "1432", "line": 52, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 52, "endColumn": 12}, {"ruleId": "1218", "severity": 1, "message": "1433", "line": 53, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 53, "endColumn": 21}, {"ruleId": "1218", "severity": 1, "message": "1393", "line": 54, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 54, "endColumn": 19}, {"ruleId": "1218", "severity": 1, "message": "1358", "line": 54, "column": 21, "nodeType": "1220", "messageId": "1221", "endLine": 54, "endColumn": 25}, {"ruleId": "1218", "severity": 1, "message": "1483", "line": 55, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 55, "endColumn": 22}, {"ruleId": "1218", "severity": 1, "message": "1484", "line": 55, "column": 24, "nodeType": "1220", "messageId": "1221", "endLine": 55, "endColumn": 39}, {"ruleId": "1218", "severity": 1, "message": "1485", "line": 56, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 56, "endColumn": 31}, {"ruleId": "1218", "severity": 1, "message": "1414", "line": 57, "column": 26, "nodeType": "1220", "messageId": "1221", "endLine": 57, "endColumn": 40}, {"ruleId": "1218", "severity": 1, "message": "1486", "line": 107, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 107, "endColumn": 14}, {"ruleId": "1218", "severity": 1, "message": "1487", "line": 108, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 108, "endColumn": 23}, {"ruleId": "1218", "severity": 1, "message": "1488", "line": 109, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 109, "endColumn": 25}, {"ruleId": "1218", "severity": 1, "message": "1489", "line": 111, "column": 9, "nodeType": "1220", "messageId": "1221", "endLine": 111, "endColumn": 19}, {"ruleId": "1218", "severity": 1, "message": "1490", "line": 112, "column": 9, "nodeType": "1220", "messageId": "1221", "endLine": 112, "endColumn": 20}, {"ruleId": "1218", "severity": 1, "message": "1491", "line": 117, "column": 9, "nodeType": "1220", "messageId": "1221", "endLine": 117, "endColumn": 29}, {"ruleId": "1218", "severity": 1, "message": "1492", "line": 125, "column": 9, "nodeType": "1220", "messageId": "1221", "endLine": 125, "endColumn": 32}, {"ruleId": "1218", "severity": 1, "message": "1493", "line": 131, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 131, "endColumn": 22}, {"ruleId": "1218", "severity": 1, "message": "1494", "line": 133, "column": 9, "nodeType": "1220", "messageId": "1221", "endLine": 133, "endColumn": 19}, {"ruleId": "1218", "severity": 1, "message": "1495", "line": 142, "column": 9, "nodeType": "1220", "messageId": "1221", "endLine": 142, "endColumn": 19}, {"ruleId": "1218", "severity": 1, "message": "1276", "line": 177, "column": 25, "nodeType": "1220", "messageId": "1221", "endLine": 177, "endColumn": 41}, {"ruleId": "1230", "severity": 1, "message": "1496", "line": 222, "column": 6, "nodeType": "1232", "endLine": 222, "endColumn": 8, "suggestions": "1497"}, {"ruleId": "1230", "severity": 1, "message": "1498", "line": 244, "column": 6, "nodeType": "1232", "endLine": 244, "endColumn": 68, "suggestions": "1499"}, {"ruleId": "1277", "severity": 1, "message": "1278", "line": 426, "column": 64, "nodeType": "1279", "messageId": "1280", "endLine": 426, "endColumn": 66}, {"ruleId": "1277", "severity": 1, "message": "1278", "line": 432, "column": 66, "nodeType": "1279", "messageId": "1280", "endLine": 432, "endColumn": 68}, {"ruleId": "1277", "severity": 1, "message": "1306", "line": 450, "column": 50, "nodeType": "1279", "messageId": "1280", "endLine": 450, "endColumn": 52}, {"ruleId": "1277", "severity": 1, "message": "1278", "line": 743, "column": 61, "nodeType": "1279", "messageId": "1280", "endLine": 743, "endColumn": 63}, {"ruleId": "1218", "severity": 1, "message": "1219", "line": 1, "column": 46, "nodeType": "1220", "messageId": "1221", "endLine": 1, "endColumn": 56}, {"ruleId": "1218", "severity": 1, "message": "1479", "line": 7, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 7, "endColumn": 15}, {"ruleId": "1218", "severity": 1, "message": "1500", "line": 19, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 19, "endColumn": 24}, {"ruleId": "1230", "severity": 1, "message": "1501", "line": 58, "column": 6, "nodeType": "1232", "endLine": 58, "endColumn": 39, "suggestions": "1502"}, {"ruleId": "1230", "severity": 1, "message": "1503", "line": 137, "column": 6, "nodeType": "1232", "endLine": 137, "endColumn": 36, "suggestions": "1504"}, {"ruleId": "1218", "severity": 1, "message": "1444", "line": 1, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 1, "endColumn": 19}, {"ruleId": "1218", "severity": 1, "message": "1505", "line": 6, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 6, "endColumn": 17}, {"ruleId": "1218", "severity": 1, "message": "1506", "line": 15, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 15, "endColumn": 27}, {"ruleId": "1218", "severity": 1, "message": "1507", "line": 4, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 4, "endColumn": 16}, {"ruleId": "1218", "severity": 1, "message": "1223", "line": 5, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 5, "endColumn": 9}, {"ruleId": "1218", "severity": 1, "message": "1452", "line": 6, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 6, "endColumn": 13}, {"ruleId": "1218", "severity": 1, "message": "1508", "line": 7, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 7, "endColumn": 6}, {"ruleId": "1218", "severity": 1, "message": "1332", "line": 8, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 8, "endColumn": 13}, {"ruleId": "1218", "severity": 1, "message": "1480", "line": 9, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 9, "endColumn": 9}, {"ruleId": "1218", "severity": 1, "message": "1451", "line": 10, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 10, "endColumn": 10}, {"ruleId": "1218", "severity": 1, "message": "1377", "line": 11, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 11, "endColumn": 7}, {"ruleId": "1218", "severity": 1, "message": "1378", "line": 12, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 12, "endColumn": 11}, {"ruleId": "1218", "severity": 1, "message": "1479", "line": 13, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 13, "endColumn": 15}, {"ruleId": "1218", "severity": 1, "message": "1269", "line": 14, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 14, "endColumn": 10}, {"ruleId": "1218", "severity": 1, "message": "1509", "line": 17, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 17, "endColumn": 17}, {"ruleId": "1218", "severity": 1, "message": "1482", "line": 19, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 19, "endColumn": 17}, {"ruleId": "1218", "severity": 1, "message": "1510", "line": 20, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 20, "endColumn": 21}, {"ruleId": "1218", "severity": 1, "message": "1511", "line": 21, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 21, "endColumn": 24}, {"ruleId": "1218", "severity": 1, "message": "1512", "line": 22, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 22, "endColumn": 20}, {"ruleId": "1218", "severity": 1, "message": "1513", "line": 23, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 23, "endColumn": 16}, {"ruleId": "1218", "severity": 1, "message": "1514", "line": 23, "column": 18, "nodeType": "1220", "messageId": "1221", "endLine": 23, "endColumn": 22}, {"ruleId": "1218", "severity": 1, "message": "1257", "line": 24, "column": 13, "nodeType": "1220", "messageId": "1221", "endLine": 24, "endColumn": 16}, {"ruleId": "1218", "severity": 1, "message": "1515", "line": 33, "column": 11, "nodeType": "1220", "messageId": "1221", "endLine": 33, "endColumn": 19}, {"ruleId": "1218", "severity": 1, "message": "1516", "line": 4, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 4, "endColumn": 14}, {"ruleId": "1218", "severity": 1, "message": "1224", "line": 10, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 10, "endColumn": 12}, {"ruleId": "1218", "severity": 1, "message": "1435", "line": 14, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 14, "endColumn": 8}, {"ruleId": "1218", "severity": 1, "message": "1517", "line": 21, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 21, "endColumn": 22}, {"ruleId": "1218", "severity": 1, "message": "1518", "line": 22, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 22, "endColumn": 30}, {"ruleId": "1218", "severity": 1, "message": "1519", "line": 22, "column": 32, "nodeType": "1220", "messageId": "1221", "endLine": 22, "endColumn": 42}, {"ruleId": "1218", "severity": 1, "message": "1358", "line": 15, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 15, "endColumn": 7}, {"ruleId": "1218", "severity": 1, "message": "1269", "line": 15, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 15, "endColumn": 10}, {"ruleId": "1218", "severity": 1, "message": "1470", "line": 1, "column": 27, "nodeType": "1220", "messageId": "1221", "endLine": 1, "endColumn": 36}, {"ruleId": "1218", "severity": 1, "message": "1520", "line": 13, "column": 13, "nodeType": "1220", "messageId": "1221", "endLine": 13, "endColumn": 23}, {"ruleId": "1218", "severity": 1, "message": "1444", "line": 1, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 1, "endColumn": 19}, {"ruleId": "1218", "severity": 1, "message": "1521", "line": 4, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 4, "endColumn": 20}, {"ruleId": "1218", "severity": 1, "message": "1444", "line": 1, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 1, "endColumn": 19}, {"ruleId": "1218", "severity": 1, "message": "1521", "line": 10, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 10, "endColumn": 20}, {"ruleId": "1218", "severity": 1, "message": "1522", "line": 11, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 11, "endColumn": 15}, {"ruleId": "1218", "severity": 1, "message": "1523", "line": 1, "column": 17, "nodeType": "1220", "messageId": "1221", "endLine": 1, "endColumn": 25}, {"ruleId": "1218", "severity": 1, "message": "1524", "line": 10, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 10, "endColumn": 21}, {"ruleId": "1218", "severity": 1, "message": "1525", "line": 8, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 8, "endColumn": 28}, {"ruleId": "1218", "severity": 1, "message": "1358", "line": 29, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 29, "endColumn": 12}, {"ruleId": "1218", "severity": 1, "message": "1526", "line": 52, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 52, "endColumn": 15}, {"ruleId": "1218", "severity": 1, "message": "1527", "line": 57, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 57, "endColumn": 28}, {"ruleId": "1218", "severity": 1, "message": "1528", "line": 58, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 58, "endColumn": 16}, {"ruleId": "1218", "severity": 1, "message": "1529", "line": 59, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 59, "endColumn": 19}, {"ruleId": "1218", "severity": 1, "message": "1530", "line": 73, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 73, "endColumn": 16}, {"ruleId": "1218", "severity": 1, "message": "1531", "line": 73, "column": 18, "nodeType": "1220", "messageId": "1221", "endLine": 73, "endColumn": 27}, {"ruleId": "1218", "severity": 1, "message": "1263", "line": 76, "column": 27, "nodeType": "1220", "messageId": "1221", "endLine": 76, "endColumn": 34}, {"ruleId": "1277", "severity": 1, "message": "1306", "line": 143, "column": 53, "nodeType": "1279", "messageId": "1280", "endLine": 143, "endColumn": 55}, {"ruleId": "1230", "severity": 1, "message": "1532", "line": 152, "column": 6, "nodeType": "1232", "endLine": 152, "endColumn": 8, "suggestions": "1533"}, {"ruleId": "1230", "severity": 1, "message": "1353", "line": 373, "column": 6, "nodeType": "1232", "endLine": 373, "endColumn": 8, "suggestions": "1534"}, {"ruleId": "1218", "severity": 1, "message": "1444", "line": 1, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 1, "endColumn": 19}, {"ruleId": "1218", "severity": 1, "message": "1535", "line": 6, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 6, "endColumn": 19}, {"ruleId": "1218", "severity": 1, "message": "1536", "line": 29, "column": 9, "nodeType": "1220", "messageId": "1221", "endLine": 29, "endColumn": 17}, {"ruleId": "1218", "severity": 1, "message": "1407", "line": 1, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 1, "endColumn": 14}, {"ruleId": "1218", "severity": 1, "message": "1470", "line": 2, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 2, "endColumn": 19}, {"ruleId": "1218", "severity": 1, "message": "1523", "line": 2, "column": 21, "nodeType": "1220", "messageId": "1221", "endLine": 2, "endColumn": 29}, {"ruleId": "1218", "severity": 1, "message": "1433", "line": 5, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 5, "endColumn": 14}, {"ruleId": "1218", "severity": 1, "message": "1407", "line": 8, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 8, "endColumn": 9}, {"ruleId": "1218", "severity": 1, "message": "1537", "line": 10, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 10, "endColumn": 8}, {"ruleId": "1218", "severity": 1, "message": "1377", "line": 9, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 9, "endColumn": 7}, {"ruleId": "1218", "severity": 1, "message": "1378", "line": 10, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 10, "endColumn": 11}, {"ruleId": "1218", "severity": 1, "message": "1479", "line": 11, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 11, "endColumn": 15}, {"ruleId": "1218", "severity": 1, "message": "1269", "line": 12, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 12, "endColumn": 10}, {"ruleId": "1218", "severity": 1, "message": "1538", "line": 12, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 12, "endColumn": 14}, {"ruleId": "1218", "severity": 1, "message": "1539", "line": 12, "column": 18, "nodeType": "1220", "messageId": "1221", "endLine": 12, "endColumn": 35}, {"ruleId": "1218", "severity": 1, "message": "1455", "line": 13, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 13, "endColumn": 16}, {"ruleId": "1218", "severity": 1, "message": "1540", "line": 14, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 14, "endColumn": 15}, {"ruleId": "1218", "severity": 1, "message": "1261", "line": 16, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 16, "endColumn": 18}, {"ruleId": "1218", "severity": 1, "message": "1259", "line": 17, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 17, "endColumn": 19}, {"ruleId": "1218", "severity": 1, "message": "1541", "line": 20, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 20, "endColumn": 24}, {"ruleId": "1218", "severity": 1, "message": "1542", "line": 21, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 21, "endColumn": 19}, {"ruleId": "1218", "severity": 1, "message": "1543", "line": 45, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 45, "endColumn": 19}, {"ruleId": "1218", "severity": 1, "message": "1276", "line": 59, "column": 25, "nodeType": "1220", "messageId": "1221", "endLine": 59, "endColumn": 41}, {"ruleId": "1230", "severity": 1, "message": "1544", "line": 81, "column": 6, "nodeType": "1232", "endLine": 81, "endColumn": 8, "suggestions": "1545"}, {"ruleId": "1218", "severity": 1, "message": "1377", "line": 9, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 9, "endColumn": 7}, {"ruleId": "1218", "severity": 1, "message": "1378", "line": 10, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 10, "endColumn": 11}, {"ruleId": "1218", "severity": 1, "message": "1479", "line": 11, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 11, "endColumn": 15}, {"ruleId": "1218", "severity": 1, "message": "1269", "line": 12, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 12, "endColumn": 10}, {"ruleId": "1546", "severity": 1, "message": "1547", "line": 37, "column": 61, "nodeType": "1548", "messageId": "1549", "endLine": 37, "endColumn": 63}, {"ruleId": "1218", "severity": 1, "message": "1452", "line": 5, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 5, "endColumn": 13}, {"ruleId": "1218", "severity": 1, "message": "1407", "line": 13, "column": 16, "nodeType": "1220", "messageId": "1221", "endLine": 13, "endColumn": 22}, {"ruleId": "1218", "severity": 1, "message": "1550", "line": 13, "column": 24, "nodeType": "1220", "messageId": "1221", "endLine": 13, "endColumn": 40}, {"ruleId": "1218", "severity": 1, "message": "1537", "line": 13, "column": 42, "nodeType": "1220", "messageId": "1221", "endLine": 13, "endColumn": 47}, {"ruleId": "1218", "severity": 1, "message": "1373", "line": 14, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 14, "endColumn": 23}, {"ruleId": "1218", "severity": 1, "message": "1551", "line": 15, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 15, "endColumn": 20}, {"ruleId": "1218", "severity": 1, "message": "1552", "line": 17, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 17, "endColumn": 29}, {"ruleId": "1218", "severity": 1, "message": "1553", "line": 18, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 18, "endColumn": 26}, {"ruleId": "1218", "severity": 1, "message": "1554", "line": 19, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 19, "endColumn": 29}, {"ruleId": "1218", "severity": 1, "message": "1555", "line": 20, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 20, "endColumn": 17}, {"ruleId": "1218", "severity": 1, "message": "1556", "line": 21, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 21, "endColumn": 25}, {"ruleId": "1218", "severity": 1, "message": "1223", "line": 8, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 8, "endColumn": 9}, {"ruleId": "1218", "severity": 1, "message": "1557", "line": 10, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 10, "endColumn": 26}, {"ruleId": "1218", "severity": 1, "message": "1558", "line": 22, "column": 9, "nodeType": "1220", "messageId": "1221", "endLine": 22, "endColumn": 22}, {"ruleId": "1218", "severity": 1, "message": "1407", "line": 1, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 1, "endColumn": 14}, {"ruleId": "1218", "severity": 1, "message": "1470", "line": 2, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 2, "endColumn": 19}, {"ruleId": "1218", "severity": 1, "message": "1523", "line": 2, "column": 21, "nodeType": "1220", "messageId": "1221", "endLine": 2, "endColumn": 29}, {"ruleId": "1218", "severity": 1, "message": "1559", "line": 2, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 2, "endColumn": 20}, {"ruleId": "1218", "severity": 1, "message": "1470", "line": 4, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 4, "endColumn": 12}, {"ruleId": "1218", "severity": 1, "message": "1263", "line": 38, "column": 27, "nodeType": "1220", "messageId": "1221", "endLine": 38, "endColumn": 34}, {"ruleId": "1218", "severity": 1, "message": "1304", "line": 49, "column": 11, "nodeType": "1220", "messageId": "1221", "endLine": 49, "endColumn": 19}, {"ruleId": "1218", "severity": 1, "message": "1538", "line": 7, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 7, "endColumn": 14}, {"ruleId": "1218", "severity": 1, "message": "1455", "line": 8, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 8, "endColumn": 16}, {"ruleId": "1218", "severity": 1, "message": "1261", "line": 9, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 9, "endColumn": 18}, {"ruleId": "1218", "severity": 1, "message": "1259", "line": 10, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 10, "endColumn": 19}, {"ruleId": "1218", "severity": 1, "message": "1560", "line": 12, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 12, "endColumn": 29}, {"ruleId": "1218", "severity": 1, "message": "1408", "line": 13, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 13, "endColumn": 23}, {"ruleId": "1218", "severity": 1, "message": "1561", "line": 19, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 19, "endColumn": 24}, {"ruleId": "1218", "severity": 1, "message": "1562", "line": 20, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 20, "endColumn": 20}, {"ruleId": "1218", "severity": 1, "message": "1563", "line": 21, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 21, "endColumn": 21}, {"ruleId": "1218", "severity": 1, "message": "1342", "line": 21, "column": 23, "nodeType": "1220", "messageId": "1221", "endLine": 21, "endColumn": 32}, {"ruleId": "1218", "severity": 1, "message": "1269", "line": 21, "column": 41, "nodeType": "1220", "messageId": "1221", "endLine": 21, "endColumn": 48}, {"ruleId": "1218", "severity": 1, "message": "1564", "line": 22, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 22, "endColumn": 27}, {"ruleId": "1230", "severity": 1, "message": "1565", "line": 56, "column": 6, "nodeType": "1232", "endLine": 56, "endColumn": 8, "suggestions": "1566"}, {"ruleId": "1218", "severity": 1, "message": "1263", "line": 32, "column": 27, "nodeType": "1220", "messageId": "1221", "endLine": 32, "endColumn": 34}, {"ruleId": "1230", "severity": 1, "message": "1567", "line": 57, "column": 6, "nodeType": "1232", "endLine": 57, "endColumn": 8, "suggestions": "1568"}, {"ruleId": "1218", "severity": 1, "message": "1569", "line": 64, "column": 15, "nodeType": "1220", "messageId": "1221", "endLine": 64, "endColumn": 57}, {"ruleId": "1277", "severity": 1, "message": "1306", "line": 85, "column": 38, "nodeType": "1279", "messageId": "1280", "endLine": 85, "endColumn": 40}, {"ruleId": "1277", "severity": 1, "message": "1306", "line": 94, "column": 67, "nodeType": "1279", "messageId": "1280", "endLine": 94, "endColumn": 69}, {"ruleId": "1218", "severity": 1, "message": "1570", "line": 113, "column": 13, "nodeType": "1220", "messageId": "1221", "endLine": 113, "endColumn": 72}, {"ruleId": "1218", "severity": 1, "message": "1407", "line": 1, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 1, "endColumn": 14}, {"ruleId": "1230", "severity": 1, "message": "1571", "line": 15, "column": 6, "nodeType": "1232", "endLine": 15, "endColumn": 8, "suggestions": "1572"}, {"ruleId": "1573", "severity": 1, "message": "1574", "line": 22, "column": 9, "nodeType": "1575", "messageId": "1576", "endLine": 38, "endColumn": 10}, {"ruleId": "1218", "severity": 1, "message": "1444", "line": 1, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 1, "endColumn": 19}, {"ruleId": "1218", "severity": 1, "message": "1512", "line": 10, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 10, "endColumn": 20}, {"ruleId": "1218", "severity": 1, "message": "1263", "line": 36, "column": 27, "nodeType": "1220", "messageId": "1221", "endLine": 36, "endColumn": 34}, {"ruleId": "1307", "severity": 1, "message": "1308", "line": 126, "column": 11, "nodeType": "1309", "endLine": 137, "endColumn": 13}, {"ruleId": "1218", "severity": 1, "message": "1577", "line": 33, "column": 5, "nodeType": "1220", "messageId": "1221", "endLine": 33, "endColumn": 19}, {"ruleId": "1218", "severity": 1, "message": "1578", "line": 69, "column": 11, "nodeType": "1220", "messageId": "1221", "endLine": 69, "endColumn": 19}, {"ruleId": "1218", "severity": 1, "message": "1579", "line": 2, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 2, "endColumn": 24}, {"ruleId": "1218", "severity": 1, "message": "1508", "line": 9, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 9, "endColumn": 13}, {"ruleId": "1218", "severity": 1, "message": "1580", "line": 26, "column": 30, "nodeType": "1220", "messageId": "1221", "endLine": 26, "endColumn": 44}, {"ruleId": "1218", "severity": 1, "message": "1332", "line": 3, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 3, "endColumn": 18}, {"ruleId": "1218", "severity": 1, "message": "1458", "line": 4, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 4, "endColumn": 28}, {"ruleId": "1218", "severity": 1, "message": "1223", "line": 5, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 5, "endColumn": 14}, {"ruleId": "1218", "severity": 1, "message": "1454", "line": 7, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 7, "endColumn": 12}, {"ruleId": "1218", "severity": 1, "message": "1455", "line": 8, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 8, "endColumn": 16}, {"ruleId": "1218", "severity": 1, "message": "1581", "line": 11, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 11, "endColumn": 18}, {"ruleId": "1218", "severity": 1, "message": "1582", "line": 16, "column": 9, "nodeType": "1220", "messageId": "1221", "endLine": 16, "endColumn": 19}, {"ruleId": "1218", "severity": 1, "message": "1583", "line": 18, "column": 9, "nodeType": "1220", "messageId": "1221", "endLine": 18, "endColumn": 20}, {"ruleId": "1218", "severity": 1, "message": "1423", "line": 19, "column": 9, "nodeType": "1220", "messageId": "1221", "endLine": 19, "endColumn": 20}, {"ruleId": "1218", "severity": 1, "message": "1490", "line": 22, "column": 9, "nodeType": "1220", "messageId": "1221", "endLine": 22, "endColumn": 20}, {"ruleId": "1218", "severity": 1, "message": "1382", "line": 11, "column": 23, "nodeType": "1220", "messageId": "1221", "endLine": 11, "endColumn": 34}, {"ruleId": "1230", "severity": 1, "message": "1584", "line": 43, "column": 6, "nodeType": "1232", "endLine": 43, "endColumn": 8, "suggestions": "1585"}, {"ruleId": "1230", "severity": 1, "message": "1586", "line": 37, "column": 6, "nodeType": "1232", "endLine": 37, "endColumn": 8, "suggestions": "1587"}, {"ruleId": "1218", "severity": 1, "message": "1588", "line": 22, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 22, "endColumn": 18}, {"ruleId": "1218", "severity": 1, "message": "1589", "line": 22, "column": 20, "nodeType": "1220", "messageId": "1221", "endLine": 22, "endColumn": 31}, {"ruleId": "1230", "severity": 1, "message": "1590", "line": 41, "column": 6, "nodeType": "1232", "endLine": 41, "endColumn": 26, "suggestions": "1591"}, {"ruleId": "1218", "severity": 1, "message": "1407", "line": 1, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 1, "endColumn": 14}, {"ruleId": "1218", "severity": 1, "message": "1358", "line": 3, "column": 27, "nodeType": "1220", "messageId": "1221", "endLine": 3, "endColumn": 31}, {"ruleId": "1230", "severity": 1, "message": "1590", "line": 136, "column": 6, "nodeType": "1232", "endLine": 136, "endColumn": 26, "suggestions": "1592"}, {"ruleId": "1218", "severity": 1, "message": "1593", "line": 4, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 4, "endColumn": 13}, {"ruleId": "1218", "severity": 1, "message": "1407", "line": 5, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 5, "endColumn": 9}, {"ruleId": "1218", "severity": 1, "message": "1594", "line": 6, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 6, "endColumn": 9}, {"ruleId": "1218", "severity": 1, "message": "1593", "line": 11, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 11, "endColumn": 13}, {"ruleId": "1218", "severity": 1, "message": "1595", "line": 12, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 12, "endColumn": 16}, {"ruleId": "1218", "severity": 1, "message": "1407", "line": 2, "column": 27, "nodeType": "1220", "messageId": "1221", "endLine": 2, "endColumn": 33}, {"ruleId": "1218", "severity": 1, "message": "1593", "line": 4, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 4, "endColumn": 13}, {"ruleId": "1218", "severity": 1, "message": "1595", "line": 5, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 5, "endColumn": 16}, {"ruleId": "1218", "severity": 1, "message": "1407", "line": 5, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 5, "endColumn": 9}, {"ruleId": "1218", "severity": 1, "message": "1594", "line": 6, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 6, "endColumn": 9}, {"ruleId": "1218", "severity": 1, "message": "1593", "line": 11, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 11, "endColumn": 13}, {"ruleId": "1218", "severity": 1, "message": "1595", "line": 12, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 12, "endColumn": 16}, {"ruleId": "1218", "severity": 1, "message": "1407", "line": 5, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 5, "endColumn": 9}, {"ruleId": "1218", "severity": 1, "message": "1594", "line": 6, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 6, "endColumn": 9}, {"ruleId": "1218", "severity": 1, "message": "1593", "line": 11, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 11, "endColumn": 13}, {"ruleId": "1218", "severity": 1, "message": "1595", "line": 12, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 12, "endColumn": 16}, {"ruleId": "1596", "severity": 1, "message": "1597", "line": 50, "column": 15, "nodeType": "1309", "endLine": 52, "endColumn": 17}, {"ruleId": "1218", "severity": 1, "message": "1407", "line": 5, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 5, "endColumn": 9}, {"ruleId": "1218", "severity": 1, "message": "1594", "line": 6, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 6, "endColumn": 9}, {"ruleId": "1218", "severity": 1, "message": "1593", "line": 11, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 11, "endColumn": 13}, {"ruleId": "1218", "severity": 1, "message": "1595", "line": 12, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 12, "endColumn": 16}, {"ruleId": "1218", "severity": 1, "message": "1598", "line": 35, "column": 7, "nodeType": "1220", "messageId": "1221", "endLine": 35, "endColumn": 25}, {"ruleId": "1218", "severity": 1, "message": "1379", "line": 15, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 15, "endColumn": 9}, {"ruleId": "1218", "severity": 1, "message": "1599", "line": 16, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 16, "endColumn": 19}, {"ruleId": "1218", "severity": 1, "message": "1224", "line": 17, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 17, "endColumn": 12}, {"ruleId": "1218", "severity": 1, "message": "1269", "line": 22, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 22, "endColumn": 10}, {"ruleId": "1218", "severity": 1, "message": "1600", "line": 28, "column": 15, "nodeType": "1220", "messageId": "1221", "endLine": 28, "endColumn": 27}, {"ruleId": "1218", "severity": 1, "message": "1595", "line": 29, "column": 11, "nodeType": "1220", "messageId": "1221", "endLine": 29, "endColumn": 19}, {"ruleId": "1218", "severity": 1, "message": "1316", "line": 30, "column": 15, "nodeType": "1220", "messageId": "1221", "endLine": 30, "endColumn": 27}, {"ruleId": "1218", "severity": 1, "message": "1601", "line": 41, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 41, "endColumn": 30}, {"ruleId": "1218", "severity": 1, "message": "1602", "line": 100, "column": 10, "nodeType": "1220", "messageId": "1221", "endLine": 100, "endColumn": 29}, {"ruleId": "1230", "severity": 1, "message": "1603", "line": 116, "column": 6, "nodeType": "1232", "endLine": 116, "endColumn": 16, "suggestions": "1604"}, {"ruleId": "1230", "severity": 1, "message": "1605", "line": 123, "column": 6, "nodeType": "1232", "endLine": 123, "endColumn": 34, "suggestions": "1606"}, {"ruleId": "1218", "severity": 1, "message": "1607", "line": 252, "column": 9, "nodeType": "1220", "messageId": "1221", "endLine": 252, "endColumn": 35}, {"ruleId": "1218", "severity": 1, "message": "1608", "line": 257, "column": 9, "nodeType": "1220", "messageId": "1221", "endLine": 257, "endColumn": 27}, {"ruleId": "1218", "severity": 1, "message": "1470", "line": 1, "column": 29, "nodeType": "1220", "messageId": "1221", "endLine": 1, "endColumn": 38}, {"ruleId": "1218", "severity": 1, "message": "1523", "line": 1, "column": 40, "nodeType": "1220", "messageId": "1221", "endLine": 1, "endColumn": 48}, {"ruleId": "1218", "severity": 1, "message": "1470", "line": 1, "column": 29, "nodeType": "1220", "messageId": "1221", "endLine": 1, "endColumn": 38}, {"ruleId": "1218", "severity": 1, "message": "1523", "line": 1, "column": 40, "nodeType": "1220", "messageId": "1221", "endLine": 1, "endColumn": 48}, {"ruleId": "1218", "severity": 1, "message": "1412", "line": 16, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 16, "endColumn": 7}, {"ruleId": "1218", "severity": 1, "message": "1550", "line": 24, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 24, "endColumn": 19}, {"ruleId": "1218", "severity": 1, "message": "1348", "line": 38, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 38, "endColumn": 18}, {"ruleId": "1218", "severity": 1, "message": "1609", "line": 39, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 39, "endColumn": 22}, {"ruleId": "1218", "severity": 1, "message": "1610", "line": 40, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 40, "endColumn": 17}, {"ruleId": "1218", "severity": 1, "message": "1611", "line": 41, "column": 8, "nodeType": "1220", "messageId": "1221", "endLine": 41, "endColumn": 24}, {"ruleId": "1230", "severity": 1, "message": "1603", "line": 117, "column": 6, "nodeType": "1232", "endLine": 117, "endColumn": 8, "suggestions": "1612"}, {"ruleId": "1230", "severity": 1, "message": "1613", "line": 124, "column": 6, "nodeType": "1232", "endLine": 124, "endColumn": 26, "suggestions": "1614"}, {"ruleId": "1218", "severity": 1, "message": "1615", "line": 52, "column": 9, "nodeType": "1220", "messageId": "1221", "endLine": 52, "endColumn": 17}, {"ruleId": "1218", "severity": 1, "message": "1508", "line": 9, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 9, "endColumn": 6}, {"ruleId": "1218", "severity": 1, "message": "1269", "line": 18, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 18, "endColumn": 10}, {"ruleId": "1218", "severity": 1, "message": "1616", "line": 22, "column": 3, "nodeType": "1220", "messageId": "1221", "endLine": 22, "endColumn": 11}, {"ruleId": "1218", "severity": 1, "message": "1610", "line": 27, "column": 12, "nodeType": "1220", "messageId": "1221", "endLine": 27, "endColumn": 21}, {"ruleId": "1218", "severity": 1, "message": "1617", "line": 28, "column": 19, "nodeType": "1220", "messageId": "1221", "endLine": 28, "endColumn": 28}, {"ruleId": "1230", "severity": 1, "message": "1603", "line": 91, "column": 6, "nodeType": "1232", "endLine": 91, "endColumn": 12, "suggestions": "1618"}, {"ruleId": "1230", "severity": 1, "message": "1613", "line": 101, "column": 6, "nodeType": "1232", "endLine": 101, "endColumn": 26, "suggestions": "1619"}, "@typescript-eslint/no-unused-vars", "'useContext' is defined but never used.", "Identifier", "unusedVar", "'logo' is defined but never used.", "'Button' is defined but never used.", "'TextField' is defined but never used.", "'ThreeCircles' is defined but never used.", "'PreferencesContext' is defined but never used.", "'theme' is defined but never used.", "'appToastConfig' is assigned a value but never used.", "'setAppToastConfig' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useCallback has an unnecessary dependency: 'loading'. Either exclude it or remove the dependency array.", "ArrayExpression", ["1620"], "React Hook useMemo has a missing dependency: 'setLoading'. Either include it or remove the dependency array.", ["1621"], "'setActiveMenuItem' is assigned a value but never used.", "React Hook useCallback has an unnecessary dependency: 'activeMenuItem'. Either exclude it or remove the dependency array.", ["1622"], "React Hook useEffect has missing dependencies: 'location.pathname', 'loginMessage', and 'navigate'. Either include them or remove the dependency array.", ["1623"], "React Hook useEffect has a missing dependency: 'dispatchSessionExpired'. Either include it or remove the dependency array.", ["1624"], "React Hook useCallback has an unnecessary dependency: 'open'. Either exclude it or remove the dependency array.", ["1625"], ["1626"], "React Hook useCallback has an unnecessary dependency: 'toastConfig'. Either exclude it or remove the dependency array.", ["1627"], "React Hook useMemo has missing dependencies: 'setOpen', 'setToastConfig', and 'setToastMessage'. Either include them or remove the dependency array.", ["1628"], "'PaletteOptions' is defined but never used.", "'lookupReducer' is defined but never used.", "'LOGIN' is defined but never used.", "'createRef' is defined but never used.", "'axios' is defined but never used.", "'AnyAction' is defined but never used.", "'_applicationHelperService' is assigned a value but never used.", "'yup' is defined but never used.", "'Link' is defined but never used.", "'FormControl' is defined but never used.", "'FilledInput' is defined but never used.", "'InputLabel' is defined but never used.", "'MessageConstants' is defined but never used.", "'setOpen' is assigned a value but never used.", "'handleMouseDownPassword' is assigned a value but never used.", "'handleMouseUpPassword' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'title'. Either include it or remove the dependency array.", ["1629"], "'RevenueChartDashboard' is defined but never used.", "'Divider' is defined but never used.", "'Drawer' is defined but never used.", "'openqanda' is assigned a value but never used.", "'setOpenqanda' is assigned a value but never used.", "'_locationService' is assigned a value but never used.", "'businessGroupsOnBusiness' is assigned a value but never used.", "'paginationModel' is assigned a value but never used.", "'setInitialValues' is assigned a value but never used.", "eqeqeq", "Expected '!==' and instead saw '!='.", "BinaryExpression", "unexpected", "React Hook useEffect has missing dependencies: 'fetchLocationsPaginated', 'getBusiness', and 'getBusinessGroups'. Either include them or remove the dependency array.", ["1630"], "'HomeChartCard' is defined but never used.", "'originalLocationData' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'fetchLocations', 'getBusiness', and 'getBusinessGroups'. Either include them or remove the dependency array.", ["1631"], ["1632"], "'PageProps' is defined but never used.", "'ILoginModel' is defined but never used.", "'authInitiate' is defined but never used.", "'CalendarToday' is defined but never used.", "'IFileUploadResponseModel' is defined but never used.", "'ScheduleLater' is defined but never used.", "'GenericDrawer' is defined but never used.", "'date' is assigned a value but never used.", "'setDate' is assigned a value but never used.", "'scheduleForLater' is assigned a value but never used.", "'setScheduleForLater' is assigned a value but never used.", "'selectedFromGallery' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'location.state' and 'title'. Either include them or remove the dependency array.", ["1633"], "'checkFormValidity' is assigned a value but never used.", "'isValid' is assigned a value but never used.", "'response' is assigned a value but never used.", "'formatDayJsToISO' is assigned a value but never used.", "Expected '===' and instead saw '=='.", "jsx-a11y/img-redundant-alt", "Redundant alt attribute. Screen-readers already announce `img` tags as an image. You don’t need to use the words `image`, `photo,` or `picture` (or any specified custom words) in the alt prop.", "JSXOpeningElement", "'RegisteredEmployeesChart' is defined but never used.", "'ActiveJobsChart' is defined but never used.", "'PieChart' is defined but never used.", "'GroupIcon' is defined but never used.", "'WorkHistoryIcon' is defined but never used.", "'EventAvailableIcon' is defined but never used.", "'ScheduleIcon' is defined but never used.", "'EditLocationAltIcon' is defined but never used.", "'HelpIcon' is defined but never used.", "'ArrowUpwardRoundedIcon' is defined but never used.", "'ArrowDownwardRoundedIcon' is defined but never used.", "'FormHelperText' is defined but never used.", "'Grid2' is defined but never used.", "'BusinessInteractionsChart' is defined but never used.", "'SearchQueriesList' is defined but never used.", "'rbAccess' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchLocations'. Either include it or remove the dependency array.", ["1634"], "React Hook useEffect has a missing dependency: 'locationList'. Either include it or remove the dependency array. You can also replace multiple useState variables with useReducer if 'setSelectedAccountId' needs the current value of 'locationList'.", ["1635"], "React Hook useEffect has missing dependencies: 'fetchAnalyticsData', 'locationList', and 'selectedAccountId'. Either include them or remove the dependency array.", ["1636"], "'IconButton' is defined but never used.", "'Dialog' is defined but never used.", "'AddIcon' is defined but never used.", "'ServicesDisplay' is defined but never used.", "'Accessibility' is defined but never used.", "'FlagIcon' is defined but never used.", "'ChatIcon' is defined but never used.", "'LanguageIcon' is defined but never used.", "'LocationOnIcon' is defined but never used.", "'useCallback' is defined but never used.", "'Container' is defined but never used.", "'activeTab' is assigned a value but never used.", "'setActiveTab' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadSavedConfigurations'. Either include it or remove the dependency array.", ["1637"], "'Modal' is defined but never used.", "'DeleteIcon' is defined but never used.", "'SendIcon' is defined but never used.", "'DeleteOutlineIcon' is defined but never used.", "React Hook useEffect has missing dependencies: 'navigate' and 'userInfo.roleId'. Either include them or remove the dependency array.", ["1638"], "React Hook useEffect has a missing dependency: 'getRolesList'. Either include it or remove the dependency array.", ["1639"], "'SearchOffIcon' is defined but never used.", "'TableRowsRoundedIcon' is defined but never used.", "'IUsersListResponse' is defined but never used.", "'Grid' is defined but never used.", "'Stack' is defined but never used.", "'TablePagination' is defined but never used.", "'searchText' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchUsersPaginated'. Either include it or remove the dependency array.", ["1640"], "'rowsPerPage' is assigned a value but never used.", "'handleChangePage' is assigned a value but never used.", "'handleChangeRowsPerPage' is assigned a value but never used.", "'BlockOutlinedIcon' is defined but never used.", "'CheckCircleRoundedIcon' is defined but never used.", "'IBusinessListResponseModel' is defined but never used.", "'FormGroup' is defined but never used.", "'VerifiedIcon' is defined but never used.", "'PauseCircleFilledIcon' is defined but never used.", "'CheckCircleIcon' is defined but never used.", "'setAlertConfig' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'fetchBusinessPaginated', 'title', and 'userInfo'. Either include them or remove the dependency array.", ["1641"], "'List' is defined but never used.", "'ListItem' is defined but never used.", "'Switch' is defined but never used.", "'businessPreview' is defined but never used.", "'LockOpenIcon' is defined but never used.", "'useSelector' is defined but never used.", "'CancelOutlinedIcon' is defined but never used.", "'CampaignRoundedIcon' is defined but never used.", "'label' is assigned a value but never used.", "'StatusCardProps' is defined but never used.", "'progress' is assigned a value but never used.", "'setProgress' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'accountId', 'businessId', 'getLocationSummary', and 'locationId'. Either include them or remove the dependency array.", ["1642"], "React Hook useEffect has a missing dependency: 'performMissingInformationOperation'. Either include it or remove the dependency array.", ["1643"], "'CardMedia' is defined but never used.", "'showConfirmPopup' is assigned a value but never used.", "'setShowConfirmPopup' is assigned a value but never used.", ["1644"], "React Hook useEffect has a missing dependency: 'fetchLocationsPaginated'. Either include it or remove the dependency array.", ["1645"], "React Hook useEffect has missing dependencies: 'getBusiness' and 'getBusinessGroups'. Either include them or remove the dependency array.", ["1646"], "'FallingLines' is defined but never used.", "'RotatingLines' is defined but never used.", "'IDeleteRecord' is defined but never used.", "React Hook useEffect has a missing dependency: 'gmbCallBack'. Either include it or remove the dependency array.", ["1647"], "'StarBorderIcon' is defined but never used.", "'Avatar' is defined but never used.", "'StarRoundedIcon' is defined but never used.", "'ILocationListRequestModel' is defined but never used.", "'STARRATINGMAP' is defined but never used.", "'UserAvatar' is defined but never used.", "'Chip' is defined but never used.", "'SearchOutlinedIcon' is defined but never used.", "'InputAdornment' is defined but never used.", "'newestIcon' is assigned a value but never used.", "'oldestIcon' is assigned a value but never used.", "'highRatingIcon' is assigned a value but never used.", "'lowRatingIcon' is assigned a value but never used.", "'showScroll' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'fetchLocationsPaginated', 'getAllTags', 'getBusiness', and 'getBusinessGroups'. Either include them or remove the dependency array.", ["1648"], "'scrollToTop' is assigned a value but never used.", "'handleClick' is assigned a value but never used.", "'handleSearch' is assigned a value but never used.", "'AUTH_REQUESTED' is defined but never used.", "'AUTH_SUCCESS' is defined but never used.", "'AUTH_LOGOUT' is defined but never used.", "'AUTH_ERROR' is defined but never used.", "'AUTH_UNAUTHORIZED' is defined but never used.", "'IRoleBasedAccessResponseModel' is defined but never used.", "'ILoggedInUserResponseModel' is defined but never used.", "'Card' is defined but never used.", "'CardContent' is defined but never used.", "'Pagination' is defined but never used.", "'Paper' is defined but never used.", "'OutlinedInput' is defined but never used.", "'getIn' is defined but never used.", "React Hook useEffect has missing dependencies: 'getBusiness', 'getBusinessGroups', and 'getLocationsList'. Either include them or remove the dependency array.", ["1649"], "'MenuProps' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'logoutUser'. Either include it or remove the dependency array.", ["1650"], ["1651"], "'Component' is defined but never used.", "no-useless-escape", "Unnecessary escape character: \\..", "Literal", "unnecessaryEscape", ["1652", "1653"], ["1654", "1655"], "'Toolbar' is defined but never used.", "'Typography' is defined but never used.", "'MenuIcon' is defined but never used.", "'Menu' is defined but never used.", "'MenuItem' is defined but never used.", "'ManageAccountsIcon' is defined but never used.", "'Collapse' is defined but never used.", "'SettingsOutlinedIcon' is defined but never used.", "'ArrowForwardIosRoundedIcon' is defined but never used.", "'ListAltSharp' is defined but never used.", "'MapsUgcRoundedIcon' is defined but never used.", "'AppBar' is assigned a value but never used.", "'theme' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'isMobile', 'menuOpened', 'openLeftMenu', 'rbAccess', and 'userInfo'. Either include them or remove the dependency array.", ["1656"], "'handleMenuItemClick' is assigned a value but never used.", "'GeoGridRoutes' is assigned a value but never used.", ["1657", "1658"], ["1659", "1660"], "'useEffect' is defined but never used.", "'useMemo' is defined but never used.", "'MONTHS' is assigned a value but never used.", "'PolarArea' is defined but never used.", "'Bar' is defined but never used.", "'ReviewService' is defined but never used.", "'title' is defined but never used.", "React Hook useEffect has missing dependencies: 'onDateChange' and 'selectedDuration'. Either include them or remove the dependency array. If 'onDateChange' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["1661"], "'ListItemText' is defined but never used.", "'AppBar' is defined but never used.", "'RoleType' is defined but never used.", "'CloseIcon' is defined but never used.", "'ArrowBackIos' is defined but never used.", "'ArrowForwardIos' is defined but never used.", "'LinearProgressWithLabel' is defined but never used.", "'open' is assigned a value but never used.", "'selectedValue' is assigned a value but never used.", "'selectedOptions' is assigned a value but never used.", "'handleOpen' is assigned a value but never used.", "'handleClose' is assigned a value but never used.", "'handleDropdownChange' is assigned a value but never used.", "'handleMultiSelectChange' is assigned a value but never used.", "'currentIndex' is assigned a value but never used.", "'handleNext' is assigned a value but never used.", "'handlePrev' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'getBusiness' and 'getLocationsList'. Either include them or remove the dependency array.", ["1662"], "React Hook useEffect has a missing dependency: 'initialValues'. Either include it or remove the dependency array.", ["1663"], "'LoadingContext' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchSearchKeywords'. Either include it or remove the dependency array.", ["1664"], "React Hook useEffect has a missing dependency: 'fetchMoreData'. Either include it or remove the dependency array.", ["1665"], "'SAVE_SCHEDULED' is defined but never used.", "'IGoogleCreatePost' is defined but never used.", "'DialogContent' is defined but never used.", "'Box' is defined but never used.", "'ListItemButton' is defined but never used.", "'ArrowBackIcon' is defined but never used.", "'ChevronRightIcon' is defined but never used.", "'MoreVertIcon' is defined but never used.", "'Formik' is defined but never used.", "'Form' is defined but never used.", "'Category' is defined but never used.", "'DialogTitle' is defined but never used.", "'AdapterDayjs' is defined but never used.", "'LocalizationProvider' is defined but never used.", "'DatePicker' is defined but never used.", "'ZoomInIcon' is defined but never used.", "'LIST_OF_LOCATIONS' is defined but never used.", "'LIST_OF_ROLE' is defined but never used.", "'useState' is defined but never used.", "'useNavigate' is defined but never used.", "'IUserResponseModel' is defined but never used.", "'IUser' is defined but never used.", "'IAlertDialogConfig' is defined but never used.", "'logOut' is defined but never used.", "'AlertDialog' is defined but never used.", "'isEdit' is assigned a value but never used.", "'setIsEdit' is assigned a value but never used.", "React Hook useEffect has missing dependencies: '_userService', 'getBusiness', 'getBusinessGroups', 'getLocationsList', and 'props.editData'. Either include them or remove the dependency array.", ["1666"], ["1667"], "'LIST_OF_BUSINESS' is defined but never used.", "'navigate' is assigned a value but never used.", "'Badge' is defined but never used.", "'Select' is defined but never used.", "'SelectChangeEvent' is defined but never used.", "'IRole' is defined but never used.", "'IBusinessGroup' is defined but never used.", "'ILocation' is defined but never used.", "'usersList' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchUsers'. Either include it or remove the dependency array.", ["1668"], "array-callback-return", "Array.prototype.map() expects a value to be returned at the end of arrow function.", "ArrowFunctionExpression", "expectedAtEnd", "'CircularProgress' is defined but never used.", "'MediaGallery' is defined but never used.", "'MISSING_INFORMATION' is defined but never used.", "'IconOnAvailability' is defined but never used.", "'LocationOnRoundedIcon' is defined but never used.", "'MovieIcon' is defined but never used.", "'AccountCircleIcon' is defined but never used.", "'NearMeOutlinedIcon' is defined but never used.", "'handleOpenMap' is assigned a value but never used.", "'FunctionComponent' is defined but never used.", "'ThumbUpAltRoundedIcon' is defined but never used.", "'FeedbackTemplate' is defined but never used.", "'FeedbackCard' is defined but never used.", "'CssBaseline' is defined but never used.", "'ImageBackgroundCard' is defined but never used.", "React Hook useEffect has missing dependencies: 'props.review.review', 'props.review.reviewerName', 'props.review.reviewerProfilePic', and 'props.review.starRating'. Either include them or remove the dependency array. If 'setPostTemplateConfig' needs the current value of 'props.review.review', you can also switch to useReducer instead of useState and read 'props.review.review' in the reducer.", ["1669"], "React Hook useEffect has a missing dependency: 'getAllTags'. Either include it or remove the dependency array.", ["1670"], "'createTag' is assigned a value but never used.", "'updateTagsToReviewResponse' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'size'. Either include it or remove the dependency array.", ["1671"], "no-lone-blocks", "Nested block is redundant.", "BlockStatement", "redundantNestedBlock", "'navigationType' is assigned a value but never used.", "'userInfo' is assigned a value but never used.", "'isValidElement' is defined but never used.", "'activeMenuItem' is assigned a value but never used.", "'LogoutIcon' is defined but never used.", "'logoutUser' is assigned a value but never used.", "'openSubMenu' is assigned a value but never used.", "React Hook useEffect has missing dependencies: '_locationService', 'props.mediaItems', and 'setLoading'. Either include them or remove the dependency array.", ["1672"], "React Hook useEffect has a missing dependency: 'props.profileImage'. Either include it or remove the dependency array.", ["1673"], "'fontType' is assigned a value but never used.", "'setFontType' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'props'. Either include it or remove the dependency array. However, 'props' will change when *any* prop changes, so the preferred fix is to destructure the 'props' object outside of the useEffect call and refer to those specific props inside useEffect.", ["1674"], ["1675"], "'ref' is defined but never used.", "'Rating' is defined but never used.", "'StarIcon' is defined but never used.", "jsx-a11y/alt-text", "img elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", "'availableLocations' is assigned a value but never used.", "'FormControlLabel' is defined but never used.", "'SettingsIcon' is defined but never used.", "'ICreateReplyTemplateRequest' is defined but never used.", "'openAutoReplyDrawer' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadBusinesses'. Either include it or remove the dependency array.", ["1676"], "React Hook useEffect has missing dependencies: 'loadAutoReplySettings' and 'loadTemplates'. Either include them or remove the dependency array.", ["1677"], "'handleAutoReplyDrawerClose' is assigned a value but never used.", "'getStarRatingColor' is assigned a value but never used.", "'VisibilityIcon' is defined but never used.", "'ImageIcon' is defined but never used.", "'VideoLibraryIcon' is defined but never used.", ["1678"], "React Hook useEffect has a missing dependency: 'loadAssets'. Either include it or remove the dependency array.", ["1679"], "'isMobile' is assigned a value but never used.", "'Checkbox' is defined but never used.", "'VideoIcon' is defined but never used.", ["1680"], ["1681"], {"desc": "1682", "fix": "1683"}, {"desc": "1684", "fix": "1685"}, {"desc": "1682", "fix": "1686"}, {"desc": "1687", "fix": "1688"}, {"desc": "1689", "fix": "1690"}, {"desc": "1682", "fix": "1691"}, {"desc": "1682", "fix": "1692"}, {"desc": "1682", "fix": "1693"}, {"desc": "1694", "fix": "1695"}, {"desc": "1696", "fix": "1697"}, {"desc": "1698", "fix": "1699"}, {"desc": "1700", "fix": "1701"}, {"kind": "1702", "justification": "1703"}, {"desc": "1704", "fix": "1705"}, {"desc": "1706", "fix": "1707"}, {"desc": "1708", "fix": "1709"}, {"desc": "1710", "fix": "1711"}, {"desc": "1712", "fix": "1713"}, {"desc": "1714", "fix": "1715"}, {"desc": "1716", "fix": "1717"}, {"desc": "1718", "fix": "1719"}, {"desc": "1720", "fix": "1721"}, {"desc": "1722", "fix": "1723"}, {"desc": "1724", "fix": "1725"}, {"desc": "1696", "fix": "1726"}, {"desc": "1727", "fix": "1728"}, {"desc": "1729", "fix": "1730"}, {"desc": "1731", "fix": "1732"}, {"desc": "1733", "fix": "1734"}, {"desc": "1735", "fix": "1736"}, {"desc": "1737", "fix": "1738"}, {"desc": "1737", "fix": "1739"}, {"messageId": "1740", "fix": "1741", "desc": "1742"}, {"messageId": "1743", "fix": "1744", "desc": "1745"}, {"messageId": "1740", "fix": "1746", "desc": "1742"}, {"messageId": "1743", "fix": "1747", "desc": "1745"}, {"desc": "1748", "fix": "1749"}, {"messageId": "1740", "fix": "1750", "desc": "1742"}, {"messageId": "1743", "fix": "1751", "desc": "1745"}, {"messageId": "1740", "fix": "1752", "desc": "1742"}, {"messageId": "1743", "fix": "1753", "desc": "1745"}, {"desc": "1754", "fix": "1755"}, {"desc": "1756", "fix": "1757"}, {"desc": "1758", "fix": "1759"}, {"desc": "1760", "fix": "1761"}, {"desc": "1762", "fix": "1763"}, {"desc": "1764", "fix": "1765"}, {"desc": "1716", "fix": "1766"}, {"desc": "1767", "fix": "1768"}, {"desc": "1769", "fix": "1770"}, {"desc": "1771", "fix": "1772"}, {"desc": "1773", "fix": "1774"}, {"desc": "1775", "fix": "1776"}, {"desc": "1777", "fix": "1778"}, {"desc": "1779", "fix": "1780"}, {"desc": "1779", "fix": "1781"}, {"desc": "1782", "fix": "1783"}, {"desc": "1784", "fix": "1785"}, {"desc": "1786", "fix": "1787"}, {"desc": "1788", "fix": "1789"}, {"desc": "1790", "fix": "1791"}, {"desc": "1788", "fix": "1792"}, "Update the dependencies array to be: []", {"range": "1793", "text": "1794"}, "Update the dependencies array to be: [loading, setLoading]", {"range": "1795", "text": "1796"}, {"range": "1797", "text": "1794"}, "Update the dependencies array to be: [userInfo, success, location.pathname, navigate, loginMessage]", {"range": "1798", "text": "1799"}, "Update the dependencies array to be: [dispatchSessionExpired, isUnAuthorised]", {"range": "1800", "text": "1801"}, {"range": "1802", "text": "1794"}, {"range": "1803", "text": "1794"}, {"range": "1804", "text": "1794"}, "Update the dependencies array to be: [open, setOpen, message, setToastMessage, toastConfig, setToastConfig]", {"range": "1805", "text": "1806"}, "Update the dependencies array to be: [title]", {"range": "1807", "text": "1808"}, "Update the dependencies array to be: [fetchLocationsPaginated, getBusiness, getBusinessGroups]", {"range": "1809", "text": "1810"}, "Update the dependencies array to be: [fetchLocations, getBusiness, getBusinessGroups]", {"range": "1811", "text": "1812"}, "directive", "", "Update the dependencies array to be: [location.state, title]", {"range": "1813", "text": "1814"}, "Update the dependencies array to be: [fetchLocations]", {"range": "1815", "text": "1816"}, "Update the dependencies array to be: [locationList, selectedLocationId]", {"range": "1817", "text": "1818"}, "Update the dependencies array to be: [selectedLocationId, selectedDateRange, selectedAccountId, fetchAnalyticsData, locationList]", {"range": "1819", "text": "1820"}, "Update the dependencies array to be: [loadSavedConfigurations, title, user]", {"range": "1821", "text": "1822"}, "Update the dependencies array to be: [navigate, userInfo.roleId]", {"range": "1823", "text": "1824"}, "Update the dependencies array to be: [getRolesList]", {"range": "1825", "text": "1826"}, "Update the dependencies array to be: [fetchUsersPaginated, paginationModel]", {"range": "1827", "text": "1828"}, "Update the dependencies array to be: [fetchBusinessPaginated, title, userInfo]", {"range": "1829", "text": "1830"}, "Update the dependencies array to be: [accountId, businessId, getLocationSummary, locationId]", {"range": "1831", "text": "1832"}, "Update the dependencies array to be: [locationSummary, performMissingInformationOperation]", {"range": "1833", "text": "1834"}, {"range": "1835", "text": "1808"}, "Update the dependencies array to be: [fetchLocationsPaginated, paginationModel]", {"range": "1836", "text": "1837"}, "Update the dependencies array to be: [getBusiness, getBusinessGroups]", {"range": "1838", "text": "1839"}, "Update the dependencies array to be: [gmbCallBack, searchParams]", {"range": "1840", "text": "1841"}, "Update the dependencies array to be: [fetchLocationsPaginated, getAllTags, getBusiness, getBusinessGroups]", {"range": "1842", "text": "1843"}, "Update the dependencies array to be: [getBusiness, getBusinessGroups, getLocationsList]", {"range": "1844", "text": "1845"}, "Update the dependencies array to be: [logoutUser, navigate]", {"range": "1846", "text": "1847"}, {"range": "1848", "text": "1847"}, "removeEscape", {"range": "1849", "text": "1703"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "1850", "text": "1851"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", {"range": "1852", "text": "1703"}, {"range": "1853", "text": "1851"}, "Update the dependencies array to be: [isMobile, menuOpened, openLeftMenu, rbAccess, userInfo]", {"range": "1854", "text": "1855"}, {"range": "1856", "text": "1703"}, {"range": "1857", "text": "1851"}, {"range": "1858", "text": "1703"}, {"range": "1859", "text": "1851"}, "Update the dependencies array to be: [onDateChange, selectedDuration]", {"range": "1860", "text": "1861"}, "Update the dependencies array to be: [getBusiness, getLocationsList]", {"range": "1862", "text": "1863"}, "Update the dependencies array to be: [initialValues.locationId, initialValues.accountId, locations, initialValues]", {"range": "1864", "text": "1865"}, "Update the dependencies array to be: [accountId, locationId, from, to, fetchSearchKeywords]", {"range": "1866", "text": "1867"}, "Update the dependencies array to be: [nextPageToken, loading, open, fetchMoreData]", {"range": "1868", "text": "1869"}, "Update the dependencies array to be: [_userService, getBusiness, getBusinessGroups, getLocationsList, props.editData]", {"range": "1870", "text": "1871"}, {"range": "1872", "text": "1826"}, "Update the dependencies array to be: [fetchUsers]", {"range": "1873", "text": "1874"}, "Update the dependencies array to be: [props.review.review, props.review.reviewerName, props.review.reviewerProfilePic, props.review.starRating]", {"range": "1875", "text": "1876"}, "Update the dependencies array to be: [getAllTags]", {"range": "1877", "text": "1878"}, "Update the dependencies array to be: [size]", {"range": "1879", "text": "1880"}, "Update the dependencies array to be: [_locationService, props.mediaItems, setLoading]", {"range": "1881", "text": "1882"}, "Update the dependencies array to be: [props.profileImage]", {"range": "1883", "text": "1884"}, "Update the dependencies array to be: [postTemplateConfig, props]", {"range": "1885", "text": "1886"}, {"range": "1887", "text": "1886"}, "Update the dependencies array to be: [loadBusinesses, userInfo]", {"range": "1888", "text": "1889"}, "Update the dependencies array to be: [loadAutoReplySettings, loadTemplates, selectedBusiness, userInfo]", {"range": "1890", "text": "1891"}, "Update the dependencies array to be: [loadBusinesses]", {"range": "1892", "text": "1893"}, "Update the dependencies array to be: [loadAssets, selectedBusinessId]", {"range": "1894", "text": "1895"}, "Update the dependencies array to be: [loadBusinesses, open]", {"range": "1896", "text": "1897"}, {"range": "1898", "text": "1895"}, [3723, 3732], "[]", [3836, 3845], "[loading, setLoading]", [4126, 4142], [4801, 4820], "[userInfo, success, location.pathname, navigate, loginMessage]", [5072, 5088], "[dispatchSessionExpired, isUnAuthorised]", [5603, 5609], [5734, 5740], [5923, 5936], [6115, 6143], "[open, setOpen, message, setToastMessage, toastConfig, setToastConfig]", [2979, 2981], "[title]", [5326, 5328], "[fetchLocationsPaginated, getBusiness, getBusinessGroups]", [5424, 5426], "[fetchLocations, getBusiness, getBusinessGroups]", [9700, 9702], "[location.state, title]", [4843, 4845], "[fetchLocations]", [5049, 5069], "[locationList, selectedLocationId]", [5446, 5485], "[selectedLocationId, selectedDateRange, selectedAccountId, fetchAnalyticsData, locationList]", [2227, 2240], "[loadSavedConfigurations, title, user]", [3559, 3561], "[navigate, userInfo.roleId]", [3614, 3616], "[getRolesList]", [5200, 5217], "[fetchUsersPaginated, paginationModel]", [5187, 5189], "[fetchBusinessPaginated, title, userInfo]", [5063, 5065], "[accountId, businessId, getLocationSummary, locationId]", [8294, 8311], "[locationSummary, performMissingInformationOperation]", [5560, 5562], [5626, 5643], "[fetchLocationsPaginated, paginationModel]", [5820, 5822], "[getBusiness, getBusinessGroups]", [1934, 1948], "[gmbCallBack, searchParams]", [7854, 7856], "[fetchLocationsPaginated, getAllTags, getBusiness, getBusinessGroups]", [4079, 4081], "[getBusiness, getBusinessGroups, getLocationsList]", [904, 914], "[logo<PERSON><PERSON><PERSON>, navigate]", [905, 915], [184, 185], [184, 184], "\\", [200, 201], [200, 200], [6518, 6520], "[isMobile, menuOpened, openLeftMenu, rbAccess, userInfo]", [288, 289], [288, 288], [304, 305], [304, 304], [1857, 1859], "[onDate<PERSON><PERSON>e, selectedDuration]", [6903, 6905], "[getBusiness, getLocationsList]", [7653, 7715], "[initialValues.locationId, initialValues.accountId, locations, initialValues]", [1519, 1552], "[accountId, locationId, from, to, fetchSearchKeywords]", [3743, 3773], "[nextPageToken, loading, open, fetchMoreData]", [5857, 5859], "[_userService, getBusiness, getBusinessGroups, getLocationsList, props.editData]", [12623, 12625], [3571, 3573], "[fetchUsers]", [2956, 2958], "[props.review.review, props.review.reviewerName, props.review.reviewerProfilePic, props.review.starRating]", [2469, 2471], "[getAllTags]", [497, 499], "[size]", [1388, 1390], "[_locationService, props.mediaItems, setLoading]", [1123, 1125], "[props.profileImage]", [1210, 1230], "[postTemplateConfig, props]", [6340, 6360], [3585, 3595], "[loadBusinesses, userInfo]", [3728, 3756], "[loadAutoReplySettings, loadTemplates, selectedBusiness, userInfo]", [3619, 3621], "[loadBusinesses]", [3750, 3770], "[loadAssets, selectedBusinessId]", [2388, 2394], "[loadBusinesses, open]", [2586, 2606]]