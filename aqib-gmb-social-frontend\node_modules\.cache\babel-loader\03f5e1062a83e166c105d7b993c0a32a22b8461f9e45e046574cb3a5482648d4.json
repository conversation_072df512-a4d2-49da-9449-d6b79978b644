{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport Autocomplete from '@mui/material/Autocomplete';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nlet warnedOnce = false;\n\n/**\n * @ignore - do not document.\n */\nexport default /*#__PURE__*/React.forwardRef(function DeprecatedAutocomplete(props, ref) {\n  if (!warnedOnce) {\n    console.warn(['MUI: The Autocomplete component was moved from the lab to the core.', '', \"You should use `import { Autocomplete } from '@mui/material'`\", \"or `import Autocomplete from '@mui/material/Autocomplete'`\"].join('\\n'));\n    warnedOnce = true;\n  }\n  return /*#__PURE__*/_jsx(Autocomplete, _extends({\n    ref: ref\n  }, props));\n});", "map": {"version": 3, "names": ["_extends", "React", "Autocomplete", "jsx", "_jsx", "warnedOnce", "forwardRef", "DeprecatedAutocomplete", "props", "ref", "console", "warn", "join"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/node_modules/@mui/lab/Autocomplete/Autocomplete.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport Autocomplete from '@mui/material/Autocomplete';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nlet warnedOnce = false;\n\n/**\n * @ignore - do not document.\n */\nexport default /*#__PURE__*/React.forwardRef(function DeprecatedAutocomplete(props, ref) {\n  if (!warnedOnce) {\n    console.warn(['MUI: The Autocomplete component was moved from the lab to the core.', '', \"You should use `import { Autocomplete } from '@mui/material'`\", \"or `import Autocomplete from '@mui/material/Autocomplete'`\"].join('\\n'));\n    warnedOnce = true;\n  }\n  return /*#__PURE__*/_jsx(Autocomplete, _extends({\n    ref: ref\n  }, props));\n});"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,YAAY,MAAM,4BAA4B;AACrD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,IAAIC,UAAU,GAAG,KAAK;;AAEtB;AACA;AACA;AACA,eAAe,aAAaJ,KAAK,CAACK,UAAU,CAAC,SAASC,sBAAsBA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACvF,IAAI,CAACJ,UAAU,EAAE;IACfK,OAAO,CAACC,IAAI,CAAC,CAAC,qEAAqE,EAAE,EAAE,EAAE,+DAA+D,EAAE,4DAA4D,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;IACnOP,UAAU,GAAG,IAAI;EACnB;EACA,OAAO,aAAaD,IAAI,CAACF,YAAY,EAAEF,QAAQ,CAAC;IAC9CS,GAAG,EAAEA;EACP,CAAC,EAAED,KAAK,CAAC,CAAC;AACZ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}