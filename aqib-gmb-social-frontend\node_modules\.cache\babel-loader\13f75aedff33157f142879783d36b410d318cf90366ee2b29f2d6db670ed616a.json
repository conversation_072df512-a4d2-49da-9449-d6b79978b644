{"ast": null, "code": "'use client';\n\n/* eslint-disable no-constant-condition */\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { unstable_setRef as setRef, unstable_useEventCallback as useEventCallback, unstable_useControlled as useControlled, unstable_useId as useId, usePreviousProps } from '@mui/utils';\n\n// https://stackoverflow.com/questions/990904/remove-accents-diacritics-in-a-string-in-javascript\n// Give up on IE11 support for this feature\nfunction stripDiacritics(string) {\n  return typeof string.normalize !== 'undefined' ? string.normalize('NFD').replace(/[\\u0300-\\u036f]/g, '') : string;\n}\nexport function createFilterOptions() {\n  let config = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  const {\n    ignoreAccents = true,\n    ignoreCase = true,\n    limit,\n    matchFrom = 'any',\n    stringify,\n    trim = false\n  } = config;\n  return (options, _ref) => {\n    let {\n      inputValue,\n      getOptionLabel\n    } = _ref;\n    let input = trim ? inputValue.trim() : inputValue;\n    if (ignoreCase) {\n      input = input.toLowerCase();\n    }\n    if (ignoreAccents) {\n      input = stripDiacritics(input);\n    }\n    const filteredOptions = !input ? options : options.filter(option => {\n      let candidate = (stringify || getOptionLabel)(option);\n      if (ignoreCase) {\n        candidate = candidate.toLowerCase();\n      }\n      if (ignoreAccents) {\n        candidate = stripDiacritics(candidate);\n      }\n      return matchFrom === 'start' ? candidate.indexOf(input) === 0 : candidate.indexOf(input) > -1;\n    });\n    return typeof limit === 'number' ? filteredOptions.slice(0, limit) : filteredOptions;\n  };\n}\n\n// To replace with .findIndex() once we stop IE11 support.\nfunction findIndex(array, comp) {\n  for (let i = 0; i < array.length; i += 1) {\n    if (comp(array[i])) {\n      return i;\n    }\n  }\n  return -1;\n}\nconst defaultFilterOptions = createFilterOptions();\n\n// Number of options to jump in list box when `Page Up` and `Page Down` keys are used.\nconst pageSize = 5;\nconst defaultIsActiveElementInListbox = listboxRef => {\n  var _listboxRef$current$p;\n  return listboxRef.current !== null && ((_listboxRef$current$p = listboxRef.current.parentElement) == null ? void 0 : _listboxRef$current$p.contains(document.activeElement));\n};\nconst MULTIPLE_DEFAULT_VALUE = [];\nexport function useAutocomplete(props) {\n  const {\n    // eslint-disable-next-line @typescript-eslint/naming-convention\n    unstable_isActiveElementInListbox = defaultIsActiveElementInListbox,\n    // eslint-disable-next-line @typescript-eslint/naming-convention\n    unstable_classNamePrefix = 'Mui',\n    autoComplete = false,\n    autoHighlight = false,\n    autoSelect = false,\n    blurOnSelect = false,\n    clearOnBlur = !props.freeSolo,\n    clearOnEscape = false,\n    componentName = 'useAutocomplete',\n    defaultValue = props.multiple ? MULTIPLE_DEFAULT_VALUE : null,\n    disableClearable = false,\n    disableCloseOnSelect = false,\n    disabled: disabledProp,\n    disabledItemsFocusable = false,\n    disableListWrap = false,\n    filterOptions = defaultFilterOptions,\n    filterSelectedOptions = false,\n    freeSolo = false,\n    getOptionDisabled,\n    getOptionKey,\n    getOptionLabel: getOptionLabelProp = option => {\n      var _option$label;\n      return (_option$label = option.label) != null ? _option$label : option;\n    },\n    groupBy,\n    handleHomeEndKeys = !props.freeSolo,\n    id: idProp,\n    includeInputInList = false,\n    inputValue: inputValueProp,\n    isOptionEqualToValue = (option, value) => option === value,\n    multiple = false,\n    onChange,\n    onClose,\n    onHighlightChange,\n    onInputChange,\n    onOpen,\n    open: openProp,\n    openOnFocus = false,\n    options,\n    readOnly = false,\n    selectOnFocus = !props.freeSolo,\n    value: valueProp\n  } = props;\n  const id = useId(idProp);\n  let getOptionLabel = getOptionLabelProp;\n  getOptionLabel = option => {\n    const optionLabel = getOptionLabelProp(option);\n    if (typeof optionLabel !== 'string') {\n      if (process.env.NODE_ENV !== 'production') {\n        const erroneousReturn = optionLabel === undefined ? 'undefined' : \"\".concat(typeof optionLabel, \" (\").concat(optionLabel, \")\");\n        console.error(\"MUI: The `getOptionLabel` method of \".concat(componentName, \" returned \").concat(erroneousReturn, \" instead of a string for \").concat(JSON.stringify(option), \".\"));\n      }\n      return String(optionLabel);\n    }\n    return optionLabel;\n  };\n  const ignoreFocus = React.useRef(false);\n  const firstFocus = React.useRef(true);\n  const inputRef = React.useRef(null);\n  const listboxRef = React.useRef(null);\n  const [anchorEl, setAnchorEl] = React.useState(null);\n  const [focusedTag, setFocusedTag] = React.useState(-1);\n  const defaultHighlighted = autoHighlight ? 0 : -1;\n  const highlightedIndexRef = React.useRef(defaultHighlighted);\n  const [value, setValueState] = useControlled({\n    controlled: valueProp,\n    default: defaultValue,\n    name: componentName\n  });\n  const [inputValue, setInputValueState] = useControlled({\n    controlled: inputValueProp,\n    default: '',\n    name: componentName,\n    state: 'inputValue'\n  });\n  const [focused, setFocused] = React.useState(false);\n  const resetInputValue = React.useCallback((event, newValue) => {\n    // retain current `inputValue` if new option isn't selected and `clearOnBlur` is false\n    // When `multiple` is enabled, `newValue` is an array of all selected items including the newly selected item\n    const isOptionSelected = multiple ? value.length < newValue.length : newValue !== null;\n    if (!isOptionSelected && !clearOnBlur) {\n      return;\n    }\n    let newInputValue;\n    if (multiple) {\n      newInputValue = '';\n    } else if (newValue == null) {\n      newInputValue = '';\n    } else {\n      const optionLabel = getOptionLabel(newValue);\n      newInputValue = typeof optionLabel === 'string' ? optionLabel : '';\n    }\n    if (inputValue === newInputValue) {\n      return;\n    }\n    setInputValueState(newInputValue);\n    if (onInputChange) {\n      onInputChange(event, newInputValue, 'reset');\n    }\n  }, [getOptionLabel, inputValue, multiple, onInputChange, setInputValueState, clearOnBlur, value]);\n  const [open, setOpenState] = useControlled({\n    controlled: openProp,\n    default: false,\n    name: componentName,\n    state: 'open'\n  });\n  const [inputPristine, setInputPristine] = React.useState(true);\n  const inputValueIsSelectedValue = !multiple && value != null && inputValue === getOptionLabel(value);\n  const popupOpen = open && !readOnly;\n  const filteredOptions = popupOpen ? filterOptions(options.filter(option => {\n    if (filterSelectedOptions && (multiple ? value : [value]).some(value2 => value2 !== null && isOptionEqualToValue(option, value2))) {\n      return false;\n    }\n    return true;\n  }),\n  // we use the empty string to manipulate `filterOptions` to not filter any options\n  // i.e. the filter predicate always returns true\n  {\n    inputValue: inputValueIsSelectedValue && inputPristine ? '' : inputValue,\n    getOptionLabel\n  }) : [];\n  const previousProps = usePreviousProps({\n    filteredOptions,\n    value,\n    inputValue\n  });\n  React.useEffect(() => {\n    const valueChange = value !== previousProps.value;\n    if (focused && !valueChange) {\n      return;\n    }\n\n    // Only reset the input's value when freeSolo if the component's value changes.\n    if (freeSolo && !valueChange) {\n      return;\n    }\n    resetInputValue(null, value);\n  }, [value, resetInputValue, focused, previousProps.value, freeSolo]);\n  const listboxAvailable = open && filteredOptions.length > 0 && !readOnly;\n  if (process.env.NODE_ENV !== 'production') {\n    if (value !== null && !freeSolo && options.length > 0) {\n      const missingValue = (multiple ? value : [value]).filter(value2 => !options.some(option => isOptionEqualToValue(option, value2)));\n      if (missingValue.length > 0) {\n        console.warn([\"MUI: The value provided to \".concat(componentName, \" is invalid.\"), \"None of the options match with `\".concat(missingValue.length > 1 ? JSON.stringify(missingValue) : JSON.stringify(missingValue[0]), \"`.\"), 'You can use the `isOptionEqualToValue` prop to customize the equality test.'].join('\\n'));\n      }\n    }\n  }\n  const focusTag = useEventCallback(tagToFocus => {\n    if (tagToFocus === -1) {\n      inputRef.current.focus();\n    } else {\n      anchorEl.querySelector(\"[data-tag-index=\\\"\".concat(tagToFocus, \"\\\"]\")).focus();\n    }\n  });\n\n  // Ensure the focusedTag is never inconsistent\n  React.useEffect(() => {\n    if (multiple && focusedTag > value.length - 1) {\n      setFocusedTag(-1);\n      focusTag(-1);\n    }\n  }, [value, multiple, focusedTag, focusTag]);\n  function validOptionIndex(index, direction) {\n    if (!listboxRef.current || index < 0 || index >= filteredOptions.length) {\n      return -1;\n    }\n    let nextFocus = index;\n    while (true) {\n      const option = listboxRef.current.querySelector(\"[data-option-index=\\\"\".concat(nextFocus, \"\\\"]\"));\n\n      // Same logic as MenuList.js\n      const nextFocusDisabled = disabledItemsFocusable ? false : !option || option.disabled || option.getAttribute('aria-disabled') === 'true';\n      if (option && option.hasAttribute('tabindex') && !nextFocusDisabled) {\n        // The next option is available\n        return nextFocus;\n      }\n\n      // The next option is disabled, move to the next element.\n      // with looped index\n      if (direction === 'next') {\n        nextFocus = (nextFocus + 1) % filteredOptions.length;\n      } else {\n        nextFocus = (nextFocus - 1 + filteredOptions.length) % filteredOptions.length;\n      }\n\n      // We end up with initial index, that means we don't have available options.\n      // All of them are disabled\n      if (nextFocus === index) {\n        return -1;\n      }\n    }\n  }\n  const setHighlightedIndex = useEventCallback(_ref2 => {\n    let {\n      event,\n      index,\n      reason = 'auto'\n    } = _ref2;\n    highlightedIndexRef.current = index;\n\n    // does the index exist?\n    if (index === -1) {\n      inputRef.current.removeAttribute('aria-activedescendant');\n    } else {\n      inputRef.current.setAttribute('aria-activedescendant', \"\".concat(id, \"-option-\").concat(index));\n    }\n    if (onHighlightChange) {\n      onHighlightChange(event, index === -1 ? null : filteredOptions[index], reason);\n    }\n    if (!listboxRef.current) {\n      return;\n    }\n    const prev = listboxRef.current.querySelector(\"[role=\\\"option\\\"].\".concat(unstable_classNamePrefix, \"-focused\"));\n    if (prev) {\n      prev.classList.remove(\"\".concat(unstable_classNamePrefix, \"-focused\"));\n      prev.classList.remove(\"\".concat(unstable_classNamePrefix, \"-focusVisible\"));\n    }\n    let listboxNode = listboxRef.current;\n    if (listboxRef.current.getAttribute('role') !== 'listbox') {\n      listboxNode = listboxRef.current.parentElement.querySelector('[role=\"listbox\"]');\n    }\n\n    // \"No results\"\n    if (!listboxNode) {\n      return;\n    }\n    if (index === -1) {\n      listboxNode.scrollTop = 0;\n      return;\n    }\n    const option = listboxRef.current.querySelector(\"[data-option-index=\\\"\".concat(index, \"\\\"]\"));\n    if (!option) {\n      return;\n    }\n    option.classList.add(\"\".concat(unstable_classNamePrefix, \"-focused\"));\n    if (reason === 'keyboard') {\n      option.classList.add(\"\".concat(unstable_classNamePrefix, \"-focusVisible\"));\n    }\n\n    // Scroll active descendant into view.\n    // Logic copied from https://www.w3.org/WAI/content-assets/wai-aria-practices/patterns/combobox/examples/js/select-only.js\n    // In case of mouse clicks and touch (in mobile devices) we avoid scrolling the element and keep both behaviors same.\n    // Consider this API instead once it has a better browser support:\n    // .scrollIntoView({ scrollMode: 'if-needed', block: 'nearest' });\n    if (listboxNode.scrollHeight > listboxNode.clientHeight && reason !== 'mouse' && reason !== 'touch') {\n      const element = option;\n      const scrollBottom = listboxNode.clientHeight + listboxNode.scrollTop;\n      const elementBottom = element.offsetTop + element.offsetHeight;\n      if (elementBottom > scrollBottom) {\n        listboxNode.scrollTop = elementBottom - listboxNode.clientHeight;\n      } else if (element.offsetTop - element.offsetHeight * (groupBy ? 1.3 : 0) < listboxNode.scrollTop) {\n        listboxNode.scrollTop = element.offsetTop - element.offsetHeight * (groupBy ? 1.3 : 0);\n      }\n    }\n  });\n  const changeHighlightedIndex = useEventCallback(_ref3 => {\n    let {\n      event,\n      diff,\n      direction = 'next',\n      reason = 'auto'\n    } = _ref3;\n    if (!popupOpen) {\n      return;\n    }\n    const getNextIndex = () => {\n      const maxIndex = filteredOptions.length - 1;\n      if (diff === 'reset') {\n        return defaultHighlighted;\n      }\n      if (diff === 'start') {\n        return 0;\n      }\n      if (diff === 'end') {\n        return maxIndex;\n      }\n      const newIndex = highlightedIndexRef.current + diff;\n      if (newIndex < 0) {\n        if (newIndex === -1 && includeInputInList) {\n          return -1;\n        }\n        if (disableListWrap && highlightedIndexRef.current !== -1 || Math.abs(diff) > 1) {\n          return 0;\n        }\n        return maxIndex;\n      }\n      if (newIndex > maxIndex) {\n        if (newIndex === maxIndex + 1 && includeInputInList) {\n          return -1;\n        }\n        if (disableListWrap || Math.abs(diff) > 1) {\n          return maxIndex;\n        }\n        return 0;\n      }\n      return newIndex;\n    };\n    const nextIndex = validOptionIndex(getNextIndex(), direction);\n    setHighlightedIndex({\n      index: nextIndex,\n      reason,\n      event\n    });\n\n    // Sync the content of the input with the highlighted option.\n    if (autoComplete && diff !== 'reset') {\n      if (nextIndex === -1) {\n        inputRef.current.value = inputValue;\n      } else {\n        const option = getOptionLabel(filteredOptions[nextIndex]);\n        inputRef.current.value = option;\n\n        // The portion of the selected suggestion that has not been typed by the user,\n        // a completion string, appears inline after the input cursor in the textbox.\n        const index = option.toLowerCase().indexOf(inputValue.toLowerCase());\n        if (index === 0 && inputValue.length > 0) {\n          inputRef.current.setSelectionRange(inputValue.length, option.length);\n        }\n      }\n    }\n  });\n  const getPreviousHighlightedOptionIndex = () => {\n    const isSameValue = (value1, value2) => {\n      const label1 = value1 ? getOptionLabel(value1) : '';\n      const label2 = value2 ? getOptionLabel(value2) : '';\n      return label1 === label2;\n    };\n    if (highlightedIndexRef.current !== -1 && previousProps.filteredOptions && previousProps.filteredOptions.length !== filteredOptions.length && previousProps.inputValue === inputValue && (multiple ? value.length === previousProps.value.length && previousProps.value.every((val, i) => getOptionLabel(value[i]) === getOptionLabel(val)) : isSameValue(previousProps.value, value))) {\n      const previousHighlightedOption = previousProps.filteredOptions[highlightedIndexRef.current];\n      if (previousHighlightedOption) {\n        return findIndex(filteredOptions, option => {\n          return getOptionLabel(option) === getOptionLabel(previousHighlightedOption);\n        });\n      }\n    }\n    return -1;\n  };\n  const syncHighlightedIndex = React.useCallback(() => {\n    if (!popupOpen) {\n      return;\n    }\n\n    // Check if the previously highlighted option still exists in the updated filtered options list and if the value and inputValue haven't changed\n    // If it exists and the value and the inputValue haven't changed, just update its index, otherwise continue execution\n    const previousHighlightedOptionIndex = getPreviousHighlightedOptionIndex();\n    if (previousHighlightedOptionIndex !== -1) {\n      highlightedIndexRef.current = previousHighlightedOptionIndex;\n      return;\n    }\n    const valueItem = multiple ? value[0] : value;\n\n    // The popup is empty, reset\n    if (filteredOptions.length === 0 || valueItem == null) {\n      changeHighlightedIndex({\n        diff: 'reset'\n      });\n      return;\n    }\n    if (!listboxRef.current) {\n      return;\n    }\n\n    // Synchronize the value with the highlighted index\n    if (valueItem != null) {\n      const currentOption = filteredOptions[highlightedIndexRef.current];\n\n      // Keep the current highlighted index if possible\n      if (multiple && currentOption && findIndex(value, val => isOptionEqualToValue(currentOption, val)) !== -1) {\n        return;\n      }\n      const itemIndex = findIndex(filteredOptions, optionItem => isOptionEqualToValue(optionItem, valueItem));\n      if (itemIndex === -1) {\n        changeHighlightedIndex({\n          diff: 'reset'\n        });\n      } else {\n        setHighlightedIndex({\n          index: itemIndex\n        });\n      }\n      return;\n    }\n\n    // Prevent the highlighted index to leak outside the boundaries.\n    if (highlightedIndexRef.current >= filteredOptions.length - 1) {\n      setHighlightedIndex({\n        index: filteredOptions.length - 1\n      });\n      return;\n    }\n\n    // Restore the focus to the previous index.\n    setHighlightedIndex({\n      index: highlightedIndexRef.current\n    });\n    // Ignore filteredOptions (and options, isOptionEqualToValue, getOptionLabel) not to break the scroll position\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [\n  // Only sync the highlighted index when the option switch between empty and not\n  filteredOptions.length,\n  // Don't sync the highlighted index with the value when multiple\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  multiple ? false : value, filterSelectedOptions, changeHighlightedIndex, setHighlightedIndex, popupOpen, inputValue, multiple]);\n  const handleListboxRef = useEventCallback(node => {\n    setRef(listboxRef, node);\n    if (!node) {\n      return;\n    }\n    syncHighlightedIndex();\n  });\n  if (process.env.NODE_ENV !== 'production') {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useEffect(() => {\n      if (!inputRef.current || inputRef.current.nodeName !== 'INPUT') {\n        if (inputRef.current && inputRef.current.nodeName === 'TEXTAREA') {\n          console.warn([\"A textarea element was provided to \".concat(componentName, \" where input was expected.\"), \"This is not a supported scenario but it may work under certain conditions.\", \"A textarea keyboard navigation may conflict with Autocomplete controls (for example enter and arrow keys).\", \"Make sure to test keyboard navigation and add custom event handlers if necessary.\"].join('\\n'));\n        } else {\n          console.error([\"MUI: Unable to find the input element. It was resolved to \".concat(inputRef.current, \" while an HTMLInputElement was expected.\"), \"Instead, \".concat(componentName, \" expects an input element.\"), '', componentName === 'useAutocomplete' ? 'Make sure you have bound getInputProps correctly and that the normal ref/effect resolutions order is guaranteed.' : 'Make sure you have customized the input component correctly.'].join('\\n'));\n        }\n      }\n    }, [componentName]);\n  }\n  React.useEffect(() => {\n    syncHighlightedIndex();\n  }, [syncHighlightedIndex]);\n  const handleOpen = event => {\n    if (open) {\n      return;\n    }\n    setOpenState(true);\n    setInputPristine(true);\n    if (onOpen) {\n      onOpen(event);\n    }\n  };\n  const handleClose = (event, reason) => {\n    if (!open) {\n      return;\n    }\n    setOpenState(false);\n    if (onClose) {\n      onClose(event, reason);\n    }\n  };\n  const handleValue = (event, newValue, reason, details) => {\n    if (multiple) {\n      if (value.length === newValue.length && value.every((val, i) => val === newValue[i])) {\n        return;\n      }\n    } else if (value === newValue) {\n      return;\n    }\n    if (onChange) {\n      onChange(event, newValue, reason, details);\n    }\n    setValueState(newValue);\n  };\n  const isTouch = React.useRef(false);\n  const selectNewValue = function (event, option) {\n    let reasonProp = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 'selectOption';\n    let origin = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 'options';\n    let reason = reasonProp;\n    let newValue = option;\n    if (multiple) {\n      newValue = Array.isArray(value) ? value.slice() : [];\n      if (process.env.NODE_ENV !== 'production') {\n        const matches = newValue.filter(val => isOptionEqualToValue(option, val));\n        if (matches.length > 1) {\n          console.error([\"MUI: The `isOptionEqualToValue` method of \".concat(componentName, \" does not handle the arguments correctly.\"), \"The component expects a single value to match a given option but found \".concat(matches.length, \" matches.\")].join('\\n'));\n        }\n      }\n      const itemIndex = findIndex(newValue, valueItem => isOptionEqualToValue(option, valueItem));\n      if (itemIndex === -1) {\n        newValue.push(option);\n      } else if (origin !== 'freeSolo') {\n        newValue.splice(itemIndex, 1);\n        reason = 'removeOption';\n      }\n    }\n    resetInputValue(event, newValue);\n    handleValue(event, newValue, reason, {\n      option\n    });\n    if (!disableCloseOnSelect && (!event || !event.ctrlKey && !event.metaKey)) {\n      handleClose(event, reason);\n    }\n    if (blurOnSelect === true || blurOnSelect === 'touch' && isTouch.current || blurOnSelect === 'mouse' && !isTouch.current) {\n      inputRef.current.blur();\n    }\n  };\n  function validTagIndex(index, direction) {\n    if (index === -1) {\n      return -1;\n    }\n    let nextFocus = index;\n    while (true) {\n      // Out of range\n      if (direction === 'next' && nextFocus === value.length || direction === 'previous' && nextFocus === -1) {\n        return -1;\n      }\n      const option = anchorEl.querySelector(\"[data-tag-index=\\\"\".concat(nextFocus, \"\\\"]\"));\n\n      // Same logic as MenuList.js\n      if (!option || !option.hasAttribute('tabindex') || option.disabled || option.getAttribute('aria-disabled') === 'true') {\n        nextFocus += direction === 'next' ? 1 : -1;\n      } else {\n        return nextFocus;\n      }\n    }\n  }\n  const handleFocusTag = (event, direction) => {\n    if (!multiple) {\n      return;\n    }\n    if (inputValue === '') {\n      handleClose(event, 'toggleInput');\n    }\n    let nextTag = focusedTag;\n    if (focusedTag === -1) {\n      if (inputValue === '' && direction === 'previous') {\n        nextTag = value.length - 1;\n      }\n    } else {\n      nextTag += direction === 'next' ? 1 : -1;\n      if (nextTag < 0) {\n        nextTag = 0;\n      }\n      if (nextTag === value.length) {\n        nextTag = -1;\n      }\n    }\n    nextTag = validTagIndex(nextTag, direction);\n    setFocusedTag(nextTag);\n    focusTag(nextTag);\n  };\n  const handleClear = event => {\n    ignoreFocus.current = true;\n    setInputValueState('');\n    if (onInputChange) {\n      onInputChange(event, '', 'clear');\n    }\n    handleValue(event, multiple ? [] : null, 'clear');\n  };\n  const handleKeyDown = other => event => {\n    if (other.onKeyDown) {\n      other.onKeyDown(event);\n    }\n    if (event.defaultMuiPrevented) {\n      return;\n    }\n    if (focusedTag !== -1 && ['ArrowLeft', 'ArrowRight'].indexOf(event.key) === -1) {\n      setFocusedTag(-1);\n      focusTag(-1);\n    }\n\n    // Wait until IME is settled.\n    if (event.which !== 229) {\n      switch (event.key) {\n        case 'Home':\n          if (popupOpen && handleHomeEndKeys) {\n            // Prevent scroll of the page\n            event.preventDefault();\n            changeHighlightedIndex({\n              diff: 'start',\n              direction: 'next',\n              reason: 'keyboard',\n              event\n            });\n          }\n          break;\n        case 'End':\n          if (popupOpen && handleHomeEndKeys) {\n            // Prevent scroll of the page\n            event.preventDefault();\n            changeHighlightedIndex({\n              diff: 'end',\n              direction: 'previous',\n              reason: 'keyboard',\n              event\n            });\n          }\n          break;\n        case 'PageUp':\n          // Prevent scroll of the page\n          event.preventDefault();\n          changeHighlightedIndex({\n            diff: -pageSize,\n            direction: 'previous',\n            reason: 'keyboard',\n            event\n          });\n          handleOpen(event);\n          break;\n        case 'PageDown':\n          // Prevent scroll of the page\n          event.preventDefault();\n          changeHighlightedIndex({\n            diff: pageSize,\n            direction: 'next',\n            reason: 'keyboard',\n            event\n          });\n          handleOpen(event);\n          break;\n        case 'ArrowDown':\n          // Prevent cursor move\n          event.preventDefault();\n          changeHighlightedIndex({\n            diff: 1,\n            direction: 'next',\n            reason: 'keyboard',\n            event\n          });\n          handleOpen(event);\n          break;\n        case 'ArrowUp':\n          // Prevent cursor move\n          event.preventDefault();\n          changeHighlightedIndex({\n            diff: -1,\n            direction: 'previous',\n            reason: 'keyboard',\n            event\n          });\n          handleOpen(event);\n          break;\n        case 'ArrowLeft':\n          handleFocusTag(event, 'previous');\n          break;\n        case 'ArrowRight':\n          handleFocusTag(event, 'next');\n          break;\n        case 'Enter':\n          if (highlightedIndexRef.current !== -1 && popupOpen) {\n            const option = filteredOptions[highlightedIndexRef.current];\n            const disabled = getOptionDisabled ? getOptionDisabled(option) : false;\n\n            // Avoid early form validation, let the end-users continue filling the form.\n            event.preventDefault();\n            if (disabled) {\n              return;\n            }\n            selectNewValue(event, option, 'selectOption');\n\n            // Move the selection to the end.\n            if (autoComplete) {\n              inputRef.current.setSelectionRange(inputRef.current.value.length, inputRef.current.value.length);\n            }\n          } else if (freeSolo && inputValue !== '' && inputValueIsSelectedValue === false) {\n            if (multiple) {\n              // Allow people to add new values before they submit the form.\n              event.preventDefault();\n            }\n            selectNewValue(event, inputValue, 'createOption', 'freeSolo');\n          }\n          break;\n        case 'Escape':\n          if (popupOpen) {\n            // Avoid Opera to exit fullscreen mode.\n            event.preventDefault();\n            // Avoid the Modal to handle the event.\n            event.stopPropagation();\n            handleClose(event, 'escape');\n          } else if (clearOnEscape && (inputValue !== '' || multiple && value.length > 0)) {\n            // Avoid Opera to exit fullscreen mode.\n            event.preventDefault();\n            // Avoid the Modal to handle the event.\n            event.stopPropagation();\n            handleClear(event);\n          }\n          break;\n        case 'Backspace':\n          // Remove the value on the left of the \"cursor\"\n          if (multiple && !readOnly && inputValue === '' && value.length > 0) {\n            const index = focusedTag === -1 ? value.length - 1 : focusedTag;\n            const newValue = value.slice();\n            newValue.splice(index, 1);\n            handleValue(event, newValue, 'removeOption', {\n              option: value[index]\n            });\n          }\n          break;\n        case 'Delete':\n          // Remove the value on the right of the \"cursor\"\n          if (multiple && !readOnly && inputValue === '' && value.length > 0 && focusedTag !== -1) {\n            const index = focusedTag;\n            const newValue = value.slice();\n            newValue.splice(index, 1);\n            handleValue(event, newValue, 'removeOption', {\n              option: value[index]\n            });\n          }\n          break;\n        default:\n      }\n    }\n  };\n  const handleFocus = event => {\n    setFocused(true);\n    if (openOnFocus && !ignoreFocus.current) {\n      handleOpen(event);\n    }\n  };\n  const handleBlur = event => {\n    // Ignore the event when using the scrollbar with IE11\n    if (unstable_isActiveElementInListbox(listboxRef)) {\n      inputRef.current.focus();\n      return;\n    }\n    setFocused(false);\n    firstFocus.current = true;\n    ignoreFocus.current = false;\n    if (autoSelect && highlightedIndexRef.current !== -1 && popupOpen) {\n      selectNewValue(event, filteredOptions[highlightedIndexRef.current], 'blur');\n    } else if (autoSelect && freeSolo && inputValue !== '') {\n      selectNewValue(event, inputValue, 'blur', 'freeSolo');\n    } else if (clearOnBlur) {\n      resetInputValue(event, value);\n    }\n    handleClose(event, 'blur');\n  };\n  const handleInputChange = event => {\n    const newValue = event.target.value;\n    if (inputValue !== newValue) {\n      setInputValueState(newValue);\n      setInputPristine(false);\n      if (onInputChange) {\n        onInputChange(event, newValue, 'input');\n      }\n    }\n    if (newValue === '') {\n      if (!disableClearable && !multiple) {\n        handleValue(event, null, 'clear');\n      }\n    } else {\n      handleOpen(event);\n    }\n  };\n  const handleOptionMouseMove = event => {\n    const index = Number(event.currentTarget.getAttribute('data-option-index'));\n    if (highlightedIndexRef.current !== index) {\n      setHighlightedIndex({\n        event,\n        index,\n        reason: 'mouse'\n      });\n    }\n  };\n  const handleOptionTouchStart = event => {\n    setHighlightedIndex({\n      event,\n      index: Number(event.currentTarget.getAttribute('data-option-index')),\n      reason: 'touch'\n    });\n    isTouch.current = true;\n  };\n  const handleOptionClick = event => {\n    const index = Number(event.currentTarget.getAttribute('data-option-index'));\n    selectNewValue(event, filteredOptions[index], 'selectOption');\n    isTouch.current = false;\n  };\n  const handleTagDelete = index => event => {\n    const newValue = value.slice();\n    newValue.splice(index, 1);\n    handleValue(event, newValue, 'removeOption', {\n      option: value[index]\n    });\n  };\n  const handlePopupIndicator = event => {\n    if (open) {\n      handleClose(event, 'toggleInput');\n    } else {\n      handleOpen(event);\n    }\n  };\n\n  // Prevent input blur when interacting with the combobox\n  const handleMouseDown = event => {\n    // Prevent focusing the input if click is anywhere outside the Autocomplete\n    if (!event.currentTarget.contains(event.target)) {\n      return;\n    }\n    if (event.target.getAttribute('id') !== id) {\n      event.preventDefault();\n    }\n  };\n\n  // Focus the input when interacting with the combobox\n  const handleClick = event => {\n    // Prevent focusing the input if click is anywhere outside the Autocomplete\n    if (!event.currentTarget.contains(event.target)) {\n      return;\n    }\n    inputRef.current.focus();\n    if (selectOnFocus && firstFocus.current && inputRef.current.selectionEnd - inputRef.current.selectionStart === 0) {\n      inputRef.current.select();\n    }\n    firstFocus.current = false;\n  };\n  const handleInputMouseDown = event => {\n    if (!disabledProp && (inputValue === '' || !open)) {\n      handlePopupIndicator(event);\n    }\n  };\n  let dirty = freeSolo && inputValue.length > 0;\n  dirty = dirty || (multiple ? value.length > 0 : value !== null);\n  let groupedOptions = filteredOptions;\n  if (groupBy) {\n    // used to keep track of key and indexes in the result array\n    const indexBy = new Map();\n    let warn = false;\n    groupedOptions = filteredOptions.reduce((acc, option, index) => {\n      const group = groupBy(option);\n      if (acc.length > 0 && acc[acc.length - 1].group === group) {\n        acc[acc.length - 1].options.push(option);\n      } else {\n        if (process.env.NODE_ENV !== 'production') {\n          if (indexBy.get(group) && !warn) {\n            console.warn(\"MUI: The options provided combined with the `groupBy` method of \".concat(componentName, \" returns duplicated headers.\"), 'You can solve the issue by sorting the options with the output of `groupBy`.');\n            warn = true;\n          }\n          indexBy.set(group, true);\n        }\n        acc.push({\n          key: index,\n          index,\n          group,\n          options: [option]\n        });\n      }\n      return acc;\n    }, []);\n  }\n  if (disabledProp && focused) {\n    handleBlur();\n  }\n  return {\n    getRootProps: function () {\n      let other = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n      return _extends({\n        'aria-owns': listboxAvailable ? \"\".concat(id, \"-listbox\") : null\n      }, other, {\n        onKeyDown: handleKeyDown(other),\n        onMouseDown: handleMouseDown,\n        onClick: handleClick\n      });\n    },\n    getInputLabelProps: () => ({\n      id: \"\".concat(id, \"-label\"),\n      htmlFor: id\n    }),\n    getInputProps: () => ({\n      id,\n      value: inputValue,\n      onBlur: handleBlur,\n      onFocus: handleFocus,\n      onChange: handleInputChange,\n      onMouseDown: handleInputMouseDown,\n      // if open then this is handled imperatively so don't let react override\n      // only have an opinion about this when closed\n      'aria-activedescendant': popupOpen ? '' : null,\n      'aria-autocomplete': autoComplete ? 'both' : 'list',\n      'aria-controls': listboxAvailable ? \"\".concat(id, \"-listbox\") : undefined,\n      'aria-expanded': listboxAvailable,\n      // Disable browser's suggestion that might overlap with the popup.\n      // Handle autocomplete but not autofill.\n      autoComplete: 'off',\n      ref: inputRef,\n      autoCapitalize: 'none',\n      spellCheck: 'false',\n      role: 'combobox',\n      disabled: disabledProp\n    }),\n    getClearProps: () => ({\n      tabIndex: -1,\n      type: 'button',\n      onClick: handleClear\n    }),\n    getPopupIndicatorProps: () => ({\n      tabIndex: -1,\n      type: 'button',\n      onClick: handlePopupIndicator\n    }),\n    getTagProps: _ref4 => {\n      let {\n        index\n      } = _ref4;\n      return _extends({\n        key: index,\n        'data-tag-index': index,\n        tabIndex: -1\n      }, !readOnly && {\n        onDelete: handleTagDelete(index)\n      });\n    },\n    getListboxProps: () => ({\n      role: 'listbox',\n      id: \"\".concat(id, \"-listbox\"),\n      'aria-labelledby': \"\".concat(id, \"-label\"),\n      ref: handleListboxRef,\n      onMouseDown: event => {\n        // Prevent blur\n        event.preventDefault();\n      }\n    }),\n    getOptionProps: _ref5 => {\n      let {\n        index,\n        option\n      } = _ref5;\n      var _getOptionKey;\n      const selected = (multiple ? value : [value]).some(value2 => value2 != null && isOptionEqualToValue(option, value2));\n      const disabled = getOptionDisabled ? getOptionDisabled(option) : false;\n      return {\n        key: (_getOptionKey = getOptionKey == null ? void 0 : getOptionKey(option)) != null ? _getOptionKey : getOptionLabel(option),\n        tabIndex: -1,\n        role: 'option',\n        id: \"\".concat(id, \"-option-\").concat(index),\n        onMouseMove: handleOptionMouseMove,\n        onClick: handleOptionClick,\n        onTouchStart: handleOptionTouchStart,\n        'data-option-index': index,\n        'aria-disabled': disabled,\n        'aria-selected': selected\n      };\n    },\n    id,\n    inputValue,\n    value,\n    dirty,\n    expanded: popupOpen && anchorEl,\n    popupOpen,\n    focused: focused || focusedTag !== -1,\n    anchorEl,\n    setAnchorEl,\n    focusedTag,\n    groupedOptions\n  };\n}", "map": {"version": 3, "names": ["_extends", "React", "unstable_setRef", "setRef", "unstable_useEventCallback", "useEventCallback", "unstable_useControlled", "useControlled", "unstable_useId", "useId", "usePreviousProps", "stripDiacritics", "string", "normalize", "replace", "createFilterOptions", "config", "arguments", "length", "undefined", "ignoreAccents", "ignoreCase", "limit", "matchFrom", "stringify", "trim", "options", "_ref", "inputValue", "getOptionLabel", "input", "toLowerCase", "filteredOptions", "filter", "option", "candidate", "indexOf", "slice", "findIndex", "array", "comp", "i", "defaultFilterOptions", "pageSize", "defaultIsActiveElementInListbox", "listboxRef", "_listboxRef$current$p", "current", "parentElement", "contains", "document", "activeElement", "MULTIPLE_DEFAULT_VALUE", "useAutocomplete", "props", "unstable_isActiveElementInListbox", "unstable_classNamePrefix", "autoComplete", "autoHighlight", "autoSelect", "blurOnSelect", "clearOnBlur", "freeSolo", "clearOnEscape", "componentName", "defaultValue", "multiple", "disableClearable", "disableCloseOnSelect", "disabled", "disabledProp", "disabledItemsFocusable", "disableListWrap", "filterOptions", "filterSelectedOptions", "getOptionDisabled", "getOption<PERSON>ey", "getOptionLabelProp", "_option$label", "label", "groupBy", "handleHomeEndKeys", "id", "idProp", "includeInputInList", "inputValueProp", "isOptionEqualToValue", "value", "onChange", "onClose", "onHighlightChange", "onInputChange", "onOpen", "open", "openProp", "openOnFocus", "readOnly", "selectOnFocus", "valueProp", "optionLabel", "process", "env", "NODE_ENV", "erroneousReturn", "concat", "console", "error", "JSON", "String", "ignoreFocus", "useRef", "firstFocus", "inputRef", "anchorEl", "setAnchorEl", "useState", "focusedTag", "setFocusedTag", "defaultHighlighted", "highlightedIndexRef", "setValueState", "controlled", "default", "name", "setInputValueState", "state", "focused", "setFocused", "resetInputValue", "useCallback", "event", "newValue", "isOptionSelected", "newInputValue", "setOpenState", "inputPristine", "setInputPristine", "inputValueIsSelectedValue", "popupOpen", "some", "value2", "previousProps", "useEffect", "valueChange", "listboxAvailable", "missing<PERSON><PERSON><PERSON>", "warn", "join", "focusTag", "tagToFocus", "focus", "querySelector", "validOptionIndex", "index", "direction", "nextFocus", "nextFocusDisabled", "getAttribute", "hasAttribute", "setHighlightedIndex", "_ref2", "reason", "removeAttribute", "setAttribute", "prev", "classList", "remove", "listboxNode", "scrollTop", "add", "scrollHeight", "clientHeight", "element", "scrollBottom", "elementBottom", "offsetTop", "offsetHeight", "changeHighlightedIndex", "_ref3", "diff", "getNextIndex", "maxIndex", "newIndex", "Math", "abs", "nextIndex", "setSelectionRange", "getPreviousHighlightedOptionIndex", "isSameValue", "value1", "label1", "label2", "every", "val", "previousHighlightedOption", "syncHighlightedIndex", "previousHighlightedOptionIndex", "valueItem", "currentOption", "itemIndex", "optionItem", "handleListboxRef", "node", "nodeName", "handleOpen", "handleClose", "handleValue", "details", "is<PERSON><PERSON>ch", "selectNewValue", "reasonProp", "origin", "Array", "isArray", "matches", "push", "splice", "ctrl<PERSON>ey", "metaKey", "blur", "validTagIndex", "handleFocusTag", "nextTag", "handleClear", "handleKeyDown", "other", "onKeyDown", "defaultMuiPrevented", "key", "which", "preventDefault", "stopPropagation", "handleFocus", "handleBlur", "handleInputChange", "target", "handleOptionMouseMove", "Number", "currentTarget", "handleOptionTouchStart", "handleOptionClick", "handleTagDelete", "handlePopupIndicator", "handleMouseDown", "handleClick", "selectionEnd", "selectionStart", "select", "handleInputMouseDown", "dirty", "groupedOptions", "indexBy", "Map", "reduce", "acc", "group", "get", "set", "getRootProps", "onMouseDown", "onClick", "getInputLabelProps", "htmlFor", "getInputProps", "onBlur", "onFocus", "ref", "autoCapitalize", "spell<PERSON>heck", "role", "getClearProps", "tabIndex", "type", "getPopupIndicatorProps", "getTagProps", "_ref4", "onDelete", "getListboxProps", "getOptionProps", "_ref5", "_getO<PERSON><PERSON>ey", "selected", "onMouseMove", "onTouchStart", "expanded"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/node_modules/@mui/base/useAutocomplete/useAutocomplete.js"], "sourcesContent": ["'use client';\n\n/* eslint-disable no-constant-condition */\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { unstable_setRef as setRef, unstable_useEventCallback as useEventCallback, unstable_useControlled as useControlled, unstable_useId as useId, usePreviousProps } from '@mui/utils';\n\n// https://stackoverflow.com/questions/990904/remove-accents-diacritics-in-a-string-in-javascript\n// Give up on IE11 support for this feature\nfunction stripDiacritics(string) {\n  return typeof string.normalize !== 'undefined' ? string.normalize('NFD').replace(/[\\u0300-\\u036f]/g, '') : string;\n}\nexport function createFilterOptions(config = {}) {\n  const {\n    ignoreAccents = true,\n    ignoreCase = true,\n    limit,\n    matchFrom = 'any',\n    stringify,\n    trim = false\n  } = config;\n  return (options, {\n    inputValue,\n    getOptionLabel\n  }) => {\n    let input = trim ? inputValue.trim() : inputValue;\n    if (ignoreCase) {\n      input = input.toLowerCase();\n    }\n    if (ignoreAccents) {\n      input = stripDiacritics(input);\n    }\n    const filteredOptions = !input ? options : options.filter(option => {\n      let candidate = (stringify || getOptionLabel)(option);\n      if (ignoreCase) {\n        candidate = candidate.toLowerCase();\n      }\n      if (ignoreAccents) {\n        candidate = stripDiacritics(candidate);\n      }\n      return matchFrom === 'start' ? candidate.indexOf(input) === 0 : candidate.indexOf(input) > -1;\n    });\n    return typeof limit === 'number' ? filteredOptions.slice(0, limit) : filteredOptions;\n  };\n}\n\n// To replace with .findIndex() once we stop IE11 support.\nfunction findIndex(array, comp) {\n  for (let i = 0; i < array.length; i += 1) {\n    if (comp(array[i])) {\n      return i;\n    }\n  }\n  return -1;\n}\nconst defaultFilterOptions = createFilterOptions();\n\n// Number of options to jump in list box when `Page Up` and `Page Down` keys are used.\nconst pageSize = 5;\nconst defaultIsActiveElementInListbox = listboxRef => {\n  var _listboxRef$current$p;\n  return listboxRef.current !== null && ((_listboxRef$current$p = listboxRef.current.parentElement) == null ? void 0 : _listboxRef$current$p.contains(document.activeElement));\n};\nconst MULTIPLE_DEFAULT_VALUE = [];\nexport function useAutocomplete(props) {\n  const {\n    // eslint-disable-next-line @typescript-eslint/naming-convention\n    unstable_isActiveElementInListbox = defaultIsActiveElementInListbox,\n    // eslint-disable-next-line @typescript-eslint/naming-convention\n    unstable_classNamePrefix = 'Mui',\n    autoComplete = false,\n    autoHighlight = false,\n    autoSelect = false,\n    blurOnSelect = false,\n    clearOnBlur = !props.freeSolo,\n    clearOnEscape = false,\n    componentName = 'useAutocomplete',\n    defaultValue = props.multiple ? MULTIPLE_DEFAULT_VALUE : null,\n    disableClearable = false,\n    disableCloseOnSelect = false,\n    disabled: disabledProp,\n    disabledItemsFocusable = false,\n    disableListWrap = false,\n    filterOptions = defaultFilterOptions,\n    filterSelectedOptions = false,\n    freeSolo = false,\n    getOptionDisabled,\n    getOptionKey,\n    getOptionLabel: getOptionLabelProp = option => {\n      var _option$label;\n      return (_option$label = option.label) != null ? _option$label : option;\n    },\n    groupBy,\n    handleHomeEndKeys = !props.freeSolo,\n    id: idProp,\n    includeInputInList = false,\n    inputValue: inputValueProp,\n    isOptionEqualToValue = (option, value) => option === value,\n    multiple = false,\n    onChange,\n    onClose,\n    onHighlightChange,\n    onInputChange,\n    onOpen,\n    open: openProp,\n    openOnFocus = false,\n    options,\n    readOnly = false,\n    selectOnFocus = !props.freeSolo,\n    value: valueProp\n  } = props;\n  const id = useId(idProp);\n  let getOptionLabel = getOptionLabelProp;\n  getOptionLabel = option => {\n    const optionLabel = getOptionLabelProp(option);\n    if (typeof optionLabel !== 'string') {\n      if (process.env.NODE_ENV !== 'production') {\n        const erroneousReturn = optionLabel === undefined ? 'undefined' : `${typeof optionLabel} (${optionLabel})`;\n        console.error(`MUI: The \\`getOptionLabel\\` method of ${componentName} returned ${erroneousReturn} instead of a string for ${JSON.stringify(option)}.`);\n      }\n      return String(optionLabel);\n    }\n    return optionLabel;\n  };\n  const ignoreFocus = React.useRef(false);\n  const firstFocus = React.useRef(true);\n  const inputRef = React.useRef(null);\n  const listboxRef = React.useRef(null);\n  const [anchorEl, setAnchorEl] = React.useState(null);\n  const [focusedTag, setFocusedTag] = React.useState(-1);\n  const defaultHighlighted = autoHighlight ? 0 : -1;\n  const highlightedIndexRef = React.useRef(defaultHighlighted);\n  const [value, setValueState] = useControlled({\n    controlled: valueProp,\n    default: defaultValue,\n    name: componentName\n  });\n  const [inputValue, setInputValueState] = useControlled({\n    controlled: inputValueProp,\n    default: '',\n    name: componentName,\n    state: 'inputValue'\n  });\n  const [focused, setFocused] = React.useState(false);\n  const resetInputValue = React.useCallback((event, newValue) => {\n    // retain current `inputValue` if new option isn't selected and `clearOnBlur` is false\n    // When `multiple` is enabled, `newValue` is an array of all selected items including the newly selected item\n    const isOptionSelected = multiple ? value.length < newValue.length : newValue !== null;\n    if (!isOptionSelected && !clearOnBlur) {\n      return;\n    }\n    let newInputValue;\n    if (multiple) {\n      newInputValue = '';\n    } else if (newValue == null) {\n      newInputValue = '';\n    } else {\n      const optionLabel = getOptionLabel(newValue);\n      newInputValue = typeof optionLabel === 'string' ? optionLabel : '';\n    }\n    if (inputValue === newInputValue) {\n      return;\n    }\n    setInputValueState(newInputValue);\n    if (onInputChange) {\n      onInputChange(event, newInputValue, 'reset');\n    }\n  }, [getOptionLabel, inputValue, multiple, onInputChange, setInputValueState, clearOnBlur, value]);\n  const [open, setOpenState] = useControlled({\n    controlled: openProp,\n    default: false,\n    name: componentName,\n    state: 'open'\n  });\n  const [inputPristine, setInputPristine] = React.useState(true);\n  const inputValueIsSelectedValue = !multiple && value != null && inputValue === getOptionLabel(value);\n  const popupOpen = open && !readOnly;\n  const filteredOptions = popupOpen ? filterOptions(options.filter(option => {\n    if (filterSelectedOptions && (multiple ? value : [value]).some(value2 => value2 !== null && isOptionEqualToValue(option, value2))) {\n      return false;\n    }\n    return true;\n  }),\n  // we use the empty string to manipulate `filterOptions` to not filter any options\n  // i.e. the filter predicate always returns true\n  {\n    inputValue: inputValueIsSelectedValue && inputPristine ? '' : inputValue,\n    getOptionLabel\n  }) : [];\n  const previousProps = usePreviousProps({\n    filteredOptions,\n    value,\n    inputValue\n  });\n  React.useEffect(() => {\n    const valueChange = value !== previousProps.value;\n    if (focused && !valueChange) {\n      return;\n    }\n\n    // Only reset the input's value when freeSolo if the component's value changes.\n    if (freeSolo && !valueChange) {\n      return;\n    }\n    resetInputValue(null, value);\n  }, [value, resetInputValue, focused, previousProps.value, freeSolo]);\n  const listboxAvailable = open && filteredOptions.length > 0 && !readOnly;\n  if (process.env.NODE_ENV !== 'production') {\n    if (value !== null && !freeSolo && options.length > 0) {\n      const missingValue = (multiple ? value : [value]).filter(value2 => !options.some(option => isOptionEqualToValue(option, value2)));\n      if (missingValue.length > 0) {\n        console.warn([`MUI: The value provided to ${componentName} is invalid.`, `None of the options match with \\`${missingValue.length > 1 ? JSON.stringify(missingValue) : JSON.stringify(missingValue[0])}\\`.`, 'You can use the `isOptionEqualToValue` prop to customize the equality test.'].join('\\n'));\n      }\n    }\n  }\n  const focusTag = useEventCallback(tagToFocus => {\n    if (tagToFocus === -1) {\n      inputRef.current.focus();\n    } else {\n      anchorEl.querySelector(`[data-tag-index=\"${tagToFocus}\"]`).focus();\n    }\n  });\n\n  // Ensure the focusedTag is never inconsistent\n  React.useEffect(() => {\n    if (multiple && focusedTag > value.length - 1) {\n      setFocusedTag(-1);\n      focusTag(-1);\n    }\n  }, [value, multiple, focusedTag, focusTag]);\n  function validOptionIndex(index, direction) {\n    if (!listboxRef.current || index < 0 || index >= filteredOptions.length) {\n      return -1;\n    }\n    let nextFocus = index;\n    while (true) {\n      const option = listboxRef.current.querySelector(`[data-option-index=\"${nextFocus}\"]`);\n\n      // Same logic as MenuList.js\n      const nextFocusDisabled = disabledItemsFocusable ? false : !option || option.disabled || option.getAttribute('aria-disabled') === 'true';\n      if (option && option.hasAttribute('tabindex') && !nextFocusDisabled) {\n        // The next option is available\n        return nextFocus;\n      }\n\n      // The next option is disabled, move to the next element.\n      // with looped index\n      if (direction === 'next') {\n        nextFocus = (nextFocus + 1) % filteredOptions.length;\n      } else {\n        nextFocus = (nextFocus - 1 + filteredOptions.length) % filteredOptions.length;\n      }\n\n      // We end up with initial index, that means we don't have available options.\n      // All of them are disabled\n      if (nextFocus === index) {\n        return -1;\n      }\n    }\n  }\n  const setHighlightedIndex = useEventCallback(({\n    event,\n    index,\n    reason = 'auto'\n  }) => {\n    highlightedIndexRef.current = index;\n\n    // does the index exist?\n    if (index === -1) {\n      inputRef.current.removeAttribute('aria-activedescendant');\n    } else {\n      inputRef.current.setAttribute('aria-activedescendant', `${id}-option-${index}`);\n    }\n    if (onHighlightChange) {\n      onHighlightChange(event, index === -1 ? null : filteredOptions[index], reason);\n    }\n    if (!listboxRef.current) {\n      return;\n    }\n    const prev = listboxRef.current.querySelector(`[role=\"option\"].${unstable_classNamePrefix}-focused`);\n    if (prev) {\n      prev.classList.remove(`${unstable_classNamePrefix}-focused`);\n      prev.classList.remove(`${unstable_classNamePrefix}-focusVisible`);\n    }\n    let listboxNode = listboxRef.current;\n    if (listboxRef.current.getAttribute('role') !== 'listbox') {\n      listboxNode = listboxRef.current.parentElement.querySelector('[role=\"listbox\"]');\n    }\n\n    // \"No results\"\n    if (!listboxNode) {\n      return;\n    }\n    if (index === -1) {\n      listboxNode.scrollTop = 0;\n      return;\n    }\n    const option = listboxRef.current.querySelector(`[data-option-index=\"${index}\"]`);\n    if (!option) {\n      return;\n    }\n    option.classList.add(`${unstable_classNamePrefix}-focused`);\n    if (reason === 'keyboard') {\n      option.classList.add(`${unstable_classNamePrefix}-focusVisible`);\n    }\n\n    // Scroll active descendant into view.\n    // Logic copied from https://www.w3.org/WAI/content-assets/wai-aria-practices/patterns/combobox/examples/js/select-only.js\n    // In case of mouse clicks and touch (in mobile devices) we avoid scrolling the element and keep both behaviors same.\n    // Consider this API instead once it has a better browser support:\n    // .scrollIntoView({ scrollMode: 'if-needed', block: 'nearest' });\n    if (listboxNode.scrollHeight > listboxNode.clientHeight && reason !== 'mouse' && reason !== 'touch') {\n      const element = option;\n      const scrollBottom = listboxNode.clientHeight + listboxNode.scrollTop;\n      const elementBottom = element.offsetTop + element.offsetHeight;\n      if (elementBottom > scrollBottom) {\n        listboxNode.scrollTop = elementBottom - listboxNode.clientHeight;\n      } else if (element.offsetTop - element.offsetHeight * (groupBy ? 1.3 : 0) < listboxNode.scrollTop) {\n        listboxNode.scrollTop = element.offsetTop - element.offsetHeight * (groupBy ? 1.3 : 0);\n      }\n    }\n  });\n  const changeHighlightedIndex = useEventCallback(({\n    event,\n    diff,\n    direction = 'next',\n    reason = 'auto'\n  }) => {\n    if (!popupOpen) {\n      return;\n    }\n    const getNextIndex = () => {\n      const maxIndex = filteredOptions.length - 1;\n      if (diff === 'reset') {\n        return defaultHighlighted;\n      }\n      if (diff === 'start') {\n        return 0;\n      }\n      if (diff === 'end') {\n        return maxIndex;\n      }\n      const newIndex = highlightedIndexRef.current + diff;\n      if (newIndex < 0) {\n        if (newIndex === -1 && includeInputInList) {\n          return -1;\n        }\n        if (disableListWrap && highlightedIndexRef.current !== -1 || Math.abs(diff) > 1) {\n          return 0;\n        }\n        return maxIndex;\n      }\n      if (newIndex > maxIndex) {\n        if (newIndex === maxIndex + 1 && includeInputInList) {\n          return -1;\n        }\n        if (disableListWrap || Math.abs(diff) > 1) {\n          return maxIndex;\n        }\n        return 0;\n      }\n      return newIndex;\n    };\n    const nextIndex = validOptionIndex(getNextIndex(), direction);\n    setHighlightedIndex({\n      index: nextIndex,\n      reason,\n      event\n    });\n\n    // Sync the content of the input with the highlighted option.\n    if (autoComplete && diff !== 'reset') {\n      if (nextIndex === -1) {\n        inputRef.current.value = inputValue;\n      } else {\n        const option = getOptionLabel(filteredOptions[nextIndex]);\n        inputRef.current.value = option;\n\n        // The portion of the selected suggestion that has not been typed by the user,\n        // a completion string, appears inline after the input cursor in the textbox.\n        const index = option.toLowerCase().indexOf(inputValue.toLowerCase());\n        if (index === 0 && inputValue.length > 0) {\n          inputRef.current.setSelectionRange(inputValue.length, option.length);\n        }\n      }\n    }\n  });\n  const getPreviousHighlightedOptionIndex = () => {\n    const isSameValue = (value1, value2) => {\n      const label1 = value1 ? getOptionLabel(value1) : '';\n      const label2 = value2 ? getOptionLabel(value2) : '';\n      return label1 === label2;\n    };\n    if (highlightedIndexRef.current !== -1 && previousProps.filteredOptions && previousProps.filteredOptions.length !== filteredOptions.length && previousProps.inputValue === inputValue && (multiple ? value.length === previousProps.value.length && previousProps.value.every((val, i) => getOptionLabel(value[i]) === getOptionLabel(val)) : isSameValue(previousProps.value, value))) {\n      const previousHighlightedOption = previousProps.filteredOptions[highlightedIndexRef.current];\n      if (previousHighlightedOption) {\n        return findIndex(filteredOptions, option => {\n          return getOptionLabel(option) === getOptionLabel(previousHighlightedOption);\n        });\n      }\n    }\n    return -1;\n  };\n  const syncHighlightedIndex = React.useCallback(() => {\n    if (!popupOpen) {\n      return;\n    }\n\n    // Check if the previously highlighted option still exists in the updated filtered options list and if the value and inputValue haven't changed\n    // If it exists and the value and the inputValue haven't changed, just update its index, otherwise continue execution\n    const previousHighlightedOptionIndex = getPreviousHighlightedOptionIndex();\n    if (previousHighlightedOptionIndex !== -1) {\n      highlightedIndexRef.current = previousHighlightedOptionIndex;\n      return;\n    }\n    const valueItem = multiple ? value[0] : value;\n\n    // The popup is empty, reset\n    if (filteredOptions.length === 0 || valueItem == null) {\n      changeHighlightedIndex({\n        diff: 'reset'\n      });\n      return;\n    }\n    if (!listboxRef.current) {\n      return;\n    }\n\n    // Synchronize the value with the highlighted index\n    if (valueItem != null) {\n      const currentOption = filteredOptions[highlightedIndexRef.current];\n\n      // Keep the current highlighted index if possible\n      if (multiple && currentOption && findIndex(value, val => isOptionEqualToValue(currentOption, val)) !== -1) {\n        return;\n      }\n      const itemIndex = findIndex(filteredOptions, optionItem => isOptionEqualToValue(optionItem, valueItem));\n      if (itemIndex === -1) {\n        changeHighlightedIndex({\n          diff: 'reset'\n        });\n      } else {\n        setHighlightedIndex({\n          index: itemIndex\n        });\n      }\n      return;\n    }\n\n    // Prevent the highlighted index to leak outside the boundaries.\n    if (highlightedIndexRef.current >= filteredOptions.length - 1) {\n      setHighlightedIndex({\n        index: filteredOptions.length - 1\n      });\n      return;\n    }\n\n    // Restore the focus to the previous index.\n    setHighlightedIndex({\n      index: highlightedIndexRef.current\n    });\n    // Ignore filteredOptions (and options, isOptionEqualToValue, getOptionLabel) not to break the scroll position\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [\n  // Only sync the highlighted index when the option switch between empty and not\n  filteredOptions.length,\n  // Don't sync the highlighted index with the value when multiple\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  multiple ? false : value, filterSelectedOptions, changeHighlightedIndex, setHighlightedIndex, popupOpen, inputValue, multiple]);\n  const handleListboxRef = useEventCallback(node => {\n    setRef(listboxRef, node);\n    if (!node) {\n      return;\n    }\n    syncHighlightedIndex();\n  });\n  if (process.env.NODE_ENV !== 'production') {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useEffect(() => {\n      if (!inputRef.current || inputRef.current.nodeName !== 'INPUT') {\n        if (inputRef.current && inputRef.current.nodeName === 'TEXTAREA') {\n          console.warn([`A textarea element was provided to ${componentName} where input was expected.`, `This is not a supported scenario but it may work under certain conditions.`, `A textarea keyboard navigation may conflict with Autocomplete controls (for example enter and arrow keys).`, `Make sure to test keyboard navigation and add custom event handlers if necessary.`].join('\\n'));\n        } else {\n          console.error([`MUI: Unable to find the input element. It was resolved to ${inputRef.current} while an HTMLInputElement was expected.`, `Instead, ${componentName} expects an input element.`, '', componentName === 'useAutocomplete' ? 'Make sure you have bound getInputProps correctly and that the normal ref/effect resolutions order is guaranteed.' : 'Make sure you have customized the input component correctly.'].join('\\n'));\n        }\n      }\n    }, [componentName]);\n  }\n  React.useEffect(() => {\n    syncHighlightedIndex();\n  }, [syncHighlightedIndex]);\n  const handleOpen = event => {\n    if (open) {\n      return;\n    }\n    setOpenState(true);\n    setInputPristine(true);\n    if (onOpen) {\n      onOpen(event);\n    }\n  };\n  const handleClose = (event, reason) => {\n    if (!open) {\n      return;\n    }\n    setOpenState(false);\n    if (onClose) {\n      onClose(event, reason);\n    }\n  };\n  const handleValue = (event, newValue, reason, details) => {\n    if (multiple) {\n      if (value.length === newValue.length && value.every((val, i) => val === newValue[i])) {\n        return;\n      }\n    } else if (value === newValue) {\n      return;\n    }\n    if (onChange) {\n      onChange(event, newValue, reason, details);\n    }\n    setValueState(newValue);\n  };\n  const isTouch = React.useRef(false);\n  const selectNewValue = (event, option, reasonProp = 'selectOption', origin = 'options') => {\n    let reason = reasonProp;\n    let newValue = option;\n    if (multiple) {\n      newValue = Array.isArray(value) ? value.slice() : [];\n      if (process.env.NODE_ENV !== 'production') {\n        const matches = newValue.filter(val => isOptionEqualToValue(option, val));\n        if (matches.length > 1) {\n          console.error([`MUI: The \\`isOptionEqualToValue\\` method of ${componentName} does not handle the arguments correctly.`, `The component expects a single value to match a given option but found ${matches.length} matches.`].join('\\n'));\n        }\n      }\n      const itemIndex = findIndex(newValue, valueItem => isOptionEqualToValue(option, valueItem));\n      if (itemIndex === -1) {\n        newValue.push(option);\n      } else if (origin !== 'freeSolo') {\n        newValue.splice(itemIndex, 1);\n        reason = 'removeOption';\n      }\n    }\n    resetInputValue(event, newValue);\n    handleValue(event, newValue, reason, {\n      option\n    });\n    if (!disableCloseOnSelect && (!event || !event.ctrlKey && !event.metaKey)) {\n      handleClose(event, reason);\n    }\n    if (blurOnSelect === true || blurOnSelect === 'touch' && isTouch.current || blurOnSelect === 'mouse' && !isTouch.current) {\n      inputRef.current.blur();\n    }\n  };\n  function validTagIndex(index, direction) {\n    if (index === -1) {\n      return -1;\n    }\n    let nextFocus = index;\n    while (true) {\n      // Out of range\n      if (direction === 'next' && nextFocus === value.length || direction === 'previous' && nextFocus === -1) {\n        return -1;\n      }\n      const option = anchorEl.querySelector(`[data-tag-index=\"${nextFocus}\"]`);\n\n      // Same logic as MenuList.js\n      if (!option || !option.hasAttribute('tabindex') || option.disabled || option.getAttribute('aria-disabled') === 'true') {\n        nextFocus += direction === 'next' ? 1 : -1;\n      } else {\n        return nextFocus;\n      }\n    }\n  }\n  const handleFocusTag = (event, direction) => {\n    if (!multiple) {\n      return;\n    }\n    if (inputValue === '') {\n      handleClose(event, 'toggleInput');\n    }\n    let nextTag = focusedTag;\n    if (focusedTag === -1) {\n      if (inputValue === '' && direction === 'previous') {\n        nextTag = value.length - 1;\n      }\n    } else {\n      nextTag += direction === 'next' ? 1 : -1;\n      if (nextTag < 0) {\n        nextTag = 0;\n      }\n      if (nextTag === value.length) {\n        nextTag = -1;\n      }\n    }\n    nextTag = validTagIndex(nextTag, direction);\n    setFocusedTag(nextTag);\n    focusTag(nextTag);\n  };\n  const handleClear = event => {\n    ignoreFocus.current = true;\n    setInputValueState('');\n    if (onInputChange) {\n      onInputChange(event, '', 'clear');\n    }\n    handleValue(event, multiple ? [] : null, 'clear');\n  };\n  const handleKeyDown = other => event => {\n    if (other.onKeyDown) {\n      other.onKeyDown(event);\n    }\n    if (event.defaultMuiPrevented) {\n      return;\n    }\n    if (focusedTag !== -1 && ['ArrowLeft', 'ArrowRight'].indexOf(event.key) === -1) {\n      setFocusedTag(-1);\n      focusTag(-1);\n    }\n\n    // Wait until IME is settled.\n    if (event.which !== 229) {\n      switch (event.key) {\n        case 'Home':\n          if (popupOpen && handleHomeEndKeys) {\n            // Prevent scroll of the page\n            event.preventDefault();\n            changeHighlightedIndex({\n              diff: 'start',\n              direction: 'next',\n              reason: 'keyboard',\n              event\n            });\n          }\n          break;\n        case 'End':\n          if (popupOpen && handleHomeEndKeys) {\n            // Prevent scroll of the page\n            event.preventDefault();\n            changeHighlightedIndex({\n              diff: 'end',\n              direction: 'previous',\n              reason: 'keyboard',\n              event\n            });\n          }\n          break;\n        case 'PageUp':\n          // Prevent scroll of the page\n          event.preventDefault();\n          changeHighlightedIndex({\n            diff: -pageSize,\n            direction: 'previous',\n            reason: 'keyboard',\n            event\n          });\n          handleOpen(event);\n          break;\n        case 'PageDown':\n          // Prevent scroll of the page\n          event.preventDefault();\n          changeHighlightedIndex({\n            diff: pageSize,\n            direction: 'next',\n            reason: 'keyboard',\n            event\n          });\n          handleOpen(event);\n          break;\n        case 'ArrowDown':\n          // Prevent cursor move\n          event.preventDefault();\n          changeHighlightedIndex({\n            diff: 1,\n            direction: 'next',\n            reason: 'keyboard',\n            event\n          });\n          handleOpen(event);\n          break;\n        case 'ArrowUp':\n          // Prevent cursor move\n          event.preventDefault();\n          changeHighlightedIndex({\n            diff: -1,\n            direction: 'previous',\n            reason: 'keyboard',\n            event\n          });\n          handleOpen(event);\n          break;\n        case 'ArrowLeft':\n          handleFocusTag(event, 'previous');\n          break;\n        case 'ArrowRight':\n          handleFocusTag(event, 'next');\n          break;\n        case 'Enter':\n          if (highlightedIndexRef.current !== -1 && popupOpen) {\n            const option = filteredOptions[highlightedIndexRef.current];\n            const disabled = getOptionDisabled ? getOptionDisabled(option) : false;\n\n            // Avoid early form validation, let the end-users continue filling the form.\n            event.preventDefault();\n            if (disabled) {\n              return;\n            }\n            selectNewValue(event, option, 'selectOption');\n\n            // Move the selection to the end.\n            if (autoComplete) {\n              inputRef.current.setSelectionRange(inputRef.current.value.length, inputRef.current.value.length);\n            }\n          } else if (freeSolo && inputValue !== '' && inputValueIsSelectedValue === false) {\n            if (multiple) {\n              // Allow people to add new values before they submit the form.\n              event.preventDefault();\n            }\n            selectNewValue(event, inputValue, 'createOption', 'freeSolo');\n          }\n          break;\n        case 'Escape':\n          if (popupOpen) {\n            // Avoid Opera to exit fullscreen mode.\n            event.preventDefault();\n            // Avoid the Modal to handle the event.\n            event.stopPropagation();\n            handleClose(event, 'escape');\n          } else if (clearOnEscape && (inputValue !== '' || multiple && value.length > 0)) {\n            // Avoid Opera to exit fullscreen mode.\n            event.preventDefault();\n            // Avoid the Modal to handle the event.\n            event.stopPropagation();\n            handleClear(event);\n          }\n          break;\n        case 'Backspace':\n          // Remove the value on the left of the \"cursor\"\n          if (multiple && !readOnly && inputValue === '' && value.length > 0) {\n            const index = focusedTag === -1 ? value.length - 1 : focusedTag;\n            const newValue = value.slice();\n            newValue.splice(index, 1);\n            handleValue(event, newValue, 'removeOption', {\n              option: value[index]\n            });\n          }\n          break;\n        case 'Delete':\n          // Remove the value on the right of the \"cursor\"\n          if (multiple && !readOnly && inputValue === '' && value.length > 0 && focusedTag !== -1) {\n            const index = focusedTag;\n            const newValue = value.slice();\n            newValue.splice(index, 1);\n            handleValue(event, newValue, 'removeOption', {\n              option: value[index]\n            });\n          }\n          break;\n        default:\n      }\n    }\n  };\n  const handleFocus = event => {\n    setFocused(true);\n    if (openOnFocus && !ignoreFocus.current) {\n      handleOpen(event);\n    }\n  };\n  const handleBlur = event => {\n    // Ignore the event when using the scrollbar with IE11\n    if (unstable_isActiveElementInListbox(listboxRef)) {\n      inputRef.current.focus();\n      return;\n    }\n    setFocused(false);\n    firstFocus.current = true;\n    ignoreFocus.current = false;\n    if (autoSelect && highlightedIndexRef.current !== -1 && popupOpen) {\n      selectNewValue(event, filteredOptions[highlightedIndexRef.current], 'blur');\n    } else if (autoSelect && freeSolo && inputValue !== '') {\n      selectNewValue(event, inputValue, 'blur', 'freeSolo');\n    } else if (clearOnBlur) {\n      resetInputValue(event, value);\n    }\n    handleClose(event, 'blur');\n  };\n  const handleInputChange = event => {\n    const newValue = event.target.value;\n    if (inputValue !== newValue) {\n      setInputValueState(newValue);\n      setInputPristine(false);\n      if (onInputChange) {\n        onInputChange(event, newValue, 'input');\n      }\n    }\n    if (newValue === '') {\n      if (!disableClearable && !multiple) {\n        handleValue(event, null, 'clear');\n      }\n    } else {\n      handleOpen(event);\n    }\n  };\n  const handleOptionMouseMove = event => {\n    const index = Number(event.currentTarget.getAttribute('data-option-index'));\n    if (highlightedIndexRef.current !== index) {\n      setHighlightedIndex({\n        event,\n        index,\n        reason: 'mouse'\n      });\n    }\n  };\n  const handleOptionTouchStart = event => {\n    setHighlightedIndex({\n      event,\n      index: Number(event.currentTarget.getAttribute('data-option-index')),\n      reason: 'touch'\n    });\n    isTouch.current = true;\n  };\n  const handleOptionClick = event => {\n    const index = Number(event.currentTarget.getAttribute('data-option-index'));\n    selectNewValue(event, filteredOptions[index], 'selectOption');\n    isTouch.current = false;\n  };\n  const handleTagDelete = index => event => {\n    const newValue = value.slice();\n    newValue.splice(index, 1);\n    handleValue(event, newValue, 'removeOption', {\n      option: value[index]\n    });\n  };\n  const handlePopupIndicator = event => {\n    if (open) {\n      handleClose(event, 'toggleInput');\n    } else {\n      handleOpen(event);\n    }\n  };\n\n  // Prevent input blur when interacting with the combobox\n  const handleMouseDown = event => {\n    // Prevent focusing the input if click is anywhere outside the Autocomplete\n    if (!event.currentTarget.contains(event.target)) {\n      return;\n    }\n    if (event.target.getAttribute('id') !== id) {\n      event.preventDefault();\n    }\n  };\n\n  // Focus the input when interacting with the combobox\n  const handleClick = event => {\n    // Prevent focusing the input if click is anywhere outside the Autocomplete\n    if (!event.currentTarget.contains(event.target)) {\n      return;\n    }\n    inputRef.current.focus();\n    if (selectOnFocus && firstFocus.current && inputRef.current.selectionEnd - inputRef.current.selectionStart === 0) {\n      inputRef.current.select();\n    }\n    firstFocus.current = false;\n  };\n  const handleInputMouseDown = event => {\n    if (!disabledProp && (inputValue === '' || !open)) {\n      handlePopupIndicator(event);\n    }\n  };\n  let dirty = freeSolo && inputValue.length > 0;\n  dirty = dirty || (multiple ? value.length > 0 : value !== null);\n  let groupedOptions = filteredOptions;\n  if (groupBy) {\n    // used to keep track of key and indexes in the result array\n    const indexBy = new Map();\n    let warn = false;\n    groupedOptions = filteredOptions.reduce((acc, option, index) => {\n      const group = groupBy(option);\n      if (acc.length > 0 && acc[acc.length - 1].group === group) {\n        acc[acc.length - 1].options.push(option);\n      } else {\n        if (process.env.NODE_ENV !== 'production') {\n          if (indexBy.get(group) && !warn) {\n            console.warn(`MUI: The options provided combined with the \\`groupBy\\` method of ${componentName} returns duplicated headers.`, 'You can solve the issue by sorting the options with the output of `groupBy`.');\n            warn = true;\n          }\n          indexBy.set(group, true);\n        }\n        acc.push({\n          key: index,\n          index,\n          group,\n          options: [option]\n        });\n      }\n      return acc;\n    }, []);\n  }\n  if (disabledProp && focused) {\n    handleBlur();\n  }\n  return {\n    getRootProps: (other = {}) => _extends({\n      'aria-owns': listboxAvailable ? `${id}-listbox` : null\n    }, other, {\n      onKeyDown: handleKeyDown(other),\n      onMouseDown: handleMouseDown,\n      onClick: handleClick\n    }),\n    getInputLabelProps: () => ({\n      id: `${id}-label`,\n      htmlFor: id\n    }),\n    getInputProps: () => ({\n      id,\n      value: inputValue,\n      onBlur: handleBlur,\n      onFocus: handleFocus,\n      onChange: handleInputChange,\n      onMouseDown: handleInputMouseDown,\n      // if open then this is handled imperatively so don't let react override\n      // only have an opinion about this when closed\n      'aria-activedescendant': popupOpen ? '' : null,\n      'aria-autocomplete': autoComplete ? 'both' : 'list',\n      'aria-controls': listboxAvailable ? `${id}-listbox` : undefined,\n      'aria-expanded': listboxAvailable,\n      // Disable browser's suggestion that might overlap with the popup.\n      // Handle autocomplete but not autofill.\n      autoComplete: 'off',\n      ref: inputRef,\n      autoCapitalize: 'none',\n      spellCheck: 'false',\n      role: 'combobox',\n      disabled: disabledProp\n    }),\n    getClearProps: () => ({\n      tabIndex: -1,\n      type: 'button',\n      onClick: handleClear\n    }),\n    getPopupIndicatorProps: () => ({\n      tabIndex: -1,\n      type: 'button',\n      onClick: handlePopupIndicator\n    }),\n    getTagProps: ({\n      index\n    }) => _extends({\n      key: index,\n      'data-tag-index': index,\n      tabIndex: -1\n    }, !readOnly && {\n      onDelete: handleTagDelete(index)\n    }),\n    getListboxProps: () => ({\n      role: 'listbox',\n      id: `${id}-listbox`,\n      'aria-labelledby': `${id}-label`,\n      ref: handleListboxRef,\n      onMouseDown: event => {\n        // Prevent blur\n        event.preventDefault();\n      }\n    }),\n    getOptionProps: ({\n      index,\n      option\n    }) => {\n      var _getOptionKey;\n      const selected = (multiple ? value : [value]).some(value2 => value2 != null && isOptionEqualToValue(option, value2));\n      const disabled = getOptionDisabled ? getOptionDisabled(option) : false;\n      return {\n        key: (_getOptionKey = getOptionKey == null ? void 0 : getOptionKey(option)) != null ? _getOptionKey : getOptionLabel(option),\n        tabIndex: -1,\n        role: 'option',\n        id: `${id}-option-${index}`,\n        onMouseMove: handleOptionMouseMove,\n        onClick: handleOptionClick,\n        onTouchStart: handleOptionTouchStart,\n        'data-option-index': index,\n        'aria-disabled': disabled,\n        'aria-selected': selected\n      };\n    },\n    id,\n    inputValue,\n    value,\n    dirty,\n    expanded: popupOpen && anchorEl,\n    popupOpen,\n    focused: focused || focusedTag !== -1,\n    anchorEl,\n    setAnchorEl,\n    focusedTag,\n    groupedOptions\n  };\n}"], "mappings": "AAAA,YAAY;;AAEZ;AACA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,eAAe,IAAIC,MAAM,EAAEC,yBAAyB,IAAIC,gBAAgB,EAAEC,sBAAsB,IAAIC,aAAa,EAAEC,cAAc,IAAIC,KAAK,EAAEC,gBAAgB,QAAQ,YAAY;;AAEzL;AACA;AACA,SAASC,eAAeA,CAACC,MAAM,EAAE;EAC/B,OAAO,OAAOA,MAAM,CAACC,SAAS,KAAK,WAAW,GAAGD,MAAM,CAACC,SAAS,CAAC,KAAK,CAAC,CAACC,OAAO,CAAC,kBAAkB,EAAE,EAAE,CAAC,GAAGF,MAAM;AACnH;AACA,OAAO,SAASG,mBAAmBA,CAAA,EAAc;EAAA,IAAbC,MAAM,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAC7C,MAAM;IACJG,aAAa,GAAG,IAAI;IACpBC,UAAU,GAAG,IAAI;IACjBC,KAAK;IACLC,SAAS,GAAG,KAAK;IACjBC,SAAS;IACTC,IAAI,GAAG;EACT,CAAC,GAAGT,MAAM;EACV,OAAO,CAACU,OAAO,EAAAC,IAAA,KAGT;IAAA,IAHW;MACfC,UAAU;MACVC;IACF,CAAC,GAAAF,IAAA;IACC,IAAIG,KAAK,GAAGL,IAAI,GAAGG,UAAU,CAACH,IAAI,CAAC,CAAC,GAAGG,UAAU;IACjD,IAAIP,UAAU,EAAE;MACdS,KAAK,GAAGA,KAAK,CAACC,WAAW,CAAC,CAAC;IAC7B;IACA,IAAIX,aAAa,EAAE;MACjBU,KAAK,GAAGnB,eAAe,CAACmB,KAAK,CAAC;IAChC;IACA,MAAME,eAAe,GAAG,CAACF,KAAK,GAAGJ,OAAO,GAAGA,OAAO,CAACO,MAAM,CAACC,MAAM,IAAI;MAClE,IAAIC,SAAS,GAAG,CAACX,SAAS,IAAIK,cAAc,EAAEK,MAAM,CAAC;MACrD,IAAIb,UAAU,EAAE;QACdc,SAAS,GAAGA,SAAS,CAACJ,WAAW,CAAC,CAAC;MACrC;MACA,IAAIX,aAAa,EAAE;QACjBe,SAAS,GAAGxB,eAAe,CAACwB,SAAS,CAAC;MACxC;MACA,OAAOZ,SAAS,KAAK,OAAO,GAAGY,SAAS,CAACC,OAAO,CAACN,KAAK,CAAC,KAAK,CAAC,GAAGK,SAAS,CAACC,OAAO,CAACN,KAAK,CAAC,GAAG,CAAC,CAAC;IAC/F,CAAC,CAAC;IACF,OAAO,OAAOR,KAAK,KAAK,QAAQ,GAAGU,eAAe,CAACK,KAAK,CAAC,CAAC,EAAEf,KAAK,CAAC,GAAGU,eAAe;EACtF,CAAC;AACH;;AAEA;AACA,SAASM,SAASA,CAACC,KAAK,EAAEC,IAAI,EAAE;EAC9B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,KAAK,CAACrB,MAAM,EAAEuB,CAAC,IAAI,CAAC,EAAE;IACxC,IAAID,IAAI,CAACD,KAAK,CAACE,CAAC,CAAC,CAAC,EAAE;MAClB,OAAOA,CAAC;IACV;EACF;EACA,OAAO,CAAC,CAAC;AACX;AACA,MAAMC,oBAAoB,GAAG3B,mBAAmB,CAAC,CAAC;;AAElD;AACA,MAAM4B,QAAQ,GAAG,CAAC;AAClB,MAAMC,+BAA+B,GAAGC,UAAU,IAAI;EACpD,IAAIC,qBAAqB;EACzB,OAAOD,UAAU,CAACE,OAAO,KAAK,IAAI,KAAK,CAACD,qBAAqB,GAAGD,UAAU,CAACE,OAAO,CAACC,aAAa,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGF,qBAAqB,CAACG,QAAQ,CAACC,QAAQ,CAACC,aAAa,CAAC,CAAC;AAC9K,CAAC;AACD,MAAMC,sBAAsB,GAAG,EAAE;AACjC,OAAO,SAASC,eAAeA,CAACC,KAAK,EAAE;EACrC,MAAM;IACJ;IACAC,iCAAiC,GAAGX,+BAA+B;IACnE;IACAY,wBAAwB,GAAG,KAAK;IAChCC,YAAY,GAAG,KAAK;IACpBC,aAAa,GAAG,KAAK;IACrBC,UAAU,GAAG,KAAK;IAClBC,YAAY,GAAG,KAAK;IACpBC,WAAW,GAAG,CAACP,KAAK,CAACQ,QAAQ;IAC7BC,aAAa,GAAG,KAAK;IACrBC,aAAa,GAAG,iBAAiB;IACjCC,YAAY,GAAGX,KAAK,CAACY,QAAQ,GAAGd,sBAAsB,GAAG,IAAI;IAC7De,gBAAgB,GAAG,KAAK;IACxBC,oBAAoB,GAAG,KAAK;IAC5BC,QAAQ,EAAEC,YAAY;IACtBC,sBAAsB,GAAG,KAAK;IAC9BC,eAAe,GAAG,KAAK;IACvBC,aAAa,GAAG/B,oBAAoB;IACpCgC,qBAAqB,GAAG,KAAK;IAC7BZ,QAAQ,GAAG,KAAK;IAChBa,iBAAiB;IACjBC,YAAY;IACZ/C,cAAc,EAAEgD,kBAAkB,GAAG3C,MAAM,IAAI;MAC7C,IAAI4C,aAAa;MACjB,OAAO,CAACA,aAAa,GAAG5C,MAAM,CAAC6C,KAAK,KAAK,IAAI,GAAGD,aAAa,GAAG5C,MAAM;IACxE,CAAC;IACD8C,OAAO;IACPC,iBAAiB,GAAG,CAAC3B,KAAK,CAACQ,QAAQ;IACnCoB,EAAE,EAAEC,MAAM;IACVC,kBAAkB,GAAG,KAAK;IAC1BxD,UAAU,EAAEyD,cAAc;IAC1BC,oBAAoB,GAAGA,CAACpD,MAAM,EAAEqD,KAAK,KAAKrD,MAAM,KAAKqD,KAAK;IAC1DrB,QAAQ,GAAG,KAAK;IAChBsB,QAAQ;IACRC,OAAO;IACPC,iBAAiB;IACjBC,aAAa;IACbC,MAAM;IACNC,IAAI,EAAEC,QAAQ;IACdC,WAAW,GAAG,KAAK;IACnBrE,OAAO;IACPsE,QAAQ,GAAG,KAAK;IAChBC,aAAa,GAAG,CAAC3C,KAAK,CAACQ,QAAQ;IAC/ByB,KAAK,EAAEW;EACT,CAAC,GAAG5C,KAAK;EACT,MAAM4B,EAAE,GAAGzE,KAAK,CAAC0E,MAAM,CAAC;EACxB,IAAItD,cAAc,GAAGgD,kBAAkB;EACvChD,cAAc,GAAGK,MAAM,IAAI;IACzB,MAAMiE,WAAW,GAAGtB,kBAAkB,CAAC3C,MAAM,CAAC;IAC9C,IAAI,OAAOiE,WAAW,KAAK,QAAQ,EAAE;MACnC,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzC,MAAMC,eAAe,GAAGJ,WAAW,KAAKhF,SAAS,GAAG,WAAW,MAAAqF,MAAA,CAAM,OAAOL,WAAW,QAAAK,MAAA,CAAKL,WAAW,MAAG;QAC1GM,OAAO,CAACC,KAAK,wCAAAF,MAAA,CAA0CxC,aAAa,gBAAAwC,MAAA,CAAaD,eAAe,+BAAAC,MAAA,CAA4BG,IAAI,CAACnF,SAAS,CAACU,MAAM,CAAC,MAAG,CAAC;MACxJ;MACA,OAAO0E,MAAM,CAACT,WAAW,CAAC;IAC5B;IACA,OAAOA,WAAW;EACpB,CAAC;EACD,MAAMU,WAAW,GAAG5G,KAAK,CAAC6G,MAAM,CAAC,KAAK,CAAC;EACvC,MAAMC,UAAU,GAAG9G,KAAK,CAAC6G,MAAM,CAAC,IAAI,CAAC;EACrC,MAAME,QAAQ,GAAG/G,KAAK,CAAC6G,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMjE,UAAU,GAAG5C,KAAK,CAAC6G,MAAM,CAAC,IAAI,CAAC;EACrC,MAAM,CAACG,QAAQ,EAAEC,WAAW,CAAC,GAAGjH,KAAK,CAACkH,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGpH,KAAK,CAACkH,QAAQ,CAAC,CAAC,CAAC,CAAC;EACtD,MAAMG,kBAAkB,GAAG5D,aAAa,GAAG,CAAC,GAAG,CAAC,CAAC;EACjD,MAAM6D,mBAAmB,GAAGtH,KAAK,CAAC6G,MAAM,CAACQ,kBAAkB,CAAC;EAC5D,MAAM,CAAC/B,KAAK,EAAEiC,aAAa,CAAC,GAAGjH,aAAa,CAAC;IAC3CkH,UAAU,EAAEvB,SAAS;IACrBwB,OAAO,EAAEzD,YAAY;IACrB0D,IAAI,EAAE3D;EACR,CAAC,CAAC;EACF,MAAM,CAACpC,UAAU,EAAEgG,kBAAkB,CAAC,GAAGrH,aAAa,CAAC;IACrDkH,UAAU,EAAEpC,cAAc;IAC1BqC,OAAO,EAAE,EAAE;IACXC,IAAI,EAAE3D,aAAa;IACnB6D,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG9H,KAAK,CAACkH,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAMa,eAAe,GAAG/H,KAAK,CAACgI,WAAW,CAAC,CAACC,KAAK,EAAEC,QAAQ,KAAK;IAC7D;IACA;IACA,MAAMC,gBAAgB,GAAGlE,QAAQ,GAAGqB,KAAK,CAACrE,MAAM,GAAGiH,QAAQ,CAACjH,MAAM,GAAGiH,QAAQ,KAAK,IAAI;IACtF,IAAI,CAACC,gBAAgB,IAAI,CAACvE,WAAW,EAAE;MACrC;IACF;IACA,IAAIwE,aAAa;IACjB,IAAInE,QAAQ,EAAE;MACZmE,aAAa,GAAG,EAAE;IACpB,CAAC,MAAM,IAAIF,QAAQ,IAAI,IAAI,EAAE;MAC3BE,aAAa,GAAG,EAAE;IACpB,CAAC,MAAM;MACL,MAAMlC,WAAW,GAAGtE,cAAc,CAACsG,QAAQ,CAAC;MAC5CE,aAAa,GAAG,OAAOlC,WAAW,KAAK,QAAQ,GAAGA,WAAW,GAAG,EAAE;IACpE;IACA,IAAIvE,UAAU,KAAKyG,aAAa,EAAE;MAChC;IACF;IACAT,kBAAkB,CAACS,aAAa,CAAC;IACjC,IAAI1C,aAAa,EAAE;MACjBA,aAAa,CAACuC,KAAK,EAAEG,aAAa,EAAE,OAAO,CAAC;IAC9C;EACF,CAAC,EAAE,CAACxG,cAAc,EAAED,UAAU,EAAEsC,QAAQ,EAAEyB,aAAa,EAAEiC,kBAAkB,EAAE/D,WAAW,EAAE0B,KAAK,CAAC,CAAC;EACjG,MAAM,CAACM,IAAI,EAAEyC,YAAY,CAAC,GAAG/H,aAAa,CAAC;IACzCkH,UAAU,EAAE3B,QAAQ;IACpB4B,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE3D,aAAa;IACnB6D,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAM,CAACU,aAAa,EAAEC,gBAAgB,CAAC,GAAGvI,KAAK,CAACkH,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAMsB,yBAAyB,GAAG,CAACvE,QAAQ,IAAIqB,KAAK,IAAI,IAAI,IAAI3D,UAAU,KAAKC,cAAc,CAAC0D,KAAK,CAAC;EACpG,MAAMmD,SAAS,GAAG7C,IAAI,IAAI,CAACG,QAAQ;EACnC,MAAMhE,eAAe,GAAG0G,SAAS,GAAGjE,aAAa,CAAC/C,OAAO,CAACO,MAAM,CAACC,MAAM,IAAI;IACzE,IAAIwC,qBAAqB,IAAI,CAACR,QAAQ,GAAGqB,KAAK,GAAG,CAACA,KAAK,CAAC,EAAEoD,IAAI,CAACC,MAAM,IAAIA,MAAM,KAAK,IAAI,IAAItD,oBAAoB,CAACpD,MAAM,EAAE0G,MAAM,CAAC,CAAC,EAAE;MACjI,OAAO,KAAK;IACd;IACA,OAAO,IAAI;EACb,CAAC,CAAC;EACF;EACA;EACA;IACEhH,UAAU,EAAE6G,yBAAyB,IAAIF,aAAa,GAAG,EAAE,GAAG3G,UAAU;IACxEC;EACF,CAAC,CAAC,GAAG,EAAE;EACP,MAAMgH,aAAa,GAAGnI,gBAAgB,CAAC;IACrCsB,eAAe;IACfuD,KAAK;IACL3D;EACF,CAAC,CAAC;EACF3B,KAAK,CAAC6I,SAAS,CAAC,MAAM;IACpB,MAAMC,WAAW,GAAGxD,KAAK,KAAKsD,aAAa,CAACtD,KAAK;IACjD,IAAIuC,OAAO,IAAI,CAACiB,WAAW,EAAE;MAC3B;IACF;;IAEA;IACA,IAAIjF,QAAQ,IAAI,CAACiF,WAAW,EAAE;MAC5B;IACF;IACAf,eAAe,CAAC,IAAI,EAAEzC,KAAK,CAAC;EAC9B,CAAC,EAAE,CAACA,KAAK,EAAEyC,eAAe,EAAEF,OAAO,EAAEe,aAAa,CAACtD,KAAK,EAAEzB,QAAQ,CAAC,CAAC;EACpE,MAAMkF,gBAAgB,GAAGnD,IAAI,IAAI7D,eAAe,CAACd,MAAM,GAAG,CAAC,IAAI,CAAC8E,QAAQ;EACxE,IAAII,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAIf,KAAK,KAAK,IAAI,IAAI,CAACzB,QAAQ,IAAIpC,OAAO,CAACR,MAAM,GAAG,CAAC,EAAE;MACrD,MAAM+H,YAAY,GAAG,CAAC/E,QAAQ,GAAGqB,KAAK,GAAG,CAACA,KAAK,CAAC,EAAEtD,MAAM,CAAC2G,MAAM,IAAI,CAAClH,OAAO,CAACiH,IAAI,CAACzG,MAAM,IAAIoD,oBAAoB,CAACpD,MAAM,EAAE0G,MAAM,CAAC,CAAC,CAAC;MACjI,IAAIK,YAAY,CAAC/H,MAAM,GAAG,CAAC,EAAE;QAC3BuF,OAAO,CAACyC,IAAI,CAAC,+BAAA1C,MAAA,CAA+BxC,aAAa,sDAAAwC,MAAA,CAAoDyC,YAAY,CAAC/H,MAAM,GAAG,CAAC,GAAGyF,IAAI,CAACnF,SAAS,CAACyH,YAAY,CAAC,GAAGtC,IAAI,CAACnF,SAAS,CAACyH,YAAY,CAAC,CAAC,CAAC,CAAC,SAAO,6EAA6E,CAAC,CAACE,IAAI,CAAC,IAAI,CAAC,CAAC;MACxS;IACF;EACF;EACA,MAAMC,QAAQ,GAAG/I,gBAAgB,CAACgJ,UAAU,IAAI;IAC9C,IAAIA,UAAU,KAAK,CAAC,CAAC,EAAE;MACrBrC,QAAQ,CAACjE,OAAO,CAACuG,KAAK,CAAC,CAAC;IAC1B,CAAC,MAAM;MACLrC,QAAQ,CAACsC,aAAa,sBAAA/C,MAAA,CAAqB6C,UAAU,QAAI,CAAC,CAACC,KAAK,CAAC,CAAC;IACpE;EACF,CAAC,CAAC;;EAEF;EACArJ,KAAK,CAAC6I,SAAS,CAAC,MAAM;IACpB,IAAI5E,QAAQ,IAAIkD,UAAU,GAAG7B,KAAK,CAACrE,MAAM,GAAG,CAAC,EAAE;MAC7CmG,aAAa,CAAC,CAAC,CAAC,CAAC;MACjB+B,QAAQ,CAAC,CAAC,CAAC,CAAC;IACd;EACF,CAAC,EAAE,CAAC7D,KAAK,EAAErB,QAAQ,EAAEkD,UAAU,EAAEgC,QAAQ,CAAC,CAAC;EAC3C,SAASI,gBAAgBA,CAACC,KAAK,EAAEC,SAAS,EAAE;IAC1C,IAAI,CAAC7G,UAAU,CAACE,OAAO,IAAI0G,KAAK,GAAG,CAAC,IAAIA,KAAK,IAAIzH,eAAe,CAACd,MAAM,EAAE;MACvE,OAAO,CAAC,CAAC;IACX;IACA,IAAIyI,SAAS,GAAGF,KAAK;IACrB,OAAO,IAAI,EAAE;MACX,MAAMvH,MAAM,GAAGW,UAAU,CAACE,OAAO,CAACwG,aAAa,yBAAA/C,MAAA,CAAwBmD,SAAS,QAAI,CAAC;;MAErF;MACA,MAAMC,iBAAiB,GAAGrF,sBAAsB,GAAG,KAAK,GAAG,CAACrC,MAAM,IAAIA,MAAM,CAACmC,QAAQ,IAAInC,MAAM,CAAC2H,YAAY,CAAC,eAAe,CAAC,KAAK,MAAM;MACxI,IAAI3H,MAAM,IAAIA,MAAM,CAAC4H,YAAY,CAAC,UAAU,CAAC,IAAI,CAACF,iBAAiB,EAAE;QACnE;QACA,OAAOD,SAAS;MAClB;;MAEA;MACA;MACA,IAAID,SAAS,KAAK,MAAM,EAAE;QACxBC,SAAS,GAAG,CAACA,SAAS,GAAG,CAAC,IAAI3H,eAAe,CAACd,MAAM;MACtD,CAAC,MAAM;QACLyI,SAAS,GAAG,CAACA,SAAS,GAAG,CAAC,GAAG3H,eAAe,CAACd,MAAM,IAAIc,eAAe,CAACd,MAAM;MAC/E;;MAEA;MACA;MACA,IAAIyI,SAAS,KAAKF,KAAK,EAAE;QACvB,OAAO,CAAC,CAAC;MACX;IACF;EACF;EACA,MAAMM,mBAAmB,GAAG1J,gBAAgB,CAAC2J,KAAA,IAIvC;IAAA,IAJwC;MAC5C9B,KAAK;MACLuB,KAAK;MACLQ,MAAM,GAAG;IACX,CAAC,GAAAD,KAAA;IACCzC,mBAAmB,CAACxE,OAAO,GAAG0G,KAAK;;IAEnC;IACA,IAAIA,KAAK,KAAK,CAAC,CAAC,EAAE;MAChBzC,QAAQ,CAACjE,OAAO,CAACmH,eAAe,CAAC,uBAAuB,CAAC;IAC3D,CAAC,MAAM;MACLlD,QAAQ,CAACjE,OAAO,CAACoH,YAAY,CAAC,uBAAuB,KAAA3D,MAAA,CAAKtB,EAAE,cAAAsB,MAAA,CAAWiD,KAAK,CAAE,CAAC;IACjF;IACA,IAAI/D,iBAAiB,EAAE;MACrBA,iBAAiB,CAACwC,KAAK,EAAEuB,KAAK,KAAK,CAAC,CAAC,GAAG,IAAI,GAAGzH,eAAe,CAACyH,KAAK,CAAC,EAAEQ,MAAM,CAAC;IAChF;IACA,IAAI,CAACpH,UAAU,CAACE,OAAO,EAAE;MACvB;IACF;IACA,MAAMqH,IAAI,GAAGvH,UAAU,CAACE,OAAO,CAACwG,aAAa,sBAAA/C,MAAA,CAAoBhD,wBAAwB,aAAU,CAAC;IACpG,IAAI4G,IAAI,EAAE;MACRA,IAAI,CAACC,SAAS,CAACC,MAAM,IAAA9D,MAAA,CAAIhD,wBAAwB,aAAU,CAAC;MAC5D4G,IAAI,CAACC,SAAS,CAACC,MAAM,IAAA9D,MAAA,CAAIhD,wBAAwB,kBAAe,CAAC;IACnE;IACA,IAAI+G,WAAW,GAAG1H,UAAU,CAACE,OAAO;IACpC,IAAIF,UAAU,CAACE,OAAO,CAAC8G,YAAY,CAAC,MAAM,CAAC,KAAK,SAAS,EAAE;MACzDU,WAAW,GAAG1H,UAAU,CAACE,OAAO,CAACC,aAAa,CAACuG,aAAa,CAAC,kBAAkB,CAAC;IAClF;;IAEA;IACA,IAAI,CAACgB,WAAW,EAAE;MAChB;IACF;IACA,IAAId,KAAK,KAAK,CAAC,CAAC,EAAE;MAChBc,WAAW,CAACC,SAAS,GAAG,CAAC;MACzB;IACF;IACA,MAAMtI,MAAM,GAAGW,UAAU,CAACE,OAAO,CAACwG,aAAa,yBAAA/C,MAAA,CAAwBiD,KAAK,QAAI,CAAC;IACjF,IAAI,CAACvH,MAAM,EAAE;MACX;IACF;IACAA,MAAM,CAACmI,SAAS,CAACI,GAAG,IAAAjE,MAAA,CAAIhD,wBAAwB,aAAU,CAAC;IAC3D,IAAIyG,MAAM,KAAK,UAAU,EAAE;MACzB/H,MAAM,CAACmI,SAAS,CAACI,GAAG,IAAAjE,MAAA,CAAIhD,wBAAwB,kBAAe,CAAC;IAClE;;IAEA;IACA;IACA;IACA;IACA;IACA,IAAI+G,WAAW,CAACG,YAAY,GAAGH,WAAW,CAACI,YAAY,IAAIV,MAAM,KAAK,OAAO,IAAIA,MAAM,KAAK,OAAO,EAAE;MACnG,MAAMW,OAAO,GAAG1I,MAAM;MACtB,MAAM2I,YAAY,GAAGN,WAAW,CAACI,YAAY,GAAGJ,WAAW,CAACC,SAAS;MACrE,MAAMM,aAAa,GAAGF,OAAO,CAACG,SAAS,GAAGH,OAAO,CAACI,YAAY;MAC9D,IAAIF,aAAa,GAAGD,YAAY,EAAE;QAChCN,WAAW,CAACC,SAAS,GAAGM,aAAa,GAAGP,WAAW,CAACI,YAAY;MAClE,CAAC,MAAM,IAAIC,OAAO,CAACG,SAAS,GAAGH,OAAO,CAACI,YAAY,IAAIhG,OAAO,GAAG,GAAG,GAAG,CAAC,CAAC,GAAGuF,WAAW,CAACC,SAAS,EAAE;QACjGD,WAAW,CAACC,SAAS,GAAGI,OAAO,CAACG,SAAS,GAAGH,OAAO,CAACI,YAAY,IAAIhG,OAAO,GAAG,GAAG,GAAG,CAAC,CAAC;MACxF;IACF;EACF,CAAC,CAAC;EACF,MAAMiG,sBAAsB,GAAG5K,gBAAgB,CAAC6K,KAAA,IAK1C;IAAA,IAL2C;MAC/ChD,KAAK;MACLiD,IAAI;MACJzB,SAAS,GAAG,MAAM;MAClBO,MAAM,GAAG;IACX,CAAC,GAAAiB,KAAA;IACC,IAAI,CAACxC,SAAS,EAAE;MACd;IACF;IACA,MAAM0C,YAAY,GAAGA,CAAA,KAAM;MACzB,MAAMC,QAAQ,GAAGrJ,eAAe,CAACd,MAAM,GAAG,CAAC;MAC3C,IAAIiK,IAAI,KAAK,OAAO,EAAE;QACpB,OAAO7D,kBAAkB;MAC3B;MACA,IAAI6D,IAAI,KAAK,OAAO,EAAE;QACpB,OAAO,CAAC;MACV;MACA,IAAIA,IAAI,KAAK,KAAK,EAAE;QAClB,OAAOE,QAAQ;MACjB;MACA,MAAMC,QAAQ,GAAG/D,mBAAmB,CAACxE,OAAO,GAAGoI,IAAI;MACnD,IAAIG,QAAQ,GAAG,CAAC,EAAE;QAChB,IAAIA,QAAQ,KAAK,CAAC,CAAC,IAAIlG,kBAAkB,EAAE;UACzC,OAAO,CAAC,CAAC;QACX;QACA,IAAIZ,eAAe,IAAI+C,mBAAmB,CAACxE,OAAO,KAAK,CAAC,CAAC,IAAIwI,IAAI,CAACC,GAAG,CAACL,IAAI,CAAC,GAAG,CAAC,EAAE;UAC/E,OAAO,CAAC;QACV;QACA,OAAOE,QAAQ;MACjB;MACA,IAAIC,QAAQ,GAAGD,QAAQ,EAAE;QACvB,IAAIC,QAAQ,KAAKD,QAAQ,GAAG,CAAC,IAAIjG,kBAAkB,EAAE;UACnD,OAAO,CAAC,CAAC;QACX;QACA,IAAIZ,eAAe,IAAI+G,IAAI,CAACC,GAAG,CAACL,IAAI,CAAC,GAAG,CAAC,EAAE;UACzC,OAAOE,QAAQ;QACjB;QACA,OAAO,CAAC;MACV;MACA,OAAOC,QAAQ;IACjB,CAAC;IACD,MAAMG,SAAS,GAAGjC,gBAAgB,CAAC4B,YAAY,CAAC,CAAC,EAAE1B,SAAS,CAAC;IAC7DK,mBAAmB,CAAC;MAClBN,KAAK,EAAEgC,SAAS;MAChBxB,MAAM;MACN/B;IACF,CAAC,CAAC;;IAEF;IACA,IAAIzE,YAAY,IAAI0H,IAAI,KAAK,OAAO,EAAE;MACpC,IAAIM,SAAS,KAAK,CAAC,CAAC,EAAE;QACpBzE,QAAQ,CAACjE,OAAO,CAACwC,KAAK,GAAG3D,UAAU;MACrC,CAAC,MAAM;QACL,MAAMM,MAAM,GAAGL,cAAc,CAACG,eAAe,CAACyJ,SAAS,CAAC,CAAC;QACzDzE,QAAQ,CAACjE,OAAO,CAACwC,KAAK,GAAGrD,MAAM;;QAE/B;QACA;QACA,MAAMuH,KAAK,GAAGvH,MAAM,CAACH,WAAW,CAAC,CAAC,CAACK,OAAO,CAACR,UAAU,CAACG,WAAW,CAAC,CAAC,CAAC;QACpE,IAAI0H,KAAK,KAAK,CAAC,IAAI7H,UAAU,CAACV,MAAM,GAAG,CAAC,EAAE;UACxC8F,QAAQ,CAACjE,OAAO,CAAC2I,iBAAiB,CAAC9J,UAAU,CAACV,MAAM,EAAEgB,MAAM,CAAChB,MAAM,CAAC;QACtE;MACF;IACF;EACF,CAAC,CAAC;EACF,MAAMyK,iCAAiC,GAAGA,CAAA,KAAM;IAC9C,MAAMC,WAAW,GAAGA,CAACC,MAAM,EAAEjD,MAAM,KAAK;MACtC,MAAMkD,MAAM,GAAGD,MAAM,GAAGhK,cAAc,CAACgK,MAAM,CAAC,GAAG,EAAE;MACnD,MAAME,MAAM,GAAGnD,MAAM,GAAG/G,cAAc,CAAC+G,MAAM,CAAC,GAAG,EAAE;MACnD,OAAOkD,MAAM,KAAKC,MAAM;IAC1B,CAAC;IACD,IAAIxE,mBAAmB,CAACxE,OAAO,KAAK,CAAC,CAAC,IAAI8F,aAAa,CAAC7G,eAAe,IAAI6G,aAAa,CAAC7G,eAAe,CAACd,MAAM,KAAKc,eAAe,CAACd,MAAM,IAAI2H,aAAa,CAACjH,UAAU,KAAKA,UAAU,KAAKsC,QAAQ,GAAGqB,KAAK,CAACrE,MAAM,KAAK2H,aAAa,CAACtD,KAAK,CAACrE,MAAM,IAAI2H,aAAa,CAACtD,KAAK,CAACyG,KAAK,CAAC,CAACC,GAAG,EAAExJ,CAAC,KAAKZ,cAAc,CAAC0D,KAAK,CAAC9C,CAAC,CAAC,CAAC,KAAKZ,cAAc,CAACoK,GAAG,CAAC,CAAC,GAAGL,WAAW,CAAC/C,aAAa,CAACtD,KAAK,EAAEA,KAAK,CAAC,CAAC,EAAE;MACtX,MAAM2G,yBAAyB,GAAGrD,aAAa,CAAC7G,eAAe,CAACuF,mBAAmB,CAACxE,OAAO,CAAC;MAC5F,IAAImJ,yBAAyB,EAAE;QAC7B,OAAO5J,SAAS,CAACN,eAAe,EAAEE,MAAM,IAAI;UAC1C,OAAOL,cAAc,CAACK,MAAM,CAAC,KAAKL,cAAc,CAACqK,yBAAyB,CAAC;QAC7E,CAAC,CAAC;MACJ;IACF;IACA,OAAO,CAAC,CAAC;EACX,CAAC;EACD,MAAMC,oBAAoB,GAAGlM,KAAK,CAACgI,WAAW,CAAC,MAAM;IACnD,IAAI,CAACS,SAAS,EAAE;MACd;IACF;;IAEA;IACA;IACA,MAAM0D,8BAA8B,GAAGT,iCAAiC,CAAC,CAAC;IAC1E,IAAIS,8BAA8B,KAAK,CAAC,CAAC,EAAE;MACzC7E,mBAAmB,CAACxE,OAAO,GAAGqJ,8BAA8B;MAC5D;IACF;IACA,MAAMC,SAAS,GAAGnI,QAAQ,GAAGqB,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK;;IAE7C;IACA,IAAIvD,eAAe,CAACd,MAAM,KAAK,CAAC,IAAImL,SAAS,IAAI,IAAI,EAAE;MACrDpB,sBAAsB,CAAC;QACrBE,IAAI,EAAE;MACR,CAAC,CAAC;MACF;IACF;IACA,IAAI,CAACtI,UAAU,CAACE,OAAO,EAAE;MACvB;IACF;;IAEA;IACA,IAAIsJ,SAAS,IAAI,IAAI,EAAE;MACrB,MAAMC,aAAa,GAAGtK,eAAe,CAACuF,mBAAmB,CAACxE,OAAO,CAAC;;MAElE;MACA,IAAImB,QAAQ,IAAIoI,aAAa,IAAIhK,SAAS,CAACiD,KAAK,EAAE0G,GAAG,IAAI3G,oBAAoB,CAACgH,aAAa,EAAEL,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;QACzG;MACF;MACA,MAAMM,SAAS,GAAGjK,SAAS,CAACN,eAAe,EAAEwK,UAAU,IAAIlH,oBAAoB,CAACkH,UAAU,EAAEH,SAAS,CAAC,CAAC;MACvG,IAAIE,SAAS,KAAK,CAAC,CAAC,EAAE;QACpBtB,sBAAsB,CAAC;UACrBE,IAAI,EAAE;QACR,CAAC,CAAC;MACJ,CAAC,MAAM;QACLpB,mBAAmB,CAAC;UAClBN,KAAK,EAAE8C;QACT,CAAC,CAAC;MACJ;MACA;IACF;;IAEA;IACA,IAAIhF,mBAAmB,CAACxE,OAAO,IAAIf,eAAe,CAACd,MAAM,GAAG,CAAC,EAAE;MAC7D6I,mBAAmB,CAAC;QAClBN,KAAK,EAAEzH,eAAe,CAACd,MAAM,GAAG;MAClC,CAAC,CAAC;MACF;IACF;;IAEA;IACA6I,mBAAmB,CAAC;MAClBN,KAAK,EAAElC,mBAAmB,CAACxE;IAC7B,CAAC,CAAC;IACF;IACA;EACF,CAAC,EAAE;EACH;EACAf,eAAe,CAACd,MAAM;EACtB;EACA;EACAgD,QAAQ,GAAG,KAAK,GAAGqB,KAAK,EAAEb,qBAAqB,EAAEuG,sBAAsB,EAAElB,mBAAmB,EAAErB,SAAS,EAAE9G,UAAU,EAAEsC,QAAQ,CAAC,CAAC;EAC/H,MAAMuI,gBAAgB,GAAGpM,gBAAgB,CAACqM,IAAI,IAAI;IAChDvM,MAAM,CAAC0C,UAAU,EAAE6J,IAAI,CAAC;IACxB,IAAI,CAACA,IAAI,EAAE;MACT;IACF;IACAP,oBAAoB,CAAC,CAAC;EACxB,CAAC,CAAC;EACF,IAAI/F,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC;IACArG,KAAK,CAAC6I,SAAS,CAAC,MAAM;MACpB,IAAI,CAAC9B,QAAQ,CAACjE,OAAO,IAAIiE,QAAQ,CAACjE,OAAO,CAAC4J,QAAQ,KAAK,OAAO,EAAE;QAC9D,IAAI3F,QAAQ,CAACjE,OAAO,IAAIiE,QAAQ,CAACjE,OAAO,CAAC4J,QAAQ,KAAK,UAAU,EAAE;UAChElG,OAAO,CAACyC,IAAI,CAAC,uCAAA1C,MAAA,CAAuCxC,aAAa,iTAA8S,CAACmF,IAAI,CAAC,IAAI,CAAC,CAAC;QAC7X,CAAC,MAAM;UACL1C,OAAO,CAACC,KAAK,CAAC,8DAAAF,MAAA,CAA8DQ,QAAQ,CAACjE,OAAO,2DAAAyD,MAAA,CAAwDxC,aAAa,iCAA8B,EAAE,EAAEA,aAAa,KAAK,iBAAiB,GAAG,kHAAkH,GAAG,8DAA8D,CAAC,CAACmF,IAAI,CAAC,IAAI,CAAC,CAAC;QAC3a;MACF;IACF,CAAC,EAAE,CAACnF,aAAa,CAAC,CAAC;EACrB;EACA/D,KAAK,CAAC6I,SAAS,CAAC,MAAM;IACpBqD,oBAAoB,CAAC,CAAC;EACxB,CAAC,EAAE,CAACA,oBAAoB,CAAC,CAAC;EAC1B,MAAMS,UAAU,GAAG1E,KAAK,IAAI;IAC1B,IAAIrC,IAAI,EAAE;MACR;IACF;IACAyC,YAAY,CAAC,IAAI,CAAC;IAClBE,gBAAgB,CAAC,IAAI,CAAC;IACtB,IAAI5C,MAAM,EAAE;MACVA,MAAM,CAACsC,KAAK,CAAC;IACf;EACF,CAAC;EACD,MAAM2E,WAAW,GAAGA,CAAC3E,KAAK,EAAE+B,MAAM,KAAK;IACrC,IAAI,CAACpE,IAAI,EAAE;MACT;IACF;IACAyC,YAAY,CAAC,KAAK,CAAC;IACnB,IAAI7C,OAAO,EAAE;MACXA,OAAO,CAACyC,KAAK,EAAE+B,MAAM,CAAC;IACxB;EACF,CAAC;EACD,MAAM6C,WAAW,GAAGA,CAAC5E,KAAK,EAAEC,QAAQ,EAAE8B,MAAM,EAAE8C,OAAO,KAAK;IACxD,IAAI7I,QAAQ,EAAE;MACZ,IAAIqB,KAAK,CAACrE,MAAM,KAAKiH,QAAQ,CAACjH,MAAM,IAAIqE,KAAK,CAACyG,KAAK,CAAC,CAACC,GAAG,EAAExJ,CAAC,KAAKwJ,GAAG,KAAK9D,QAAQ,CAAC1F,CAAC,CAAC,CAAC,EAAE;QACpF;MACF;IACF,CAAC,MAAM,IAAI8C,KAAK,KAAK4C,QAAQ,EAAE;MAC7B;IACF;IACA,IAAI3C,QAAQ,EAAE;MACZA,QAAQ,CAAC0C,KAAK,EAAEC,QAAQ,EAAE8B,MAAM,EAAE8C,OAAO,CAAC;IAC5C;IACAvF,aAAa,CAACW,QAAQ,CAAC;EACzB,CAAC;EACD,MAAM6E,OAAO,GAAG/M,KAAK,CAAC6G,MAAM,CAAC,KAAK,CAAC;EACnC,MAAMmG,cAAc,GAAG,SAAAA,CAAC/E,KAAK,EAAEhG,MAAM,EAAsD;IAAA,IAApDgL,UAAU,GAAAjM,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,cAAc;IAAA,IAAEkM,MAAM,GAAAlM,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,SAAS;IACpF,IAAIgJ,MAAM,GAAGiD,UAAU;IACvB,IAAI/E,QAAQ,GAAGjG,MAAM;IACrB,IAAIgC,QAAQ,EAAE;MACZiE,QAAQ,GAAGiF,KAAK,CAACC,OAAO,CAAC9H,KAAK,CAAC,GAAGA,KAAK,CAAClD,KAAK,CAAC,CAAC,GAAG,EAAE;MACpD,IAAI+D,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzC,MAAMgH,OAAO,GAAGnF,QAAQ,CAAClG,MAAM,CAACgK,GAAG,IAAI3G,oBAAoB,CAACpD,MAAM,EAAE+J,GAAG,CAAC,CAAC;QACzE,IAAIqB,OAAO,CAACpM,MAAM,GAAG,CAAC,EAAE;UACtBuF,OAAO,CAACC,KAAK,CAAC,8CAAAF,MAAA,CAAgDxC,aAAa,0HAAAwC,MAAA,CAAuH8G,OAAO,CAACpM,MAAM,eAAY,CAACiI,IAAI,CAAC,IAAI,CAAC,CAAC;QAC1O;MACF;MACA,MAAMoD,SAAS,GAAGjK,SAAS,CAAC6F,QAAQ,EAAEkE,SAAS,IAAI/G,oBAAoB,CAACpD,MAAM,EAAEmK,SAAS,CAAC,CAAC;MAC3F,IAAIE,SAAS,KAAK,CAAC,CAAC,EAAE;QACpBpE,QAAQ,CAACoF,IAAI,CAACrL,MAAM,CAAC;MACvB,CAAC,MAAM,IAAIiL,MAAM,KAAK,UAAU,EAAE;QAChChF,QAAQ,CAACqF,MAAM,CAACjB,SAAS,EAAE,CAAC,CAAC;QAC7BtC,MAAM,GAAG,cAAc;MACzB;IACF;IACAjC,eAAe,CAACE,KAAK,EAAEC,QAAQ,CAAC;IAChC2E,WAAW,CAAC5E,KAAK,EAAEC,QAAQ,EAAE8B,MAAM,EAAE;MACnC/H;IACF,CAAC,CAAC;IACF,IAAI,CAACkC,oBAAoB,KAAK,CAAC8D,KAAK,IAAI,CAACA,KAAK,CAACuF,OAAO,IAAI,CAACvF,KAAK,CAACwF,OAAO,CAAC,EAAE;MACzEb,WAAW,CAAC3E,KAAK,EAAE+B,MAAM,CAAC;IAC5B;IACA,IAAIrG,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,OAAO,IAAIoJ,OAAO,CAACjK,OAAO,IAAIa,YAAY,KAAK,OAAO,IAAI,CAACoJ,OAAO,CAACjK,OAAO,EAAE;MACxHiE,QAAQ,CAACjE,OAAO,CAAC4K,IAAI,CAAC,CAAC;IACzB;EACF,CAAC;EACD,SAASC,aAAaA,CAACnE,KAAK,EAAEC,SAAS,EAAE;IACvC,IAAID,KAAK,KAAK,CAAC,CAAC,EAAE;MAChB,OAAO,CAAC,CAAC;IACX;IACA,IAAIE,SAAS,GAAGF,KAAK;IACrB,OAAO,IAAI,EAAE;MACX;MACA,IAAIC,SAAS,KAAK,MAAM,IAAIC,SAAS,KAAKpE,KAAK,CAACrE,MAAM,IAAIwI,SAAS,KAAK,UAAU,IAAIC,SAAS,KAAK,CAAC,CAAC,EAAE;QACtG,OAAO,CAAC,CAAC;MACX;MACA,MAAMzH,MAAM,GAAG+E,QAAQ,CAACsC,aAAa,sBAAA/C,MAAA,CAAqBmD,SAAS,QAAI,CAAC;;MAExE;MACA,IAAI,CAACzH,MAAM,IAAI,CAACA,MAAM,CAAC4H,YAAY,CAAC,UAAU,CAAC,IAAI5H,MAAM,CAACmC,QAAQ,IAAInC,MAAM,CAAC2H,YAAY,CAAC,eAAe,CAAC,KAAK,MAAM,EAAE;QACrHF,SAAS,IAAID,SAAS,KAAK,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;MAC5C,CAAC,MAAM;QACL,OAAOC,SAAS;MAClB;IACF;EACF;EACA,MAAMkE,cAAc,GAAGA,CAAC3F,KAAK,EAAEwB,SAAS,KAAK;IAC3C,IAAI,CAACxF,QAAQ,EAAE;MACb;IACF;IACA,IAAItC,UAAU,KAAK,EAAE,EAAE;MACrBiL,WAAW,CAAC3E,KAAK,EAAE,aAAa,CAAC;IACnC;IACA,IAAI4F,OAAO,GAAG1G,UAAU;IACxB,IAAIA,UAAU,KAAK,CAAC,CAAC,EAAE;MACrB,IAAIxF,UAAU,KAAK,EAAE,IAAI8H,SAAS,KAAK,UAAU,EAAE;QACjDoE,OAAO,GAAGvI,KAAK,CAACrE,MAAM,GAAG,CAAC;MAC5B;IACF,CAAC,MAAM;MACL4M,OAAO,IAAIpE,SAAS,KAAK,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;MACxC,IAAIoE,OAAO,GAAG,CAAC,EAAE;QACfA,OAAO,GAAG,CAAC;MACb;MACA,IAAIA,OAAO,KAAKvI,KAAK,CAACrE,MAAM,EAAE;QAC5B4M,OAAO,GAAG,CAAC,CAAC;MACd;IACF;IACAA,OAAO,GAAGF,aAAa,CAACE,OAAO,EAAEpE,SAAS,CAAC;IAC3CrC,aAAa,CAACyG,OAAO,CAAC;IACtB1E,QAAQ,CAAC0E,OAAO,CAAC;EACnB,CAAC;EACD,MAAMC,WAAW,GAAG7F,KAAK,IAAI;IAC3BrB,WAAW,CAAC9D,OAAO,GAAG,IAAI;IAC1B6E,kBAAkB,CAAC,EAAE,CAAC;IACtB,IAAIjC,aAAa,EAAE;MACjBA,aAAa,CAACuC,KAAK,EAAE,EAAE,EAAE,OAAO,CAAC;IACnC;IACA4E,WAAW,CAAC5E,KAAK,EAAEhE,QAAQ,GAAG,EAAE,GAAG,IAAI,EAAE,OAAO,CAAC;EACnD,CAAC;EACD,MAAM8J,aAAa,GAAGC,KAAK,IAAI/F,KAAK,IAAI;IACtC,IAAI+F,KAAK,CAACC,SAAS,EAAE;MACnBD,KAAK,CAACC,SAAS,CAAChG,KAAK,CAAC;IACxB;IACA,IAAIA,KAAK,CAACiG,mBAAmB,EAAE;MAC7B;IACF;IACA,IAAI/G,UAAU,KAAK,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,YAAY,CAAC,CAAChF,OAAO,CAAC8F,KAAK,CAACkG,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;MAC9E/G,aAAa,CAAC,CAAC,CAAC,CAAC;MACjB+B,QAAQ,CAAC,CAAC,CAAC,CAAC;IACd;;IAEA;IACA,IAAIlB,KAAK,CAACmG,KAAK,KAAK,GAAG,EAAE;MACvB,QAAQnG,KAAK,CAACkG,GAAG;QACf,KAAK,MAAM;UACT,IAAI1F,SAAS,IAAIzD,iBAAiB,EAAE;YAClC;YACAiD,KAAK,CAACoG,cAAc,CAAC,CAAC;YACtBrD,sBAAsB,CAAC;cACrBE,IAAI,EAAE,OAAO;cACbzB,SAAS,EAAE,MAAM;cACjBO,MAAM,EAAE,UAAU;cAClB/B;YACF,CAAC,CAAC;UACJ;UACA;QACF,KAAK,KAAK;UACR,IAAIQ,SAAS,IAAIzD,iBAAiB,EAAE;YAClC;YACAiD,KAAK,CAACoG,cAAc,CAAC,CAAC;YACtBrD,sBAAsB,CAAC;cACrBE,IAAI,EAAE,KAAK;cACXzB,SAAS,EAAE,UAAU;cACrBO,MAAM,EAAE,UAAU;cAClB/B;YACF,CAAC,CAAC;UACJ;UACA;QACF,KAAK,QAAQ;UACX;UACAA,KAAK,CAACoG,cAAc,CAAC,CAAC;UACtBrD,sBAAsB,CAAC;YACrBE,IAAI,EAAE,CAACxI,QAAQ;YACf+G,SAAS,EAAE,UAAU;YACrBO,MAAM,EAAE,UAAU;YAClB/B;UACF,CAAC,CAAC;UACF0E,UAAU,CAAC1E,KAAK,CAAC;UACjB;QACF,KAAK,UAAU;UACb;UACAA,KAAK,CAACoG,cAAc,CAAC,CAAC;UACtBrD,sBAAsB,CAAC;YACrBE,IAAI,EAAExI,QAAQ;YACd+G,SAAS,EAAE,MAAM;YACjBO,MAAM,EAAE,UAAU;YAClB/B;UACF,CAAC,CAAC;UACF0E,UAAU,CAAC1E,KAAK,CAAC;UACjB;QACF,KAAK,WAAW;UACd;UACAA,KAAK,CAACoG,cAAc,CAAC,CAAC;UACtBrD,sBAAsB,CAAC;YACrBE,IAAI,EAAE,CAAC;YACPzB,SAAS,EAAE,MAAM;YACjBO,MAAM,EAAE,UAAU;YAClB/B;UACF,CAAC,CAAC;UACF0E,UAAU,CAAC1E,KAAK,CAAC;UACjB;QACF,KAAK,SAAS;UACZ;UACAA,KAAK,CAACoG,cAAc,CAAC,CAAC;UACtBrD,sBAAsB,CAAC;YACrBE,IAAI,EAAE,CAAC,CAAC;YACRzB,SAAS,EAAE,UAAU;YACrBO,MAAM,EAAE,UAAU;YAClB/B;UACF,CAAC,CAAC;UACF0E,UAAU,CAAC1E,KAAK,CAAC;UACjB;QACF,KAAK,WAAW;UACd2F,cAAc,CAAC3F,KAAK,EAAE,UAAU,CAAC;UACjC;QACF,KAAK,YAAY;UACf2F,cAAc,CAAC3F,KAAK,EAAE,MAAM,CAAC;UAC7B;QACF,KAAK,OAAO;UACV,IAAIX,mBAAmB,CAACxE,OAAO,KAAK,CAAC,CAAC,IAAI2F,SAAS,EAAE;YACnD,MAAMxG,MAAM,GAAGF,eAAe,CAACuF,mBAAmB,CAACxE,OAAO,CAAC;YAC3D,MAAMsB,QAAQ,GAAGM,iBAAiB,GAAGA,iBAAiB,CAACzC,MAAM,CAAC,GAAG,KAAK;;YAEtE;YACAgG,KAAK,CAACoG,cAAc,CAAC,CAAC;YACtB,IAAIjK,QAAQ,EAAE;cACZ;YACF;YACA4I,cAAc,CAAC/E,KAAK,EAAEhG,MAAM,EAAE,cAAc,CAAC;;YAE7C;YACA,IAAIuB,YAAY,EAAE;cAChBuD,QAAQ,CAACjE,OAAO,CAAC2I,iBAAiB,CAAC1E,QAAQ,CAACjE,OAAO,CAACwC,KAAK,CAACrE,MAAM,EAAE8F,QAAQ,CAACjE,OAAO,CAACwC,KAAK,CAACrE,MAAM,CAAC;YAClG;UACF,CAAC,MAAM,IAAI4C,QAAQ,IAAIlC,UAAU,KAAK,EAAE,IAAI6G,yBAAyB,KAAK,KAAK,EAAE;YAC/E,IAAIvE,QAAQ,EAAE;cACZ;cACAgE,KAAK,CAACoG,cAAc,CAAC,CAAC;YACxB;YACArB,cAAc,CAAC/E,KAAK,EAAEtG,UAAU,EAAE,cAAc,EAAE,UAAU,CAAC;UAC/D;UACA;QACF,KAAK,QAAQ;UACX,IAAI8G,SAAS,EAAE;YACb;YACAR,KAAK,CAACoG,cAAc,CAAC,CAAC;YACtB;YACApG,KAAK,CAACqG,eAAe,CAAC,CAAC;YACvB1B,WAAW,CAAC3E,KAAK,EAAE,QAAQ,CAAC;UAC9B,CAAC,MAAM,IAAInE,aAAa,KAAKnC,UAAU,KAAK,EAAE,IAAIsC,QAAQ,IAAIqB,KAAK,CAACrE,MAAM,GAAG,CAAC,CAAC,EAAE;YAC/E;YACAgH,KAAK,CAACoG,cAAc,CAAC,CAAC;YACtB;YACApG,KAAK,CAACqG,eAAe,CAAC,CAAC;YACvBR,WAAW,CAAC7F,KAAK,CAAC;UACpB;UACA;QACF,KAAK,WAAW;UACd;UACA,IAAIhE,QAAQ,IAAI,CAAC8B,QAAQ,IAAIpE,UAAU,KAAK,EAAE,IAAI2D,KAAK,CAACrE,MAAM,GAAG,CAAC,EAAE;YAClE,MAAMuI,KAAK,GAAGrC,UAAU,KAAK,CAAC,CAAC,GAAG7B,KAAK,CAACrE,MAAM,GAAG,CAAC,GAAGkG,UAAU;YAC/D,MAAMe,QAAQ,GAAG5C,KAAK,CAAClD,KAAK,CAAC,CAAC;YAC9B8F,QAAQ,CAACqF,MAAM,CAAC/D,KAAK,EAAE,CAAC,CAAC;YACzBqD,WAAW,CAAC5E,KAAK,EAAEC,QAAQ,EAAE,cAAc,EAAE;cAC3CjG,MAAM,EAAEqD,KAAK,CAACkE,KAAK;YACrB,CAAC,CAAC;UACJ;UACA;QACF,KAAK,QAAQ;UACX;UACA,IAAIvF,QAAQ,IAAI,CAAC8B,QAAQ,IAAIpE,UAAU,KAAK,EAAE,IAAI2D,KAAK,CAACrE,MAAM,GAAG,CAAC,IAAIkG,UAAU,KAAK,CAAC,CAAC,EAAE;YACvF,MAAMqC,KAAK,GAAGrC,UAAU;YACxB,MAAMe,QAAQ,GAAG5C,KAAK,CAAClD,KAAK,CAAC,CAAC;YAC9B8F,QAAQ,CAACqF,MAAM,CAAC/D,KAAK,EAAE,CAAC,CAAC;YACzBqD,WAAW,CAAC5E,KAAK,EAAEC,QAAQ,EAAE,cAAc,EAAE;cAC3CjG,MAAM,EAAEqD,KAAK,CAACkE,KAAK;YACrB,CAAC,CAAC;UACJ;UACA;QACF;MACF;IACF;EACF,CAAC;EACD,MAAM+E,WAAW,GAAGtG,KAAK,IAAI;IAC3BH,UAAU,CAAC,IAAI,CAAC;IAChB,IAAIhC,WAAW,IAAI,CAACc,WAAW,CAAC9D,OAAO,EAAE;MACvC6J,UAAU,CAAC1E,KAAK,CAAC;IACnB;EACF,CAAC;EACD,MAAMuG,UAAU,GAAGvG,KAAK,IAAI;IAC1B;IACA,IAAI3E,iCAAiC,CAACV,UAAU,CAAC,EAAE;MACjDmE,QAAQ,CAACjE,OAAO,CAACuG,KAAK,CAAC,CAAC;MACxB;IACF;IACAvB,UAAU,CAAC,KAAK,CAAC;IACjBhB,UAAU,CAAChE,OAAO,GAAG,IAAI;IACzB8D,WAAW,CAAC9D,OAAO,GAAG,KAAK;IAC3B,IAAIY,UAAU,IAAI4D,mBAAmB,CAACxE,OAAO,KAAK,CAAC,CAAC,IAAI2F,SAAS,EAAE;MACjEuE,cAAc,CAAC/E,KAAK,EAAElG,eAAe,CAACuF,mBAAmB,CAACxE,OAAO,CAAC,EAAE,MAAM,CAAC;IAC7E,CAAC,MAAM,IAAIY,UAAU,IAAIG,QAAQ,IAAIlC,UAAU,KAAK,EAAE,EAAE;MACtDqL,cAAc,CAAC/E,KAAK,EAAEtG,UAAU,EAAE,MAAM,EAAE,UAAU,CAAC;IACvD,CAAC,MAAM,IAAIiC,WAAW,EAAE;MACtBmE,eAAe,CAACE,KAAK,EAAE3C,KAAK,CAAC;IAC/B;IACAsH,WAAW,CAAC3E,KAAK,EAAE,MAAM,CAAC;EAC5B,CAAC;EACD,MAAMwG,iBAAiB,GAAGxG,KAAK,IAAI;IACjC,MAAMC,QAAQ,GAAGD,KAAK,CAACyG,MAAM,CAACpJ,KAAK;IACnC,IAAI3D,UAAU,KAAKuG,QAAQ,EAAE;MAC3BP,kBAAkB,CAACO,QAAQ,CAAC;MAC5BK,gBAAgB,CAAC,KAAK,CAAC;MACvB,IAAI7C,aAAa,EAAE;QACjBA,aAAa,CAACuC,KAAK,EAAEC,QAAQ,EAAE,OAAO,CAAC;MACzC;IACF;IACA,IAAIA,QAAQ,KAAK,EAAE,EAAE;MACnB,IAAI,CAAChE,gBAAgB,IAAI,CAACD,QAAQ,EAAE;QAClC4I,WAAW,CAAC5E,KAAK,EAAE,IAAI,EAAE,OAAO,CAAC;MACnC;IACF,CAAC,MAAM;MACL0E,UAAU,CAAC1E,KAAK,CAAC;IACnB;EACF,CAAC;EACD,MAAM0G,qBAAqB,GAAG1G,KAAK,IAAI;IACrC,MAAMuB,KAAK,GAAGoF,MAAM,CAAC3G,KAAK,CAAC4G,aAAa,CAACjF,YAAY,CAAC,mBAAmB,CAAC,CAAC;IAC3E,IAAItC,mBAAmB,CAACxE,OAAO,KAAK0G,KAAK,EAAE;MACzCM,mBAAmB,CAAC;QAClB7B,KAAK;QACLuB,KAAK;QACLQ,MAAM,EAAE;MACV,CAAC,CAAC;IACJ;EACF,CAAC;EACD,MAAM8E,sBAAsB,GAAG7G,KAAK,IAAI;IACtC6B,mBAAmB,CAAC;MAClB7B,KAAK;MACLuB,KAAK,EAAEoF,MAAM,CAAC3G,KAAK,CAAC4G,aAAa,CAACjF,YAAY,CAAC,mBAAmB,CAAC,CAAC;MACpEI,MAAM,EAAE;IACV,CAAC,CAAC;IACF+C,OAAO,CAACjK,OAAO,GAAG,IAAI;EACxB,CAAC;EACD,MAAMiM,iBAAiB,GAAG9G,KAAK,IAAI;IACjC,MAAMuB,KAAK,GAAGoF,MAAM,CAAC3G,KAAK,CAAC4G,aAAa,CAACjF,YAAY,CAAC,mBAAmB,CAAC,CAAC;IAC3EoD,cAAc,CAAC/E,KAAK,EAAElG,eAAe,CAACyH,KAAK,CAAC,EAAE,cAAc,CAAC;IAC7DuD,OAAO,CAACjK,OAAO,GAAG,KAAK;EACzB,CAAC;EACD,MAAMkM,eAAe,GAAGxF,KAAK,IAAIvB,KAAK,IAAI;IACxC,MAAMC,QAAQ,GAAG5C,KAAK,CAAClD,KAAK,CAAC,CAAC;IAC9B8F,QAAQ,CAACqF,MAAM,CAAC/D,KAAK,EAAE,CAAC,CAAC;IACzBqD,WAAW,CAAC5E,KAAK,EAAEC,QAAQ,EAAE,cAAc,EAAE;MAC3CjG,MAAM,EAAEqD,KAAK,CAACkE,KAAK;IACrB,CAAC,CAAC;EACJ,CAAC;EACD,MAAMyF,oBAAoB,GAAGhH,KAAK,IAAI;IACpC,IAAIrC,IAAI,EAAE;MACRgH,WAAW,CAAC3E,KAAK,EAAE,aAAa,CAAC;IACnC,CAAC,MAAM;MACL0E,UAAU,CAAC1E,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMiH,eAAe,GAAGjH,KAAK,IAAI;IAC/B;IACA,IAAI,CAACA,KAAK,CAAC4G,aAAa,CAAC7L,QAAQ,CAACiF,KAAK,CAACyG,MAAM,CAAC,EAAE;MAC/C;IACF;IACA,IAAIzG,KAAK,CAACyG,MAAM,CAAC9E,YAAY,CAAC,IAAI,CAAC,KAAK3E,EAAE,EAAE;MAC1CgD,KAAK,CAACoG,cAAc,CAAC,CAAC;IACxB;EACF,CAAC;;EAED;EACA,MAAMc,WAAW,GAAGlH,KAAK,IAAI;IAC3B;IACA,IAAI,CAACA,KAAK,CAAC4G,aAAa,CAAC7L,QAAQ,CAACiF,KAAK,CAACyG,MAAM,CAAC,EAAE;MAC/C;IACF;IACA3H,QAAQ,CAACjE,OAAO,CAACuG,KAAK,CAAC,CAAC;IACxB,IAAIrD,aAAa,IAAIc,UAAU,CAAChE,OAAO,IAAIiE,QAAQ,CAACjE,OAAO,CAACsM,YAAY,GAAGrI,QAAQ,CAACjE,OAAO,CAACuM,cAAc,KAAK,CAAC,EAAE;MAChHtI,QAAQ,CAACjE,OAAO,CAACwM,MAAM,CAAC,CAAC;IAC3B;IACAxI,UAAU,CAAChE,OAAO,GAAG,KAAK;EAC5B,CAAC;EACD,MAAMyM,oBAAoB,GAAGtH,KAAK,IAAI;IACpC,IAAI,CAAC5D,YAAY,KAAK1C,UAAU,KAAK,EAAE,IAAI,CAACiE,IAAI,CAAC,EAAE;MACjDqJ,oBAAoB,CAAChH,KAAK,CAAC;IAC7B;EACF,CAAC;EACD,IAAIuH,KAAK,GAAG3L,QAAQ,IAAIlC,UAAU,CAACV,MAAM,GAAG,CAAC;EAC7CuO,KAAK,GAAGA,KAAK,KAAKvL,QAAQ,GAAGqB,KAAK,CAACrE,MAAM,GAAG,CAAC,GAAGqE,KAAK,KAAK,IAAI,CAAC;EAC/D,IAAImK,cAAc,GAAG1N,eAAe;EACpC,IAAIgD,OAAO,EAAE;IACX;IACA,MAAM2K,OAAO,GAAG,IAAIC,GAAG,CAAC,CAAC;IACzB,IAAI1G,IAAI,GAAG,KAAK;IAChBwG,cAAc,GAAG1N,eAAe,CAAC6N,MAAM,CAAC,CAACC,GAAG,EAAE5N,MAAM,EAAEuH,KAAK,KAAK;MAC9D,MAAMsG,KAAK,GAAG/K,OAAO,CAAC9C,MAAM,CAAC;MAC7B,IAAI4N,GAAG,CAAC5O,MAAM,GAAG,CAAC,IAAI4O,GAAG,CAACA,GAAG,CAAC5O,MAAM,GAAG,CAAC,CAAC,CAAC6O,KAAK,KAAKA,KAAK,EAAE;QACzDD,GAAG,CAACA,GAAG,CAAC5O,MAAM,GAAG,CAAC,CAAC,CAACQ,OAAO,CAAC6L,IAAI,CAACrL,MAAM,CAAC;MAC1C,CAAC,MAAM;QACL,IAAIkE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;UACzC,IAAIqJ,OAAO,CAACK,GAAG,CAACD,KAAK,CAAC,IAAI,CAAC7G,IAAI,EAAE;YAC/BzC,OAAO,CAACyC,IAAI,oEAAA1C,MAAA,CAAsExC,aAAa,mCAAgC,8EAA8E,CAAC;YAC9MkF,IAAI,GAAG,IAAI;UACb;UACAyG,OAAO,CAACM,GAAG,CAACF,KAAK,EAAE,IAAI,CAAC;QAC1B;QACAD,GAAG,CAACvC,IAAI,CAAC;UACPa,GAAG,EAAE3E,KAAK;UACVA,KAAK;UACLsG,KAAK;UACLrO,OAAO,EAAE,CAACQ,MAAM;QAClB,CAAC,CAAC;MACJ;MACA,OAAO4N,GAAG;IACZ,CAAC,EAAE,EAAE,CAAC;EACR;EACA,IAAIxL,YAAY,IAAIwD,OAAO,EAAE;IAC3B2G,UAAU,CAAC,CAAC;EACd;EACA,OAAO;IACLyB,YAAY,EAAE,SAAAA,CAAA;MAAA,IAACjC,KAAK,GAAAhN,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;MAAA,OAAKjB,QAAQ,CAAC;QACrC,WAAW,EAAEgJ,gBAAgB,MAAAxC,MAAA,CAAMtB,EAAE,gBAAa;MACpD,CAAC,EAAE+I,KAAK,EAAE;QACRC,SAAS,EAAEF,aAAa,CAACC,KAAK,CAAC;QAC/BkC,WAAW,EAAEhB,eAAe;QAC5BiB,OAAO,EAAEhB;MACX,CAAC,CAAC;IAAA;IACFiB,kBAAkB,EAAEA,CAAA,MAAO;MACzBnL,EAAE,KAAAsB,MAAA,CAAKtB,EAAE,WAAQ;MACjBoL,OAAO,EAAEpL;IACX,CAAC,CAAC;IACFqL,aAAa,EAAEA,CAAA,MAAO;MACpBrL,EAAE;MACFK,KAAK,EAAE3D,UAAU;MACjB4O,MAAM,EAAE/B,UAAU;MAClBgC,OAAO,EAAEjC,WAAW;MACpBhJ,QAAQ,EAAEkJ,iBAAiB;MAC3ByB,WAAW,EAAEX,oBAAoB;MACjC;MACA;MACA,uBAAuB,EAAE9G,SAAS,GAAG,EAAE,GAAG,IAAI;MAC9C,mBAAmB,EAAEjF,YAAY,GAAG,MAAM,GAAG,MAAM;MACnD,eAAe,EAAEuF,gBAAgB,MAAAxC,MAAA,CAAMtB,EAAE,gBAAa/D,SAAS;MAC/D,eAAe,EAAE6H,gBAAgB;MACjC;MACA;MACAvF,YAAY,EAAE,KAAK;MACnBiN,GAAG,EAAE1J,QAAQ;MACb2J,cAAc,EAAE,MAAM;MACtBC,UAAU,EAAE,OAAO;MACnBC,IAAI,EAAE,UAAU;MAChBxM,QAAQ,EAAEC;IACZ,CAAC,CAAC;IACFwM,aAAa,EAAEA,CAAA,MAAO;MACpBC,QAAQ,EAAE,CAAC,CAAC;MACZC,IAAI,EAAE,QAAQ;MACdZ,OAAO,EAAErC;IACX,CAAC,CAAC;IACFkD,sBAAsB,EAAEA,CAAA,MAAO;MAC7BF,QAAQ,EAAE,CAAC,CAAC;MACZC,IAAI,EAAE,QAAQ;MACdZ,OAAO,EAAElB;IACX,CAAC,CAAC;IACFgC,WAAW,EAAEC,KAAA;MAAA,IAAC;QACZ1H;MACF,CAAC,GAAA0H,KAAA;MAAA,OAAKnR,QAAQ,CAAC;QACboO,GAAG,EAAE3E,KAAK;QACV,gBAAgB,EAAEA,KAAK;QACvBsH,QAAQ,EAAE,CAAC;MACb,CAAC,EAAE,CAAC/K,QAAQ,IAAI;QACdoL,QAAQ,EAAEnC,eAAe,CAACxF,KAAK;MACjC,CAAC,CAAC;IAAA;IACF4H,eAAe,EAAEA,CAAA,MAAO;MACtBR,IAAI,EAAE,SAAS;MACf3L,EAAE,KAAAsB,MAAA,CAAKtB,EAAE,aAAU;MACnB,iBAAiB,KAAAsB,MAAA,CAAKtB,EAAE,WAAQ;MAChCwL,GAAG,EAAEjE,gBAAgB;MACrB0D,WAAW,EAAEjI,KAAK,IAAI;QACpB;QACAA,KAAK,CAACoG,cAAc,CAAC,CAAC;MACxB;IACF,CAAC,CAAC;IACFgD,cAAc,EAAEC,KAAA,IAGV;MAAA,IAHW;QACf9H,KAAK;QACLvH;MACF,CAAC,GAAAqP,KAAA;MACC,IAAIC,aAAa;MACjB,MAAMC,QAAQ,GAAG,CAACvN,QAAQ,GAAGqB,KAAK,GAAG,CAACA,KAAK,CAAC,EAAEoD,IAAI,CAACC,MAAM,IAAIA,MAAM,IAAI,IAAI,IAAItD,oBAAoB,CAACpD,MAAM,EAAE0G,MAAM,CAAC,CAAC;MACpH,MAAMvE,QAAQ,GAAGM,iBAAiB,GAAGA,iBAAiB,CAACzC,MAAM,CAAC,GAAG,KAAK;MACtE,OAAO;QACLkM,GAAG,EAAE,CAACoD,aAAa,GAAG5M,YAAY,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,YAAY,CAAC1C,MAAM,CAAC,KAAK,IAAI,GAAGsP,aAAa,GAAG3P,cAAc,CAACK,MAAM,CAAC;QAC5H6O,QAAQ,EAAE,CAAC,CAAC;QACZF,IAAI,EAAE,QAAQ;QACd3L,EAAE,KAAAsB,MAAA,CAAKtB,EAAE,cAAAsB,MAAA,CAAWiD,KAAK,CAAE;QAC3BiI,WAAW,EAAE9C,qBAAqB;QAClCwB,OAAO,EAAEpB,iBAAiB;QAC1B2C,YAAY,EAAE5C,sBAAsB;QACpC,mBAAmB,EAAEtF,KAAK;QAC1B,eAAe,EAAEpF,QAAQ;QACzB,eAAe,EAAEoN;MACnB,CAAC;IACH,CAAC;IACDvM,EAAE;IACFtD,UAAU;IACV2D,KAAK;IACLkK,KAAK;IACLmC,QAAQ,EAAElJ,SAAS,IAAIzB,QAAQ;IAC/ByB,SAAS;IACTZ,OAAO,EAAEA,OAAO,IAAIV,UAAU,KAAK,CAAC,CAAC;IACrCH,QAAQ;IACRC,WAAW;IACXE,UAAU;IACVsI;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}