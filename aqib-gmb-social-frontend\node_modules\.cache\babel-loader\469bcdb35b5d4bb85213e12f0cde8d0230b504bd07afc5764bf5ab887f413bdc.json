{"ast": null, "code": "'use client';\n\nimport useLazyRef from '../useLazyRef/useLazyRef';\nimport useOnMount from '../useOnMount/useOnMount';\nexport class Timeout {\n  constructor() {\n    this.currentId = null;\n    this.clear = () => {\n      if (this.currentId !== null) {\n        clearTimeout(this.currentId);\n        this.currentId = null;\n      }\n    };\n    this.disposeEffect = () => {\n      return this.clear;\n    };\n  }\n  static create() {\n    return new Timeout();\n  }\n  /**\n   * Executes `fn` after `delay`, clearing any previously scheduled call.\n   */\n  start(delay, fn) {\n    this.clear();\n    this.currentId = setTimeout(() => {\n      this.currentId = null;\n      fn();\n    }, delay);\n  }\n}\nexport default function useTimeout() {\n  const timeout = useLazyRef(Timeout.create).current;\n  useOnMount(timeout.disposeEffect);\n  return timeout;\n}", "map": {"version": 3, "names": ["useLazyRef", "useOnMount", "Timeout", "constructor", "currentId", "clear", "clearTimeout", "disposeEffect", "create", "start", "delay", "fn", "setTimeout", "useTimeout", "timeout", "current"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/node_modules/@mui/base/node_modules/@mui/utils/esm/useTimeout/useTimeout.js"], "sourcesContent": ["'use client';\n\nimport useLazyRef from '../useLazyRef/useLazyRef';\nimport useOnMount from '../useOnMount/useOnMount';\nexport class Timeout {\n  constructor() {\n    this.currentId = null;\n    this.clear = () => {\n      if (this.currentId !== null) {\n        clearTimeout(this.currentId);\n        this.currentId = null;\n      }\n    };\n    this.disposeEffect = () => {\n      return this.clear;\n    };\n  }\n  static create() {\n    return new Timeout();\n  }\n  /**\n   * Executes `fn` after `delay`, clearing any previously scheduled call.\n   */\n  start(delay, fn) {\n    this.clear();\n    this.currentId = setTimeout(() => {\n      this.currentId = null;\n      fn();\n    }, delay);\n  }\n}\nexport default function useTimeout() {\n  const timeout = useLazyRef(Timeout.create).current;\n  useOnMount(timeout.disposeEffect);\n  return timeout;\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,0BAA0B;AACjD,OAAOC,UAAU,MAAM,0BAA0B;AACjD,OAAO,MAAMC,OAAO,CAAC;EACnBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,KAAK,GAAG,MAAM;MACjB,IAAI,IAAI,CAACD,SAAS,KAAK,IAAI,EAAE;QAC3BE,YAAY,CAAC,IAAI,CAACF,SAAS,CAAC;QAC5B,IAAI,CAACA,SAAS,GAAG,IAAI;MACvB;IACF,CAAC;IACD,IAAI,CAACG,aAAa,GAAG,MAAM;MACzB,OAAO,IAAI,CAACF,KAAK;IACnB,CAAC;EACH;EACA,OAAOG,MAAMA,CAAA,EAAG;IACd,OAAO,IAAIN,OAAO,CAAC,CAAC;EACtB;EACA;AACF;AACA;EACEO,KAAKA,CAACC,KAAK,EAAEC,EAAE,EAAE;IACf,IAAI,CAACN,KAAK,CAAC,CAAC;IACZ,IAAI,CAACD,SAAS,GAAGQ,UAAU,CAAC,MAAM;MAChC,IAAI,CAACR,SAAS,GAAG,IAAI;MACrBO,EAAE,CAAC,CAAC;IACN,CAAC,EAAED,KAAK,CAAC;EACX;AACF;AACA,eAAe,SAASG,UAAUA,CAAA,EAAG;EACnC,MAAMC,OAAO,GAAGd,UAAU,CAACE,OAAO,CAACM,MAAM,CAAC,CAACO,OAAO;EAClDd,UAAU,CAACa,OAAO,CAACP,aAAa,CAAC;EACjC,OAAOO,OAAO;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}