{"ast": null, "code": "// A change of the browser zoom change the scrollbar size.\n// Credit https://github.com/twbs/bootstrap/blob/488fd8afc535ca3a6ad4dc581f5e89217b6a36ac/js/src/util/scrollbar.js#L14-L18\nexport default function getScrollbarSize(doc) {\n  // https://developer.mozilla.org/en-US/docs/Web/API/Window/innerWidth#usage_notes\n  const documentWidth = doc.documentElement.clientWidth;\n  return Math.abs(window.innerWidth - documentWidth);\n}", "map": {"version": 3, "names": ["getScrollbarSize", "doc", "documentWidth", "documentElement", "clientWidth", "Math", "abs", "window", "innerWidth"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/node_modules/@mui/lab/node_modules/@mui/utils/esm/getScrollbarSize/getScrollbarSize.js"], "sourcesContent": ["// A change of the browser zoom change the scrollbar size.\n// Credit https://github.com/twbs/bootstrap/blob/488fd8afc535ca3a6ad4dc581f5e89217b6a36ac/js/src/util/scrollbar.js#L14-L18\nexport default function getScrollbarSize(doc) {\n  // https://developer.mozilla.org/en-US/docs/Web/API/Window/innerWidth#usage_notes\n  const documentWidth = doc.documentElement.clientWidth;\n  return Math.abs(window.innerWidth - documentWidth);\n}"], "mappings": "AAAA;AACA;AACA,eAAe,SAASA,gBAAgBA,CAACC,GAAG,EAAE;EAC5C;EACA,MAAMC,aAAa,GAAGD,GAAG,CAACE,eAAe,CAACC,WAAW;EACrD,OAAOC,IAAI,CAACC,GAAG,CAACC,MAAM,CAACC,UAAU,GAAGN,aAAa,CAAC;AACpD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}