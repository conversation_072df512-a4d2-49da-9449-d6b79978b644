{"ast": null, "code": "'use strict';\n\n/** @type {import('.')} */\nmodule.exports = ['Float16Array', 'Float32Array', 'Float64Array', 'Int8Array', 'Int16Array', 'Int32Array', 'Uint8Array', 'Uint8ClampedArray', 'Uint16Array', 'Uint32Array', 'BigInt64Array', 'BigUint64Array'];", "map": {"version": 3, "names": ["module", "exports"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/node_modules/possible-typed-array-names/index.js"], "sourcesContent": ["'use strict';\n\n/** @type {import('.')} */\nmodule.exports = [\n\t'Float16Array',\n\t'Float32Array',\n\t'Float64Array',\n\t'Int8Array',\n\t'Int16Array',\n\t'Int32Array',\n\t'Uint8Array',\n\t'Uint8ClampedArray',\n\t'Uint16Array',\n\t'Uint32Array',\n\t'BigInt64Array',\n\t'BigUint64Array'\n];\n"], "mappings": "AAAA,YAAY;;AAEZ;AACAA,MAAM,CAACC,OAAO,GAAG,CAChB,cAAc,EACd,cAAc,EACd,cAAc,EACd,WAAW,EACX,YAAY,EACZ,YAAY,EACZ,YAAY,EACZ,mBAAmB,EACnB,aAAa,EACb,aAAa,EACb,eAAe,EACf,gBAAgB,CAChB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}