{"ast": null, "code": "import { clamp } from '@mui/utils';\nexport function clampStepwise(val) {\n  let min = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : Number.MIN_SAFE_INTEGER;\n  let max = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : Number.MAX_SAFE_INTEGER;\n  let stepProp = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : NaN;\n  if (Number.isNaN(stepProp)) {\n    return clamp(val, min, max);\n  }\n  const step = stepProp || 1;\n  const remainder = val % step;\n  const positivity = Math.sign(remainder);\n  if (Math.abs(remainder) > step / 2) {\n    return clamp(val + positivity * (step - Math.abs(remainder)), min, max);\n  }\n  return clamp(val - positivity * Math.abs(remainder), min, max);\n}\nexport function isNumber(val) {\n  return typeof val === 'number' && !Number.isNaN(val) && Number.isFinite(val);\n}", "map": {"version": 3, "names": ["clamp", "clampStepwise", "val", "min", "arguments", "length", "undefined", "Number", "MIN_SAFE_INTEGER", "max", "MAX_SAFE_INTEGER", "stepProp", "NaN", "isNaN", "step", "remainder", "positivity", "Math", "sign", "abs", "isNumber", "isFinite"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/node_modules/@mui/base/unstable_useNumberInput/utils.js"], "sourcesContent": ["import { clamp } from '@mui/utils';\nexport function clampStepwise(val, min = Number.MIN_SAFE_INTEGER, max = Number.MAX_SAFE_INTEGER, stepProp = NaN) {\n  if (Number.isNaN(stepProp)) {\n    return clamp(val, min, max);\n  }\n  const step = stepProp || 1;\n  const remainder = val % step;\n  const positivity = Math.sign(remainder);\n  if (Math.abs(remainder) > step / 2) {\n    return clamp(val + positivity * (step - Math.abs(remainder)), min, max);\n  }\n  return clamp(val - positivity * Math.abs(remainder), min, max);\n}\nexport function isNumber(val) {\n  return typeof val === 'number' && !Number.isNaN(val) && Number.isFinite(val);\n}"], "mappings": "AAAA,SAASA,KAAK,QAAQ,YAAY;AAClC,OAAO,SAASC,aAAaA,CAACC,GAAG,EAAgF;EAAA,IAA9EC,GAAG,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGG,MAAM,CAACC,gBAAgB;EAAA,IAAEC,GAAG,GAAAL,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGG,MAAM,CAACG,gBAAgB;EAAA,IAAEC,QAAQ,GAAAP,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGQ,GAAG;EAC7G,IAAIL,MAAM,CAACM,KAAK,CAACF,QAAQ,CAAC,EAAE;IAC1B,OAAOX,KAAK,CAACE,GAAG,EAAEC,GAAG,EAAEM,GAAG,CAAC;EAC7B;EACA,MAAMK,IAAI,GAAGH,QAAQ,IAAI,CAAC;EAC1B,MAAMI,SAAS,GAAGb,GAAG,GAAGY,IAAI;EAC5B,MAAME,UAAU,GAAGC,IAAI,CAACC,IAAI,CAACH,SAAS,CAAC;EACvC,IAAIE,IAAI,CAACE,GAAG,CAACJ,SAAS,CAAC,GAAGD,IAAI,GAAG,CAAC,EAAE;IAClC,OAAOd,KAAK,CAACE,GAAG,GAAGc,UAAU,IAAIF,IAAI,GAAGG,IAAI,CAACE,GAAG,CAACJ,SAAS,CAAC,CAAC,EAAEZ,GAAG,EAAEM,GAAG,CAAC;EACzE;EACA,OAAOT,KAAK,CAACE,GAAG,GAAGc,UAAU,GAAGC,IAAI,CAACE,GAAG,CAACJ,SAAS,CAAC,EAAEZ,GAAG,EAAEM,GAAG,CAAC;AAChE;AACA,OAAO,SAASW,QAAQA,CAAClB,GAAG,EAAE;EAC5B,OAAO,OAAOA,GAAG,KAAK,QAAQ,IAAI,CAACK,MAAM,CAACM,KAAK,CAACX,GAAG,CAAC,IAAIK,MAAM,CAACc,QAAQ,CAACnB,GAAG,CAAC;AAC9E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}