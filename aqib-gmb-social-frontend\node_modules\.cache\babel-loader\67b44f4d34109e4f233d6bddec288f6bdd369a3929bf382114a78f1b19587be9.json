{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { unstable_useControlled as useControlled } from '@mui/utils';\nimport { useCompoundParent } from '../useCompound';\n/**\n *\n * Demos:\n *\n * - [Tabs](https://mui.com/base-ui/react-tabs/#hooks)\n *\n * API:\n *\n * - [useTabs API](https://mui.com/base-ui/react-tabs/hooks-api/#use-tabs)\n */\nfunction useTabs(parameters) {\n  const {\n    value: valueProp,\n    defaultValue,\n    onChange,\n    orientation = 'horizontal',\n    direction = 'ltr',\n    selectionFollowsFocus = false\n  } = parameters;\n  const [value, setValue] = useControlled({\n    controlled: valueProp,\n    default: defaultValue,\n    name: 'Tabs',\n    state: 'value'\n  });\n  const onSelected = React.useCallback((event, newValue) => {\n    setValue(newValue);\n    onChange == null || onChange(event, newValue);\n  }, [onChange, setValue]);\n  const {\n    subitems: tabPanels,\n    contextValue: compoundComponentContextValue\n  } = useCompoundParent();\n  const tabIdLookup = React.useRef(() => undefined);\n  const getTabPanelId = React.useCallback(tabValue => {\n    var _tabPanels$get;\n    return (_tabPanels$get = tabPanels.get(tabValue)) == null ? void 0 : _tabPanels$get.id;\n  }, [tabPanels]);\n  const getTabId = React.useCallback(tabPanelId => {\n    return tabIdLookup.current(tabPanelId);\n  }, []);\n  const registerTabIdLookup = React.useCallback(lookupFunction => {\n    tabIdLookup.current = lookupFunction;\n  }, []);\n  return {\n    contextValue: _extends({\n      direction,\n      getTabId,\n      getTabPanelId,\n      onSelected,\n      orientation,\n      registerTabIdLookup,\n      selectionFollowsFocus,\n      value\n    }, compoundComponentContextValue)\n  };\n}\nexport { useTabs };", "map": {"version": 3, "names": ["_extends", "React", "unstable_useControlled", "useControlled", "useCompoundParent", "useTabs", "parameters", "value", "valueProp", "defaultValue", "onChange", "orientation", "direction", "selectionFollowsFocus", "setValue", "controlled", "default", "name", "state", "onSelected", "useCallback", "event", "newValue", "subitems", "tabPanels", "contextValue", "compoundComponentContextValue", "tabIdLookup", "useRef", "undefined", "getTabPanelId", "tabValue", "_tabPanels$get", "get", "id", "getTabId", "tabPanelId", "current", "registerTabIdLookup", "lookupFunction"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/node_modules/@mui/base/useTabs/useTabs.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { unstable_useControlled as useControlled } from '@mui/utils';\nimport { useCompoundParent } from '../useCompound';\n/**\n *\n * Demos:\n *\n * - [Tabs](https://mui.com/base-ui/react-tabs/#hooks)\n *\n * API:\n *\n * - [useTabs API](https://mui.com/base-ui/react-tabs/hooks-api/#use-tabs)\n */\nfunction useTabs(parameters) {\n  const {\n    value: valueProp,\n    defaultValue,\n    onChange,\n    orientation = 'horizontal',\n    direction = 'ltr',\n    selectionFollowsFocus = false\n  } = parameters;\n  const [value, setValue] = useControlled({\n    controlled: valueProp,\n    default: defaultValue,\n    name: 'Tabs',\n    state: 'value'\n  });\n  const onSelected = React.useCallback((event, newValue) => {\n    setValue(newValue);\n    onChange == null || onChange(event, newValue);\n  }, [onChange, setValue]);\n  const {\n    subitems: tabPanels,\n    contextValue: compoundComponentContextValue\n  } = useCompoundParent();\n  const tabIdLookup = React.useRef(() => undefined);\n  const getTabPanelId = React.useCallback(tabValue => {\n    var _tabPanels$get;\n    return (_tabPanels$get = tabPanels.get(tabValue)) == null ? void 0 : _tabPanels$get.id;\n  }, [tabPanels]);\n  const getTabId = React.useCallback(tabPanelId => {\n    return tabIdLookup.current(tabPanelId);\n  }, []);\n  const registerTabIdLookup = React.useCallback(lookupFunction => {\n    tabIdLookup.current = lookupFunction;\n  }, []);\n  return {\n    contextValue: _extends({\n      direction,\n      getTabId,\n      getTabPanelId,\n      onSelected,\n      orientation,\n      registerTabIdLookup,\n      selectionFollowsFocus,\n      value\n    }, compoundComponentContextValue)\n  };\n}\nexport { useTabs };"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,sBAAsB,IAAIC,aAAa,QAAQ,YAAY;AACpE,SAASC,iBAAiB,QAAQ,gBAAgB;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,OAAOA,CAACC,UAAU,EAAE;EAC3B,MAAM;IACJC,KAAK,EAAEC,SAAS;IAChBC,YAAY;IACZC,QAAQ;IACRC,WAAW,GAAG,YAAY;IAC1BC,SAAS,GAAG,KAAK;IACjBC,qBAAqB,GAAG;EAC1B,CAAC,GAAGP,UAAU;EACd,MAAM,CAACC,KAAK,EAAEO,QAAQ,CAAC,GAAGX,aAAa,CAAC;IACtCY,UAAU,EAAEP,SAAS;IACrBQ,OAAO,EAAEP,YAAY;IACrBQ,IAAI,EAAE,MAAM;IACZC,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAMC,UAAU,GAAGlB,KAAK,CAACmB,WAAW,CAAC,CAACC,KAAK,EAAEC,QAAQ,KAAK;IACxDR,QAAQ,CAACQ,QAAQ,CAAC;IAClBZ,QAAQ,IAAI,IAAI,IAAIA,QAAQ,CAACW,KAAK,EAAEC,QAAQ,CAAC;EAC/C,CAAC,EAAE,CAACZ,QAAQ,EAAEI,QAAQ,CAAC,CAAC;EACxB,MAAM;IACJS,QAAQ,EAAEC,SAAS;IACnBC,YAAY,EAAEC;EAChB,CAAC,GAAGtB,iBAAiB,CAAC,CAAC;EACvB,MAAMuB,WAAW,GAAG1B,KAAK,CAAC2B,MAAM,CAAC,MAAMC,SAAS,CAAC;EACjD,MAAMC,aAAa,GAAG7B,KAAK,CAACmB,WAAW,CAACW,QAAQ,IAAI;IAClD,IAAIC,cAAc;IAClB,OAAO,CAACA,cAAc,GAAGR,SAAS,CAACS,GAAG,CAACF,QAAQ,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGC,cAAc,CAACE,EAAE;EACxF,CAAC,EAAE,CAACV,SAAS,CAAC,CAAC;EACf,MAAMW,QAAQ,GAAGlC,KAAK,CAACmB,WAAW,CAACgB,UAAU,IAAI;IAC/C,OAAOT,WAAW,CAACU,OAAO,CAACD,UAAU,CAAC;EACxC,CAAC,EAAE,EAAE,CAAC;EACN,MAAME,mBAAmB,GAAGrC,KAAK,CAACmB,WAAW,CAACmB,cAAc,IAAI;IAC9DZ,WAAW,CAACU,OAAO,GAAGE,cAAc;EACtC,CAAC,EAAE,EAAE,CAAC;EACN,OAAO;IACLd,YAAY,EAAEzB,QAAQ,CAAC;MACrBY,SAAS;MACTuB,QAAQ;MACRL,aAAa;MACbX,UAAU;MACVR,WAAW;MACX2B,mBAAmB;MACnBzB,qBAAqB;MACrBN;IACF,CAAC,EAAEmB,6BAA6B;EAClC,CAAC;AACH;AACA,SAASrB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}