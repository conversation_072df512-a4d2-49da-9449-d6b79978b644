{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport AlertTitle from '@mui/material/AlertTitle';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nlet warnedOnce = false;\n\n/**\n * @ignore - do not document.\n */\nexport default /*#__PURE__*/React.forwardRef(function DeprecatedAlertTitle(props, ref) {\n  if (!warnedOnce) {\n    console.warn(['MUI: The AlertTitle component was moved from the lab to the core.', '', \"You should use `import { AlertTitle } from '@mui/material'`\", \"or `import AlertTitle from '@mui/material/AlertTitle'`\"].join('\\n'));\n    warnedOnce = true;\n  }\n  return /*#__PURE__*/_jsx(AlertTitle, _extends({\n    ref: ref\n  }, props));\n});", "map": {"version": 3, "names": ["_extends", "React", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jsx", "_jsx", "warnedOnce", "forwardRef", "DeprecatedAlertTitle", "props", "ref", "console", "warn", "join"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/node_modules/@mui/lab/AlertTitle/AlertTitle.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport AlertTitle from '@mui/material/AlertTitle';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nlet warnedOnce = false;\n\n/**\n * @ignore - do not document.\n */\nexport default /*#__PURE__*/React.forwardRef(function DeprecatedAlertTitle(props, ref) {\n  if (!warnedOnce) {\n    console.warn(['MUI: The AlertTitle component was moved from the lab to the core.', '', \"You should use `import { AlertTitle } from '@mui/material'`\", \"or `import AlertTitle from '@mui/material/AlertTitle'`\"].join('\\n'));\n    warnedOnce = true;\n  }\n  return /*#__PURE__*/_jsx(AlertTitle, _extends({\n    ref: ref\n  }, props));\n});"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,0BAA0B;AACjD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,IAAIC,UAAU,GAAG,KAAK;;AAEtB;AACA;AACA;AACA,eAAe,aAAaJ,KAAK,CAACK,UAAU,CAAC,SAASC,oBAAoBA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACrF,IAAI,CAACJ,UAAU,EAAE;IACfK,OAAO,CAACC,IAAI,CAAC,CAAC,mEAAmE,EAAE,EAAE,EAAE,6DAA6D,EAAE,wDAAwD,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC3NP,UAAU,GAAG,IAAI;EACnB;EACA,OAAO,aAAaD,IAAI,CAACF,UAAU,EAAEF,QAAQ,CAAC;IAC5CS,GAAG,EAAEA;EACP,CAAC,EAAED,KAAK,CAAC,CAAC;AACZ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}