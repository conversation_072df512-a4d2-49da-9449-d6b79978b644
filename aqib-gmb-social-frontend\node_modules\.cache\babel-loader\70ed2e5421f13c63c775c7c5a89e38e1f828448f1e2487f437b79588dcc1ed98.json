{"ast": null, "code": "function clamp(val) {\n  let min = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : Number.MIN_SAFE_INTEGER;\n  let max = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : Number.MAX_SAFE_INTEGER;\n  return Math.max(min, Math.min(val, max));\n}\nexport default clamp;", "map": {"version": 3, "names": ["clamp", "val", "min", "arguments", "length", "undefined", "Number", "MIN_SAFE_INTEGER", "max", "MAX_SAFE_INTEGER", "Math"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/node_modules/@mui/base/node_modules/@mui/utils/esm/clamp/clamp.js"], "sourcesContent": ["function clamp(val, min = Number.MIN_SAFE_INTEGER, max = Number.MAX_SAFE_INTEGER) {\n  return Math.max(min, Math.min(val, max));\n}\nexport default clamp;"], "mappings": "AAAA,SAASA,KAAKA,CAACC,GAAG,EAAgE;EAAA,IAA9DC,GAAG,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGG,MAAM,CAACC,gBAAgB;EAAA,IAAEC,GAAG,GAAAL,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGG,MAAM,CAACG,gBAAgB;EAC9E,OAAOC,IAAI,CAACF,GAAG,CAACN,GAAG,EAAEQ,IAAI,CAACR,GAAG,CAACD,GAAG,EAAEO,GAAG,CAAC,CAAC;AAC1C;AACA,eAAeR,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}