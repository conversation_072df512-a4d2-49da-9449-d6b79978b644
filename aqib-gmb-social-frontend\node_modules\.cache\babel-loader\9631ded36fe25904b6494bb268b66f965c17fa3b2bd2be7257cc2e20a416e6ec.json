{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { unstable_useId as useId, unstable_useForkRef as useForkRef } from '@mui/utils';\nimport { useButton } from '../useButton';\nimport { useListItem } from '../useList';\nimport { DropdownActionTypes } from '../useDropdown';\nimport { DropdownContext } from '../useDropdown/DropdownContext';\nimport { combineHooksSlotProps } from '../utils/combineHooksSlotProps';\nimport { useCompoundItem } from '../useCompound';\nimport { extractEventHandlers } from '../utils/extractEventHandlers';\nfunction idGenerator(existingKeys) {\n  return \"menu-item-\".concat(existingKeys.size);\n}\nconst FALLBACK_MENU_CONTEXT = {\n  dispatch: () => {},\n  popupId: '',\n  registerPopup: () => {},\n  registerTrigger: () => {},\n  state: {\n    open: true,\n    changeReason: null\n  },\n  triggerElement: null\n};\n\n/**\n *\n * Demos:\n *\n * - [Menu](https://mui.com/base-ui/react-menu/#hooks)\n *\n * API:\n *\n * - [useMenuItem API](https://mui.com/base-ui/react-menu/hooks-api/#use-menu-item)\n */\nexport function useMenuItem(params) {\n  var _React$useContext;\n  const {\n    disabled = false,\n    id: idParam,\n    rootRef: externalRef,\n    label,\n    disableFocusOnHover = false\n  } = params;\n  const id = useId(idParam);\n  const itemRef = React.useRef(null);\n  const itemMetadata = React.useMemo(() => ({\n    disabled,\n    id: id != null ? id : '',\n    label,\n    ref: itemRef\n  }), [disabled, id, label]);\n  const {\n    dispatch\n  } = (_React$useContext = React.useContext(DropdownContext)) != null ? _React$useContext : FALLBACK_MENU_CONTEXT;\n  const {\n    getRootProps: getListRootProps,\n    highlighted\n  } = useListItem({\n    item: id,\n    handlePointerOverEvents: !disableFocusOnHover\n  });\n  const {\n    index,\n    totalItemCount\n  } = useCompoundItem(id != null ? id : idGenerator, itemMetadata);\n  const {\n    getRootProps: getButtonProps,\n    focusVisible,\n    rootRef: buttonRefHandler\n  } = useButton({\n    disabled,\n    focusableWhenDisabled: true\n  });\n  const handleRef = useForkRef(buttonRefHandler, externalRef, itemRef);\n  React.useDebugValue({\n    id,\n    highlighted,\n    disabled,\n    label\n  });\n  const createHandleClick = otherHandlers => event => {\n    var _otherHandlers$onClic;\n    (_otherHandlers$onClic = otherHandlers.onClick) == null || _otherHandlers$onClic.call(otherHandlers, event);\n    if (event.defaultMuiPrevented) {\n      return;\n    }\n    dispatch({\n      type: DropdownActionTypes.close,\n      event\n    });\n  };\n  const getOwnHandlers = function () {\n    let otherHandlers = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    return _extends({}, otherHandlers, {\n      onClick: createHandleClick(otherHandlers)\n    });\n  };\n  function getRootProps() {\n    let externalProps = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    const externalEventHandlers = extractEventHandlers(externalProps);\n    const getCombinedRootProps = combineHooksSlotProps(getOwnHandlers, combineHooksSlotProps(getButtonProps, getListRootProps));\n    return _extends({}, externalProps, externalEventHandlers, getCombinedRootProps(externalEventHandlers), {\n      id,\n      ref: handleRef,\n      role: 'menuitem'\n    });\n  }\n\n  // If `id` is undefined (during SSR in React < 18), we fall back to rendering a simplified menu item\n  // which does not have access to infortmation about its position or highlighted state.\n  if (id === undefined) {\n    return {\n      getRootProps,\n      disabled: false,\n      focusVisible,\n      highlighted: false,\n      index: -1,\n      totalItemCount: 0,\n      rootRef: handleRef\n    };\n  }\n  return {\n    getRootProps,\n    disabled,\n    focusVisible,\n    highlighted,\n    index,\n    totalItemCount,\n    rootRef: handleRef\n  };\n}", "map": {"version": 3, "names": ["_extends", "React", "unstable_useId", "useId", "unstable_useForkRef", "useForkRef", "useButton", "useListItem", "DropdownActionTypes", "DropdownContext", "combineHooksSlotProps", "useCompoundItem", "extractEventHandlers", "idGenerator", "existingKeys", "concat", "size", "FALLBACK_MENU_CONTEXT", "dispatch", "popupId", "registerPopup", "registerTrigger", "state", "open", "changeReason", "triggerElement", "useMenuItem", "params", "_React$useContext", "disabled", "id", "idParam", "rootRef", "externalRef", "label", "disableFocusOnHover", "itemRef", "useRef", "itemMetadata", "useMemo", "ref", "useContext", "getRootProps", "getListRootProps", "highlighted", "item", "handlePointerOverEvents", "index", "totalItemCount", "getButtonProps", "focusVisible", "buttonRefHandler", "focusableWhenDisabled", "handleRef", "useDebugValue", "createHandleClick", "otherHandlers", "event", "_otherHandlers$onClic", "onClick", "call", "defaultMuiPrevented", "type", "close", "getOwnHandlers", "arguments", "length", "undefined", "externalProps", "externalEventHandlers", "getCombinedRootProps", "role"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/node_modules/@mui/base/useMenuItem/useMenuItem.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { unstable_useId as useId, unstable_useForkRef as useForkRef } from '@mui/utils';\nimport { useButton } from '../useButton';\nimport { useListItem } from '../useList';\nimport { DropdownActionTypes } from '../useDropdown';\nimport { DropdownContext } from '../useDropdown/DropdownContext';\nimport { combineHooksSlotProps } from '../utils/combineHooksSlotProps';\nimport { useCompoundItem } from '../useCompound';\nimport { extractEventHandlers } from '../utils/extractEventHandlers';\nfunction idGenerator(existingKeys) {\n  return `menu-item-${existingKeys.size}`;\n}\nconst FALLBACK_MENU_CONTEXT = {\n  dispatch: () => {},\n  popupId: '',\n  registerPopup: () => {},\n  registerTrigger: () => {},\n  state: {\n    open: true,\n    changeReason: null\n  },\n  triggerElement: null\n};\n\n/**\n *\n * Demos:\n *\n * - [Menu](https://mui.com/base-ui/react-menu/#hooks)\n *\n * API:\n *\n * - [useMenuItem API](https://mui.com/base-ui/react-menu/hooks-api/#use-menu-item)\n */\nexport function useMenuItem(params) {\n  var _React$useContext;\n  const {\n    disabled = false,\n    id: idParam,\n    rootRef: externalRef,\n    label,\n    disableFocusOnHover = false\n  } = params;\n  const id = useId(idParam);\n  const itemRef = React.useRef(null);\n  const itemMetadata = React.useMemo(() => ({\n    disabled,\n    id: id != null ? id : '',\n    label,\n    ref: itemRef\n  }), [disabled, id, label]);\n  const {\n    dispatch\n  } = (_React$useContext = React.useContext(DropdownContext)) != null ? _React$useContext : FALLBACK_MENU_CONTEXT;\n  const {\n    getRootProps: getListRootProps,\n    highlighted\n  } = useListItem({\n    item: id,\n    handlePointerOverEvents: !disableFocusOnHover\n  });\n  const {\n    index,\n    totalItemCount\n  } = useCompoundItem(id != null ? id : idGenerator, itemMetadata);\n  const {\n    getRootProps: getButtonProps,\n    focusVisible,\n    rootRef: buttonRefHandler\n  } = useButton({\n    disabled,\n    focusableWhenDisabled: true\n  });\n  const handleRef = useForkRef(buttonRefHandler, externalRef, itemRef);\n  React.useDebugValue({\n    id,\n    highlighted,\n    disabled,\n    label\n  });\n  const createHandleClick = otherHandlers => event => {\n    var _otherHandlers$onClic;\n    (_otherHandlers$onClic = otherHandlers.onClick) == null || _otherHandlers$onClic.call(otherHandlers, event);\n    if (event.defaultMuiPrevented) {\n      return;\n    }\n    dispatch({\n      type: DropdownActionTypes.close,\n      event\n    });\n  };\n  const getOwnHandlers = (otherHandlers = {}) => _extends({}, otherHandlers, {\n    onClick: createHandleClick(otherHandlers)\n  });\n  function getRootProps(externalProps = {}) {\n    const externalEventHandlers = extractEventHandlers(externalProps);\n    const getCombinedRootProps = combineHooksSlotProps(getOwnHandlers, combineHooksSlotProps(getButtonProps, getListRootProps));\n    return _extends({}, externalProps, externalEventHandlers, getCombinedRootProps(externalEventHandlers), {\n      id,\n      ref: handleRef,\n      role: 'menuitem'\n    });\n  }\n\n  // If `id` is undefined (during SSR in React < 18), we fall back to rendering a simplified menu item\n  // which does not have access to infortmation about its position or highlighted state.\n  if (id === undefined) {\n    return {\n      getRootProps,\n      disabled: false,\n      focusVisible,\n      highlighted: false,\n      index: -1,\n      totalItemCount: 0,\n      rootRef: handleRef\n    };\n  }\n  return {\n    getRootProps,\n    disabled,\n    focusVisible,\n    highlighted,\n    index,\n    totalItemCount,\n    rootRef: handleRef\n  };\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,cAAc,IAAIC,KAAK,EAAEC,mBAAmB,IAAIC,UAAU,QAAQ,YAAY;AACvF,SAASC,SAAS,QAAQ,cAAc;AACxC,SAASC,WAAW,QAAQ,YAAY;AACxC,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,qBAAqB,QAAQ,gCAAgC;AACtE,SAASC,eAAe,QAAQ,gBAAgB;AAChD,SAASC,oBAAoB,QAAQ,+BAA+B;AACpE,SAASC,WAAWA,CAACC,YAAY,EAAE;EACjC,oBAAAC,MAAA,CAAoBD,YAAY,CAACE,IAAI;AACvC;AACA,MAAMC,qBAAqB,GAAG;EAC5BC,QAAQ,EAAEA,CAAA,KAAM,CAAC,CAAC;EAClBC,OAAO,EAAE,EAAE;EACXC,aAAa,EAAEA,CAAA,KAAM,CAAC,CAAC;EACvBC,eAAe,EAAEA,CAAA,KAAM,CAAC,CAAC;EACzBC,KAAK,EAAE;IACLC,IAAI,EAAE,IAAI;IACVC,YAAY,EAAE;EAChB,CAAC;EACDC,cAAc,EAAE;AAClB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,WAAWA,CAACC,MAAM,EAAE;EAClC,IAAIC,iBAAiB;EACrB,MAAM;IACJC,QAAQ,GAAG,KAAK;IAChBC,EAAE,EAAEC,OAAO;IACXC,OAAO,EAAEC,WAAW;IACpBC,KAAK;IACLC,mBAAmB,GAAG;EACxB,CAAC,GAAGR,MAAM;EACV,MAAMG,EAAE,GAAG3B,KAAK,CAAC4B,OAAO,CAAC;EACzB,MAAMK,OAAO,GAAGnC,KAAK,CAACoC,MAAM,CAAC,IAAI,CAAC;EAClC,MAAMC,YAAY,GAAGrC,KAAK,CAACsC,OAAO,CAAC,OAAO;IACxCV,QAAQ;IACRC,EAAE,EAAEA,EAAE,IAAI,IAAI,GAAGA,EAAE,GAAG,EAAE;IACxBI,KAAK;IACLM,GAAG,EAAEJ;EACP,CAAC,CAAC,EAAE,CAACP,QAAQ,EAAEC,EAAE,EAAEI,KAAK,CAAC,CAAC;EAC1B,MAAM;IACJhB;EACF,CAAC,GAAG,CAACU,iBAAiB,GAAG3B,KAAK,CAACwC,UAAU,CAAChC,eAAe,CAAC,KAAK,IAAI,GAAGmB,iBAAiB,GAAGX,qBAAqB;EAC/G,MAAM;IACJyB,YAAY,EAAEC,gBAAgB;IAC9BC;EACF,CAAC,GAAGrC,WAAW,CAAC;IACdsC,IAAI,EAAEf,EAAE;IACRgB,uBAAuB,EAAE,CAACX;EAC5B,CAAC,CAAC;EACF,MAAM;IACJY,KAAK;IACLC;EACF,CAAC,GAAGrC,eAAe,CAACmB,EAAE,IAAI,IAAI,GAAGA,EAAE,GAAGjB,WAAW,EAAEyB,YAAY,CAAC;EAChE,MAAM;IACJI,YAAY,EAAEO,cAAc;IAC5BC,YAAY;IACZlB,OAAO,EAAEmB;EACX,CAAC,GAAG7C,SAAS,CAAC;IACZuB,QAAQ;IACRuB,qBAAqB,EAAE;EACzB,CAAC,CAAC;EACF,MAAMC,SAAS,GAAGhD,UAAU,CAAC8C,gBAAgB,EAAElB,WAAW,EAAEG,OAAO,CAAC;EACpEnC,KAAK,CAACqD,aAAa,CAAC;IAClBxB,EAAE;IACFc,WAAW;IACXf,QAAQ;IACRK;EACF,CAAC,CAAC;EACF,MAAMqB,iBAAiB,GAAGC,aAAa,IAAIC,KAAK,IAAI;IAClD,IAAIC,qBAAqB;IACzB,CAACA,qBAAqB,GAAGF,aAAa,CAACG,OAAO,KAAK,IAAI,IAAID,qBAAqB,CAACE,IAAI,CAACJ,aAAa,EAAEC,KAAK,CAAC;IAC3G,IAAIA,KAAK,CAACI,mBAAmB,EAAE;MAC7B;IACF;IACA3C,QAAQ,CAAC;MACP4C,IAAI,EAAEtD,mBAAmB,CAACuD,KAAK;MAC/BN;IACF,CAAC,CAAC;EACJ,CAAC;EACD,MAAMO,cAAc,GAAG,SAAAA,CAAA;IAAA,IAACR,aAAa,GAAAS,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;IAAA,OAAKjE,QAAQ,CAAC,CAAC,CAAC,EAAEwD,aAAa,EAAE;MACzEG,OAAO,EAAEJ,iBAAiB,CAACC,aAAa;IAC1C,CAAC,CAAC;EAAA;EACF,SAASd,YAAYA,CAAA,EAAqB;IAAA,IAApB0B,aAAa,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;IACtC,MAAMI,qBAAqB,GAAGzD,oBAAoB,CAACwD,aAAa,CAAC;IACjE,MAAME,oBAAoB,GAAG5D,qBAAqB,CAACsD,cAAc,EAAEtD,qBAAqB,CAACuC,cAAc,EAAEN,gBAAgB,CAAC,CAAC;IAC3H,OAAO3C,QAAQ,CAAC,CAAC,CAAC,EAAEoE,aAAa,EAAEC,qBAAqB,EAAEC,oBAAoB,CAACD,qBAAqB,CAAC,EAAE;MACrGvC,EAAE;MACFU,GAAG,EAAEa,SAAS;MACdkB,IAAI,EAAE;IACR,CAAC,CAAC;EACJ;;EAEA;EACA;EACA,IAAIzC,EAAE,KAAKqC,SAAS,EAAE;IACpB,OAAO;MACLzB,YAAY;MACZb,QAAQ,EAAE,KAAK;MACfqB,YAAY;MACZN,WAAW,EAAE,KAAK;MAClBG,KAAK,EAAE,CAAC,CAAC;MACTC,cAAc,EAAE,CAAC;MACjBhB,OAAO,EAAEqB;IACX,CAAC;EACH;EACA,OAAO;IACLX,YAAY;IACZb,QAAQ;IACRqB,YAAY;IACZN,WAAW;IACXG,KAAK;IACLC,cAAc;IACdhB,OAAO,EAAEqB;EACX,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}