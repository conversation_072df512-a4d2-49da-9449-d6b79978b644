{"ast": null, "code": "'use strict';\n\nvar $BigInt = typeof BigInt !== 'undefined' && BigInt;\n\n/** @type {import('.')} */\nmodule.exports = function hasNativeBigInts() {\n  return typeof $BigInt === 'function' && typeof BigInt === 'function' && typeof $BigInt(42) === 'bigint' // eslint-disable-line no-magic-numbers\n  && typeof BigInt(42) === 'bigint'; // eslint-disable-line no-magic-numbers\n};", "map": {"version": 3, "names": ["$BigInt", "BigInt", "module", "exports", "hasNativeBigInts"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/node_modules/has-bigints/index.js"], "sourcesContent": ["'use strict';\n\nvar $BigInt = typeof BigInt !== 'undefined' && BigInt;\n\n/** @type {import('.')} */\nmodule.exports = function hasNativeBigInts() {\n\treturn typeof $BigInt === 'function'\n\t\t&& typeof BigInt === 'function'\n\t\t&& typeof $BigInt(42) === 'bigint' // eslint-disable-line no-magic-numbers\n\t\t&& typeof BigInt(42) === 'bigint'; // eslint-disable-line no-magic-numbers\n};\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,OAAO,GAAG,OAAOC,MAAM,KAAK,WAAW,IAAIA,MAAM;;AAErD;AACAC,MAAM,CAACC,OAAO,GAAG,SAASC,gBAAgBA,CAAA,EAAG;EAC5C,OAAO,OAAOJ,OAAO,KAAK,UAAU,IAChC,OAAOC,MAAM,KAAK,UAAU,IAC5B,OAAOD,OAAO,CAAC,EAAE,CAAC,KAAK,QAAQ,CAAC;EAAA,GAChC,OAAOC,MAAM,CAAC,EAAE,CAAC,KAAK,QAAQ,CAAC,CAAC;AACrC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}