{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NabiBaig\\\\Applications\\\\aqib-gmb-social-frontend\\\\src\\\\components\\\\leftMenu\\\\leftMenu.component.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from \"react\";\nimport { styled, useTheme } from \"@mui/material/styles\";\nimport Box from \"@mui/material/Box\";\nimport MuiDrawer from \"@mui/material/Drawer\";\nimport MuiAppBar from \"@mui/material/AppBar\";\nimport List from \"@mui/material/List\";\nimport CssBaseline from \"@mui/material/CssBaseline\";\nimport IconButton from \"@mui/material/IconButton\";\nimport ChevronLeftIcon from \"@mui/icons-material/ChevronLeft\";\nimport ChevronRightIcon from \"@mui/icons-material/ChevronRight\";\nimport MenuListItemNestedComponent from \"../menuListItemNested/menuListItemNested.component\";\nimport DashboardCustomizeRoundedIcon from \"@mui/icons-material/DashboardCustomizeRounded\";\nimport useMediaQuery from \"@mui/material/useMediaQuery\";\nimport ManageAccountsOutlinedIcon from \"@mui/icons-material/ManageAccountsOutlined\";\nimport PersonAddOutlinedIcon from \"@mui/icons-material/PersonAddOutlined\";\nimport ListItemButton from \"@mui/material/ListItemButton\";\nimport ListItemIcon from \"@mui/material/ListItemIcon\";\nimport ListItemText from \"@mui/material/ListItemText\";\nimport { Tooltip } from \"@mui/material\";\n\n//Icons\n\nimport { logOut } from \"../../actions/auth.actions\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport HeaderComponent from \"../header/header.component\";\nimport LogoutOutlinedIcon from \"@mui/icons-material/LogoutOutlined\";\nimport AdsClickOutlinedIcon from \"@mui/icons-material/AdsClickOutlined\";\nimport BorderColorOutlinedIcon from \"@mui/icons-material/BorderColorOutlined\";\nimport ChatOutlinedIcon from \"@mui/icons-material/ChatOutlined\";\nimport QuestionAnswerRoundedIcon from \"@mui/icons-material/QuestionAnswerRounded\";\nimport RateReviewIcon from \"@mui/icons-material/RateReview\";\nimport GroupIcon from \"@mui/icons-material/Group\";\nimport AdminPanelSettingsIcon from \"@mui/icons-material/AdminPanelSettings\";\nimport BusinessCenterIcon from \"@mui/icons-material/BusinessCenter\";\nimport StoreIcon from \"@mui/icons-material/Store\";\nimport PublishIcon from \"@mui/icons-material/Publish\";\nimport PostAddIcon from \"@mui/icons-material/PostAdd\";\nimport AnalyticsIcon from \"@mui/icons-material/Analytics\";\nimport GridOnIcon from \"@mui/icons-material/GridOn\";\nimport SettingsIcon from \"@mui/icons-material/Settings\";\nimport CloudUploadIcon from \"@mui/icons-material/CloudUpload\";\nimport { openMenu, toggleMenu } from \"../../actions/userPreferences.actions\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst drawerWidth = 340;\nconst openedMixin = theme => ({\n  width: drawerWidth,\n  transition: theme.transitions.create(\"width\", {\n    easing: theme.transitions.easing.sharp,\n    duration: theme.transitions.duration.enteringScreen\n  }),\n  overflowX: \"hidden\"\n});\nconst closedMixin = theme => ({\n  transition: theme.transitions.create(\"width\", {\n    easing: theme.transitions.easing.sharp,\n    duration: theme.transitions.duration.leavingScreen\n  }),\n  overflowX: \"hidden\",\n  width: `calc(${theme.spacing(7)} + 1px)`,\n  [theme.breakpoints.up(\"sm\")]: {\n    width: `calc(${theme.spacing(8)} + 1px)`\n  }\n});\nconst DrawerHeader = styled(\"div\")(({\n  theme\n}) => ({\n  display: \"flex\",\n  alignItems: \"center\",\n  justifyContent: \"flex-end\",\n  padding: theme.spacing(0, 1),\n  // necessary for content to be below app bar\n  ...theme.mixins.toolbar\n}));\n_c = DrawerHeader;\nconst AppBar = styled(MuiAppBar, {\n  shouldForwardProp: prop => prop !== \"open\"\n})(({\n  theme\n}) => ({\n  // zIndex: theme.zIndex.drawer + 1,\n  transition: theme.transitions.create([\"width\", \"margin\"], {\n    easing: theme.transitions.easing.sharp,\n    duration: theme.transitions.duration.leavingScreen\n  }),\n  variants: [{\n    props: ({\n      open\n    }) => open,\n    style: {\n      marginLeft: drawerWidth,\n      width: `calc(100% - ${drawerWidth}px)`,\n      transition: theme.transitions.create([\"width\", \"margin\"], {\n        easing: theme.transitions.easing.sharp,\n        duration: theme.transitions.duration.enteringScreen\n      })\n    }\n  }]\n}));\nconst Drawer = styled(MuiDrawer, {\n  shouldForwardProp: prop => prop !== \"open\"\n})(({\n  theme\n}) => ({\n  width: drawerWidth,\n  flexShrink: 0,\n  whiteSpace: \"nowrap\",\n  boxSizing: \"border-box\",\n  variants: [{\n    props: ({\n      open\n    }) => open,\n    style: {\n      ...openedMixin(theme),\n      \"& .MuiDrawer-paper\": openedMixin(theme)\n    }\n  }, {\n    props: ({\n      open\n    }) => !open,\n    style: {\n      ...closedMixin(theme),\n      \"& .MuiDrawer-paper\": closedMixin(theme)\n    }\n  }]\n}));\n_c2 = Drawer;\nconst LeftMenuComponent = ({\n  children\n}) => {\n  _s();\n  const dispatch = useDispatch();\n  const logoutUser = () => dispatch(logOut());\n  const openLeftMenu = show => dispatch(openMenu(show));\n  const setOpenMenuId = toggleMenuId => dispatch(toggleMenu(toggleMenuId));\n  const {\n    userInfo,\n    rbAccess\n  } = useSelector(state => state.authReducer);\n  const {\n    menuOpened,\n    toggledMenuId\n  } = useSelector(state => state.userPreferencesReducer);\n  const isMobile = useMediaQuery(\"(max-width:600px)\");\n  const theme = useTheme();\n  const handleToggle = id => {\n    const itemData = toggledMenuId === id ? null : id;\n    setOpenMenuId(itemData);\n  };\n  useEffect(() => {\n    console.log(\"userInfo: \", userInfo);\n    console.log(\"Roles Based Access: \", rbAccess);\n    if (isMobile && menuOpened) {\n      openLeftMenu(false);\n    }\n  }, []);\n  const handleMenuItemClick = isOpen => {};\n  const FONTSIZE = \"small\";\n  const DashboardRoutes = {\n    id: \"home\",\n    title: \"Dashboard\",\n    navigateTo: \"/home\",\n    icon: /*#__PURE__*/_jsxDEV(DashboardCustomizeRoundedIcon, {\n      fontSize: FONTSIZE\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 179,\n      columnNumber: 11\n    }, this),\n    open: menuOpened,\n    isAccessible: true\n  };\n  const AnalyticsRoutes = {\n    id: \"analytics\",\n    title: \"Analytics\",\n    navigateTo: \"/analytics\",\n    icon: /*#__PURE__*/_jsxDEV(AnalyticsIcon, {\n      fontSize: FONTSIZE\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 188,\n      columnNumber: 11\n    }, this),\n    open: menuOpened,\n    isAccessible: true\n  };\n  const UserManagementRoutes = {\n    id: \"user-management\",\n    title: \"User Management\",\n    navigateTo: \"\",\n    icon: /*#__PURE__*/_jsxDEV(PersonAddOutlinedIcon, {\n      fontSize: FONTSIZE\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 197,\n      columnNumber: 11\n    }, this),\n    open: menuOpened,\n    isAccessible: Boolean(rbAccess && rbAccess.UserManagement),\n    nested: [{\n      id: \"\",\n      title: \"List Users\",\n      navigateTo: \"/user-management/users\",\n      open: menuOpened,\n      isAccessible: Boolean(rbAccess && rbAccess.UserCreate),\n      icon: /*#__PURE__*/_jsxDEV(GroupIcon, {\n        fontSize: FONTSIZE\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 15\n      }, this)\n    }]\n  };\n  const RoleManagementRoutes = {\n    id: \"roles-management\",\n    title: \"Role Management\",\n    navigateTo: \"\",\n    icon: /*#__PURE__*/_jsxDEV(ManageAccountsOutlinedIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 216,\n      columnNumber: 11\n    }, this),\n    open: menuOpened,\n    isAccessible: Boolean(rbAccess && rbAccess.RoleManagement),\n    nested: [{\n      id: \"\",\n      title: \"Manage Roles\",\n      navigateTo: \"/roles-management/roles\",\n      open: menuOpened,\n      isAccessible: Boolean(rbAccess && rbAccess.RoleManagement),\n      icon: /*#__PURE__*/_jsxDEV(AdminPanelSettingsIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 226,\n        columnNumber: 15\n      }, this)\n    }]\n  };\n  const BusinessManagementRoutes = {\n    id: \"business-management\",\n    title: \"Business Management\",\n    navigateTo: \"\",\n    icon: /*#__PURE__*/_jsxDEV(AdsClickOutlinedIcon, {\n      fontSize: FONTSIZE\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 235,\n      columnNumber: 11\n    }, this),\n    open: menuOpened,\n    isAccessible: Boolean(rbAccess && rbAccess.BusinessManagement),\n    nested: [{\n      id: \"\",\n      title: \"Manage Business\",\n      navigateTo: \"/business-management/manage-business\",\n      open: menuOpened,\n      isAccessible: Boolean(rbAccess && rbAccess.BusinessManagement),\n      icon: /*#__PURE__*/_jsxDEV(BusinessCenterIcon, {\n        fontSize: FONTSIZE\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 245,\n        columnNumber: 15\n      }, this)\n    }, {\n      id: \"\",\n      title: \"Local Business\",\n      navigateTo: \"/business-management/local-business\",\n      open: menuOpened,\n      isAccessible: Boolean(rbAccess && rbAccess.BusinessManagement),\n      icon: /*#__PURE__*/_jsxDEV(StoreIcon, {\n        fontSize: FONTSIZE\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 253,\n        columnNumber: 15\n      }, this)\n    }\n    // {\n    //   title: \"Business Summary\",\n    //   navigateTo: \"/business-management/business-summary\",\n    //   open: open,\n    //   isAccessible: Boolean(rbAccess && rbAccess.BusinessManagement),\n    // },\n    ]\n  };\n  const ReviewManagementRoutes = {\n    id: \"review-management\",\n    title: \"Review Management\",\n    navigateTo: \"\",\n    icon: /*#__PURE__*/_jsxDEV(BorderColorOutlinedIcon, {\n      fontSize: FONTSIZE\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 268,\n      columnNumber: 11\n    }, this),\n    open: menuOpened,\n    isAccessible: Boolean(rbAccess && rbAccess.ReviewsManagement),\n    nested: [{\n      id: \"\",\n      title: \"List of reviews for business\",\n      navigateTo: \"/review-management/manage-reviews\",\n      open: menuOpened,\n      isAccessible: true,\n      icon: /*#__PURE__*/_jsxDEV(RateReviewIcon, {\n        fontSize: FONTSIZE\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 278,\n        columnNumber: 15\n      }, this)\n    }, {\n      id: \"\",\n      title: \"Review Settings\",\n      navigateTo: \"/review-management/review-settings\",\n      open: menuOpened,\n      isAccessible: true,\n      icon: /*#__PURE__*/_jsxDEV(SettingsIcon, {\n        fontSize: FONTSIZE\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 286,\n        columnNumber: 15\n      }, this)\n    }]\n  };\n  const QandAManagementRoutes = {\n    id: \"q-and-a\",\n    title: \"Q&A\",\n    navigateTo: \"/q-and-a\",\n    icon: /*#__PURE__*/_jsxDEV(QuestionAnswerRoundedIcon, {\n      fontSize: FONTSIZE\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 295,\n      columnNumber: 11\n    }, this),\n    open: menuOpened,\n    isAccessible: Boolean(rbAccess && rbAccess.QandAManagement)\n  };\n  const PostsManagementRoutes = {\n    id: \"post-management\",\n    title: \"Posts Management\",\n    navigateTo: \"\",\n    icon: /*#__PURE__*/_jsxDEV(ChatOutlinedIcon, {\n      fontSize: FONTSIZE\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 304,\n      columnNumber: 11\n    }, this),\n    open: menuOpened,\n    isAccessible: Boolean(rbAccess && rbAccess.PostsManagement),\n    nested: [{\n      id: \"\",\n      title: \"Bulk Post Updates\",\n      navigateTo: \"/post-management/posts\",\n      open: menuOpened,\n      isAccessible: true,\n      icon: /*#__PURE__*/_jsxDEV(PublishIcon, {\n        fontSize: FONTSIZE\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 314,\n        columnNumber: 15\n      }, this)\n    },\n    // {\n    //   title: \"Event Updates\",\n    //   navigateTo: \"\",\n    //   open: open,\n    //   isAccessible: true,\n    // },\n    {\n      id: \"\",\n      title: \"Create Posts\",\n      navigateTo: \"/post-management/create-social-post\",\n      open: menuOpened,\n      isAccessible: Boolean(rbAccess && rbAccess.PostsCreate),\n      icon: /*#__PURE__*/_jsxDEV(PostAddIcon, {\n        fontSize: FONTSIZE\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 328,\n        columnNumber: 15\n      }, this)\n    }\n    // {\n    //   id: \"\",\n    //   title: \"Page Not Found\",\n    //   navigateTo: \"/page-not-found\",\n    //   open: open,\n    //   isAccessible: true,\n    // },\n    ]\n  };\n  const GeoGridRoutes = {\n    id: \"geo-grid\",\n    title: \"Google Geo Grid\",\n    navigateTo: \"/geo-grid\",\n    icon: /*#__PURE__*/_jsxDEV(GridOnIcon, {\n      fontSize: FONTSIZE\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 344,\n      columnNumber: 11\n    }, this),\n    open: menuOpened,\n    // Using LocationManagement permission as it's the closest related permission\n    // TODO: Consider adding specific GeoGridManagement permission in future\n    isAccessible: Boolean(rbAccess && rbAccess.LocationManagement)\n  };\n  const ManageAssetsRoutes = {\n    id: \"manage-assets\",\n    title: \"Manage Assets\",\n    navigateTo: \"/manage-assets\",\n    icon: /*#__PURE__*/_jsxDEV(CloudUploadIcon, {\n      fontSize: FONTSIZE\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 355,\n      columnNumber: 11\n    }, this),\n    open: menuOpened,\n    // Using BusinessManagement permission for asset management\n    isAccessible: Boolean(rbAccess && rbAccess.BusinessManagement)\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: \"flex\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(CssBaseline, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 364,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(HeaderComponent, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 391,\n        columnNumber: 9\n      }, this), rbAccess && /*#__PURE__*/_jsxDEV(Drawer, {\n        variant: \"permanent\",\n        open: menuOpened,\n        style: isMobile && menuOpened ? {\n          position: \"absolute\"\n        } : {\n          position: \"unset\"\n        }\n        // onMouseEnter={!isMobile ? () => setOpen(true) : undefined}\n        // onMouseLeave={!isMobile ? () => setOpen(false) : undefined}\n        ,\n        children: [/*#__PURE__*/_jsxDEV(DrawerHeader, {\n          className: \"navLogoPart\",\n          children: [menuOpened ? /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              textAlign: \"center\",\n              my: 2,\n              py: 1\n            },\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              alt: \"MyLocoBiz - Login\",\n              className: \"widthLogo navFullLogo\",\n              src: require(\"../../assets/common/Logo.png\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 410,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 409,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {}, void 0, false)\n          // <Box className=\"navIcon\">\n          //   <img\n          //     alt=\"MyLocoBiz - Login\"\n          //     className=\" \"\n          //     src={require(\"../../assets/common/LogoIcon.png\")}\n          //   />\n          // </Box>\n          , isMobile ? /*#__PURE__*/_jsxDEV(IconButton, {\n            onClick: () => openLeftMenu(!menuOpened),\n            children: menuOpened ? /*#__PURE__*/_jsxDEV(ChevronLeftIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 428,\n              columnNumber: 33\n            }, this) : /*#__PURE__*/_jsxDEV(ChevronRightIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 428,\n              columnNumber: 55\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 427,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(IconButton, {\n            onClick: () => openLeftMenu(!menuOpened),\n            children: menuOpened ? /*#__PURE__*/_jsxDEV(ChevronLeftIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 432,\n              columnNumber: 33\n            }, this) : /*#__PURE__*/_jsxDEV(ChevronRightIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 432,\n              columnNumber: 55\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 431,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 407,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(List, {\n          sx: {\n            width: \"100%\",\n            height: \"100%\",\n            maxWidth: 360,\n            fontWeight: \"600\"\n          },\n          component: \"nav\",\n          \"aria-labelledby\": \"nested-list-subheader\",\n          children: [/*#__PURE__*/_jsxDEV(MenuListItemNestedComponent, {\n            props: {\n              ...DashboardRoutes,\n              isToggled: toggledMenuId === DashboardRoutes.id,\n              onToggle: () => handleToggle(DashboardRoutes.id)\n            }\n          }, \"home\", false, {\n            fileName: _jsxFileName,\n            lineNumber: 449,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(MenuListItemNestedComponent, {\n            props: {\n              ...AnalyticsRoutes,\n              isToggled: toggledMenuId === AnalyticsRoutes.id,\n              onToggle: () => handleToggle(AnalyticsRoutes.id)\n            }\n          }, \"analytics\", false, {\n            fileName: _jsxFileName,\n            lineNumber: 458,\n            columnNumber: 15\n          }, this), Boolean(rbAccess.UserManagement) && /*#__PURE__*/_jsxDEV(MenuListItemNestedComponent, {\n            props: {\n              ...UserManagementRoutes,\n              isToggled: toggledMenuId === UserManagementRoutes.id,\n              onToggle: () => handleToggle(UserManagementRoutes.id)\n            }\n          }, \"User-Management-Routes\", false, {\n            fileName: _jsxFileName,\n            lineNumber: 468,\n            columnNumber: 17\n          }, this), Boolean(rbAccess.RoleManagement) && /*#__PURE__*/_jsxDEV(MenuListItemNestedComponent, {\n            props: {\n              ...RoleManagementRoutes,\n              isToggled: toggledMenuId === RoleManagementRoutes.id,\n              onToggle: () => handleToggle(RoleManagementRoutes.id)\n            }\n          }, \"Role-Management-Routes\", false, {\n            fileName: _jsxFileName,\n            lineNumber: 479,\n            columnNumber: 17\n          }, this), Boolean(rbAccess.BusinessManagement) && /*#__PURE__*/_jsxDEV(MenuListItemNestedComponent, {\n            props: {\n              ...BusinessManagementRoutes,\n              isToggled: toggledMenuId === BusinessManagementRoutes.id,\n              onToggle: () => handleToggle(BusinessManagementRoutes.id)\n            }\n          }, \"Business-Management-Routes\", false, {\n            fileName: _jsxFileName,\n            lineNumber: 490,\n            columnNumber: 17\n          }, this), Boolean(rbAccess.ReviewsManagement) && /*#__PURE__*/_jsxDEV(MenuListItemNestedComponent, {\n            props: {\n              ...ReviewManagementRoutes,\n              isToggled: toggledMenuId === ReviewManagementRoutes.id,\n              onToggle: () => handleToggle(ReviewManagementRoutes.id)\n            }\n          }, \"Review-Management-Routes\", false, {\n            fileName: _jsxFileName,\n            lineNumber: 501,\n            columnNumber: 17\n          }, this), Boolean(rbAccess.QandAManagement) && /*#__PURE__*/_jsxDEV(MenuListItemNestedComponent, {\n            props: {\n              ...QandAManagementRoutes,\n              isToggled: toggledMenuId === QandAManagementRoutes.id,\n              onToggle: () => handleToggle(QandAManagementRoutes.id)\n            }\n          }, \"QandA-Management-Routes\", false, {\n            fileName: _jsxFileName,\n            lineNumber: 512,\n            columnNumber: 17\n          }, this), Boolean(rbAccess.PostsManagement) && /*#__PURE__*/_jsxDEV(MenuListItemNestedComponent, {\n            props: {\n              ...PostsManagementRoutes,\n              isToggled: toggledMenuId === PostsManagementRoutes.id,\n              onToggle: () => handleToggle(PostsManagementRoutes.id)\n            }\n          }, \"Posts-Management-Routes\", false, {\n            fileName: _jsxFileName,\n            lineNumber: 523,\n            columnNumber: 17\n          }, this), Boolean(rbAccess.BusinessManagement) && /*#__PURE__*/_jsxDEV(MenuListItemNestedComponent, {\n            props: {\n              ...ManageAssetsRoutes,\n              isToggled: toggledMenuId === ManageAssetsRoutes.id,\n              onToggle: () => handleToggle(ManageAssetsRoutes.id)\n            }\n          }, \"manage-assets\", false, {\n            fileName: _jsxFileName,\n            lineNumber: 547,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 439,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(List, {\n          children: /*#__PURE__*/_jsxDEV(ListItemButton, {\n            sx: [{\n              minHeight: 48,\n              px: 2.5\n            }, menuOpened ? {\n              justifyContent: \"initial\"\n            } : {\n              justifyContent: \"center\"\n            }],\n            onClick: () => {\n              openLeftMenu(false);\n              setOpenMenuId(\"\");\n              setTimeout(() => {\n                logoutUser();\n              }, 1000);\n            },\n            children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n              title: !menuOpened ? \"Logout\" : \"\",\n              placement: \"right\",\n              children: /*#__PURE__*/_jsxDEV(ListItemIcon, {\n                sx: {\n                  minWidth: 0,\n                  justifyContent: \"center\",\n                  mr: menuOpened ? 3 : \"auto\"\n                },\n                children: /*#__PURE__*/_jsxDEV(LogoutOutlinedIcon, {\n                  color: menuOpened ? \"error\" : \"inherit\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 588,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 581,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 580,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primaryTypographyProps: {\n                fontWeight: 600\n              },\n              sx: {\n                opacity: menuOpened ? 1 : 0,\n                transition: \"opacity 0.3s\"\n              },\n              primary: \"Logout\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 594,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(_Fragment, {}, void 0, false)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 558,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 557,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 396,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        component: \"main\",\n        sx: {\n          flexGrow: 1,\n          p: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(DrawerHeader, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 609,\n          columnNumber: 11\n        }, this), children]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 608,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 363,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 362,\n    columnNumber: 5\n  }, this);\n};\n_s(LeftMenuComponent, \"0HRN4QXX+RIRMjYUcjp40h+ryKo=\", false, function () {\n  return [useDispatch, useSelector, useSelector, useMediaQuery, useTheme];\n});\n_c3 = LeftMenuComponent;\nexport default LeftMenuComponent;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"DrawerHeader\");\n$RefreshReg$(_c2, \"Drawer\");\n$RefreshReg$(_c3, \"LeftMenuComponent\");", "map": {"version": 3, "names": ["React", "useEffect", "styled", "useTheme", "Box", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "MuiAppBar", "List", "CssBaseline", "IconButton", "ChevronLeftIcon", "ChevronRightIcon", "MenuListItemNestedComponent", "DashboardCustomizeRoundedIcon", "useMediaQuery", "ManageAccountsOutlinedIcon", "PersonAddOutlinedIcon", "ListItemButton", "ListItemIcon", "ListItemText", "<PERSON><PERSON><PERSON>", "logOut", "useDispatch", "useSelector", "HeaderComponent", "LogoutOutlinedIcon", "AdsClickOutlinedIcon", "BorderColorOutlinedIcon", "ChatOutlinedIcon", "QuestionAnswerRoundedIcon", "RateReviewIcon", "GroupIcon", "AdminPanelSettingsIcon", "BusinessCenterIcon", "StoreIcon", "PublishIcon", "PostAddIcon", "AnalyticsIcon", "GridOnIcon", "SettingsIcon", "CloudUploadIcon", "openMenu", "toggleMenu", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "drawerWidth", "openedMixin", "theme", "width", "transition", "transitions", "create", "easing", "sharp", "duration", "enteringScreen", "overflowX", "closedMixin", "leavingScreen", "spacing", "breakpoints", "up", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "display", "alignItems", "justifyContent", "padding", "mixins", "toolbar", "_c", "AppBar", "shouldForwardProp", "prop", "variants", "props", "open", "style", "marginLeft", "Drawer", "flexShrink", "whiteSpace", "boxSizing", "_c2", "LeftMenuComponent", "children", "_s", "dispatch", "logoutUser", "openLeftMenu", "show", "setOpenMenuId", "toggleMenuId", "userInfo", "rbAccess", "state", "authReducer", "menuOpened", "toggledMenuId", "userPreferencesReducer", "isMobile", "handleToggle", "id", "itemData", "console", "log", "handleMenuItemClick", "isOpen", "FONTSIZE", "DashboardRoutes", "title", "navigateTo", "icon", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "isAccessible", "AnalyticsRoutes", "UserManagementRoutes", "Boolean", "UserManagement", "nested", "UserCreate", "RoleManagementRoutes", "RoleManagement", "BusinessManagementRoutes", "BusinessManagement", "ReviewManagementRoutes", "ReviewsManagement", "QandAManagementRoutes", "QandAManagement", "PostsManagementRoutes", "PostsManagement", "PostsCreate", "GeoGridRoutes", "LocationManagement", "ManageAssetsRoutes", "sx", "variant", "position", "className", "textAlign", "my", "py", "alt", "src", "require", "onClick", "height", "max<PERSON><PERSON><PERSON>", "fontWeight", "component", "isToggled", "onToggle", "minHeight", "px", "setTimeout", "placement", "min<PERSON><PERSON><PERSON>", "mr", "color", "primaryTypographyProps", "opacity", "primary", "flexGrow", "p", "_c3", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/src/components/leftMenu/leftMenu.component.tsx"], "sourcesContent": ["import React, { FunctionComponent, ReactNode, useEffect } from \"react\";\r\nimport { styled, useTheme, Theme, CSSObject } from \"@mui/material/styles\";\r\nimport Box from \"@mui/material/Box\";\r\nimport MuiDrawer from \"@mui/material/Drawer\";\r\nimport MuiAppBar, { AppBarProps as MuiAppBarProps } from \"@mui/material/AppBar\";\r\nimport Toolbar from \"@mui/material/Toolbar\";\r\nimport List from \"@mui/material/List\";\r\nimport CssBaseline from \"@mui/material/CssBaseline\";\r\nimport Typography from \"@mui/material/Typography\";\r\nimport Divider from \"@mui/material/Divider\";\r\nimport IconButton from \"@mui/material/IconButton\";\r\nimport MenuIcon from \"@mui/icons-material/Menu\";\r\nimport ChevronLeftIcon from \"@mui/icons-material/ChevronLeft\";\r\nimport Button from \"@mui/material/Button\";\r\nimport Menu from \"@mui/material/Menu\";\r\nimport MenuItem from \"@mui/material/MenuItem\";\r\nimport ChevronRightIcon from \"@mui/icons-material/ChevronRight\";\r\nimport { NestedMenuItems } from \"../../interfaces/nestedMenuItems\";\r\nimport MenuListItemNestedComponent from \"../menuListItemNested/menuListItemNested.component\";\r\nimport DashboardCustomizeRoundedIcon from \"@mui/icons-material/DashboardCustomizeRounded\";\r\nimport useMediaQuery from \"@mui/material/useMediaQuery\";\r\nimport ManageAccountsIcon from \"@mui/icons-material/ManageAccounts\";\r\nimport ManageAccountsOutlinedIcon from \"@mui/icons-material/ManageAccountsOutlined\";\r\nimport PersonAddOutlinedIcon from \"@mui/icons-material/PersonAddOutlined\";\r\nimport ListItemButton from \"@mui/material/ListItemButton\";\r\nimport ListItemIcon from \"@mui/material/ListItemIcon\";\r\nimport ListItemText from \"@mui/material/ListItemText\";\r\nimport { Collapse, Tooltip } from \"@mui/material\";\r\n\r\n//Icons\r\nimport SettingsOutlinedIcon from \"@mui/icons-material/SettingsOutlined\";\r\nimport ArrowForwardIosRoundedIcon from \"@mui/icons-material/ArrowForwardIosRounded\";\r\nimport { ListAltSharp } from \"@mui/icons-material\";\r\nimport { logOut } from \"../../actions/auth.actions\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport HeaderComponent from \"../header/header.component\";\r\nimport LogoutOutlinedIcon from \"@mui/icons-material/LogoutOutlined\";\r\nimport AdsClickOutlinedIcon from \"@mui/icons-material/AdsClickOutlined\";\r\nimport BorderColorOutlinedIcon from \"@mui/icons-material/BorderColorOutlined\";\r\nimport ChatOutlinedIcon from \"@mui/icons-material/ChatOutlined\";\r\nimport MapsUgcRoundedIcon from \"@mui/icons-material/MapsUgcRounded\";\r\nimport QuestionAnswerRoundedIcon from \"@mui/icons-material/QuestionAnswerRounded\";\r\nimport RateReviewIcon from \"@mui/icons-material/RateReview\";\r\nimport GroupIcon from \"@mui/icons-material/Group\";\r\nimport AdminPanelSettingsIcon from \"@mui/icons-material/AdminPanelSettings\";\r\nimport BusinessCenterIcon from \"@mui/icons-material/BusinessCenter\";\r\nimport StoreIcon from \"@mui/icons-material/Store\";\r\nimport PublishIcon from \"@mui/icons-material/Publish\";\r\nimport PostAddIcon from \"@mui/icons-material/PostAdd\";\r\nimport AnalyticsIcon from \"@mui/icons-material/Analytics\";\r\nimport GridOnIcon from \"@mui/icons-material/GridOn\";\r\nimport SettingsIcon from \"@mui/icons-material/Settings\";\r\nimport CloudUploadIcon from \"@mui/icons-material/CloudUpload\";\r\nimport { openMenu, toggleMenu } from \"../../actions/userPreferences.actions\";\r\n\r\nconst drawerWidth = 340;\r\n\r\nconst openedMixin = (theme: Theme): CSSObject => ({\r\n  width: drawerWidth,\r\n  transition: theme.transitions.create(\"width\", {\r\n    easing: theme.transitions.easing.sharp,\r\n    duration: theme.transitions.duration.enteringScreen,\r\n  }),\r\n  overflowX: \"hidden\",\r\n});\r\n\r\nconst closedMixin = (theme: Theme): CSSObject => ({\r\n  transition: theme.transitions.create(\"width\", {\r\n    easing: theme.transitions.easing.sharp,\r\n    duration: theme.transitions.duration.leavingScreen,\r\n  }),\r\n  overflowX: \"hidden\",\r\n  width: `calc(${theme.spacing(7)} + 1px)`,\r\n  [theme.breakpoints.up(\"sm\")]: {\r\n    width: `calc(${theme.spacing(8)} + 1px)`,\r\n  },\r\n});\r\n\r\nconst DrawerHeader = styled(\"div\")(({ theme }) => ({\r\n  display: \"flex\",\r\n  alignItems: \"center\",\r\n  justifyContent: \"flex-end\",\r\n  padding: theme.spacing(0, 1),\r\n  // necessary for content to be below app bar\r\n  ...theme.mixins.toolbar,\r\n}));\r\n\r\ninterface AppBarProps extends MuiAppBarProps {\r\n  open?: boolean;\r\n}\r\n\r\nconst AppBar = styled(MuiAppBar, {\r\n  shouldForwardProp: (prop) => prop !== \"open\",\r\n})<AppBarProps>(({ theme }) => ({\r\n  // zIndex: theme.zIndex.drawer + 1,\r\n  transition: theme.transitions.create([\"width\", \"margin\"], {\r\n    easing: theme.transitions.easing.sharp,\r\n    duration: theme.transitions.duration.leavingScreen,\r\n  }),\r\n  variants: [\r\n    {\r\n      props: ({ open }) => open,\r\n      style: {\r\n        marginLeft: drawerWidth,\r\n        width: `calc(100% - ${drawerWidth}px)`,\r\n        transition: theme.transitions.create([\"width\", \"margin\"], {\r\n          easing: theme.transitions.easing.sharp,\r\n          duration: theme.transitions.duration.enteringScreen,\r\n        }),\r\n      },\r\n    },\r\n  ],\r\n}));\r\n\r\nconst Drawer = styled(MuiDrawer, {\r\n  shouldForwardProp: (prop) => prop !== \"open\",\r\n})(({ theme }) => ({\r\n  width: drawerWidth,\r\n  flexShrink: 0,\r\n  whiteSpace: \"nowrap\",\r\n  boxSizing: \"border-box\",\r\n  variants: [\r\n    {\r\n      props: ({ open }) => open,\r\n      style: {\r\n        ...openedMixin(theme),\r\n        \"& .MuiDrawer-paper\": openedMixin(theme),\r\n      },\r\n    },\r\n    {\r\n      props: ({ open }) => !open,\r\n      style: {\r\n        ...closedMixin(theme),\r\n        \"& .MuiDrawer-paper\": closedMixin(theme),\r\n      },\r\n    },\r\n  ],\r\n}));\r\n\r\ninterface Props {\r\n  children?: ReactNode;\r\n}\r\n\r\nconst LeftMenuComponent: FunctionComponent<Props> = ({ children }) => {\r\n  const dispatch = useDispatch();\r\n  const logoutUser = () => dispatch<any>(logOut());\r\n  const openLeftMenu = (show: boolean) => dispatch<any>(openMenu(show));\r\n  const setOpenMenuId = (toggleMenuId: string | null) =>\r\n    dispatch<any>(toggleMenu(toggleMenuId));\r\n  const { userInfo, rbAccess } = useSelector((state: any) => state.authReducer);\r\n  const { menuOpened, toggledMenuId } = useSelector(\r\n    (state: any) => state.userPreferencesReducer\r\n  );\r\n\r\n  const isMobile = useMediaQuery(\"(max-width:600px)\");\r\n  const theme = useTheme();\r\n\r\n  const handleToggle = (id: string) => {\r\n    const itemData = toggledMenuId === id ? null : id;\r\n    setOpenMenuId(itemData);\r\n  };\r\n\r\n  useEffect(() => {\r\n    console.log(\"userInfo: \", userInfo);\r\n    console.log(\"Roles Based Access: \", rbAccess);\r\n    if (isMobile && menuOpened) {\r\n      openLeftMenu(false);\r\n    }\r\n  }, []);\r\n\r\n  const handleMenuItemClick = (isOpen: boolean) => {};\r\n\r\n  const FONTSIZE = \"small\";\r\n\r\n  const DashboardRoutes: NestedMenuItems = {\r\n    id: \"home\",\r\n    title: \"Dashboard\",\r\n    navigateTo: \"/home\",\r\n    icon: <DashboardCustomizeRoundedIcon fontSize={FONTSIZE} />,\r\n    open: menuOpened,\r\n    isAccessible: true,\r\n  };\r\n\r\n  const AnalyticsRoutes: NestedMenuItems = {\r\n    id: \"analytics\",\r\n    title: \"Analytics\",\r\n    navigateTo: \"/analytics\",\r\n    icon: <AnalyticsIcon fontSize={FONTSIZE} />,\r\n    open: menuOpened,\r\n    isAccessible: true,\r\n  };\r\n\r\n  const UserManagementRoutes: NestedMenuItems = {\r\n    id: \"user-management\",\r\n    title: \"User Management\",\r\n    navigateTo: \"\",\r\n    icon: <PersonAddOutlinedIcon fontSize={FONTSIZE} />,\r\n    open: menuOpened,\r\n    isAccessible: Boolean(rbAccess && rbAccess.UserManagement),\r\n    nested: [\r\n      {\r\n        id: \"\",\r\n        title: \"List Users\",\r\n        navigateTo: \"/user-management/users\",\r\n        open: menuOpened,\r\n        isAccessible: Boolean(rbAccess && rbAccess.UserCreate),\r\n        icon: <GroupIcon fontSize={FONTSIZE} />,\r\n      },\r\n    ],\r\n  };\r\n\r\n  const RoleManagementRoutes: NestedMenuItems = {\r\n    id: \"roles-management\",\r\n    title: \"Role Management\",\r\n    navigateTo: \"\",\r\n    icon: <ManageAccountsOutlinedIcon />,\r\n    open: menuOpened,\r\n    isAccessible: Boolean(rbAccess && rbAccess.RoleManagement),\r\n    nested: [\r\n      {\r\n        id: \"\",\r\n        title: \"Manage Roles\",\r\n        navigateTo: \"/roles-management/roles\",\r\n        open: menuOpened,\r\n        isAccessible: Boolean(rbAccess && rbAccess.RoleManagement),\r\n        icon: <AdminPanelSettingsIcon />,\r\n      },\r\n    ],\r\n  };\r\n\r\n  const BusinessManagementRoutes: NestedMenuItems = {\r\n    id: \"business-management\",\r\n    title: \"Business Management\",\r\n    navigateTo: \"\",\r\n    icon: <AdsClickOutlinedIcon fontSize={FONTSIZE} />,\r\n    open: menuOpened,\r\n    isAccessible: Boolean(rbAccess && rbAccess.BusinessManagement),\r\n    nested: [\r\n      {\r\n        id: \"\",\r\n        title: \"Manage Business\",\r\n        navigateTo: \"/business-management/manage-business\",\r\n        open: menuOpened,\r\n        isAccessible: Boolean(rbAccess && rbAccess.BusinessManagement),\r\n        icon: <BusinessCenterIcon fontSize={FONTSIZE} />,\r\n      },\r\n      {\r\n        id: \"\",\r\n        title: \"Local Business\",\r\n        navigateTo: \"/business-management/local-business\",\r\n        open: menuOpened,\r\n        isAccessible: Boolean(rbAccess && rbAccess.BusinessManagement),\r\n        icon: <StoreIcon fontSize={FONTSIZE} />,\r\n      },\r\n      // {\r\n      //   title: \"Business Summary\",\r\n      //   navigateTo: \"/business-management/business-summary\",\r\n      //   open: open,\r\n      //   isAccessible: Boolean(rbAccess && rbAccess.BusinessManagement),\r\n      // },\r\n    ],\r\n  };\r\n\r\n  const ReviewManagementRoutes: NestedMenuItems = {\r\n    id: \"review-management\",\r\n    title: \"Review Management\",\r\n    navigateTo: \"\",\r\n    icon: <BorderColorOutlinedIcon fontSize={FONTSIZE} />,\r\n    open: menuOpened,\r\n    isAccessible: Boolean(rbAccess && rbAccess.ReviewsManagement),\r\n    nested: [\r\n      {\r\n        id: \"\",\r\n        title: \"List of reviews for business\",\r\n        navigateTo: \"/review-management/manage-reviews\",\r\n        open: menuOpened,\r\n        isAccessible: true,\r\n        icon: <RateReviewIcon fontSize={FONTSIZE} />,\r\n      },\r\n      {\r\n        id: \"\",\r\n        title: \"Review Settings\",\r\n        navigateTo: \"/review-management/review-settings\",\r\n        open: menuOpened,\r\n        isAccessible: true,\r\n        icon: <SettingsIcon fontSize={FONTSIZE} />,\r\n      },\r\n    ],\r\n  };\r\n\r\n  const QandAManagementRoutes: NestedMenuItems = {\r\n    id: \"q-and-a\",\r\n    title: \"Q&A\",\r\n    navigateTo: \"/q-and-a\",\r\n    icon: <QuestionAnswerRoundedIcon fontSize={FONTSIZE} />,\r\n    open: menuOpened,\r\n    isAccessible: Boolean(rbAccess && rbAccess.QandAManagement),\r\n  };\r\n\r\n  const PostsManagementRoutes: NestedMenuItems = {\r\n    id: \"post-management\",\r\n    title: \"Posts Management\",\r\n    navigateTo: \"\",\r\n    icon: <ChatOutlinedIcon fontSize={FONTSIZE} />,\r\n    open: menuOpened,\r\n    isAccessible: Boolean(rbAccess && rbAccess.PostsManagement),\r\n    nested: [\r\n      {\r\n        id: \"\",\r\n        title: \"Bulk Post Updates\",\r\n        navigateTo: \"/post-management/posts\",\r\n        open: menuOpened,\r\n        isAccessible: true,\r\n        icon: <PublishIcon fontSize={FONTSIZE} />,\r\n      },\r\n      // {\r\n      //   title: \"Event Updates\",\r\n      //   navigateTo: \"\",\r\n      //   open: open,\r\n      //   isAccessible: true,\r\n      // },\r\n      {\r\n        id: \"\",\r\n        title: \"Create Posts\",\r\n        navigateTo: \"/post-management/create-social-post\",\r\n        open: menuOpened,\r\n        isAccessible: Boolean(rbAccess && rbAccess.PostsCreate),\r\n        icon: <PostAddIcon fontSize={FONTSIZE} />,\r\n      },\r\n      // {\r\n      //   id: \"\",\r\n      //   title: \"Page Not Found\",\r\n      //   navigateTo: \"/page-not-found\",\r\n      //   open: open,\r\n      //   isAccessible: true,\r\n      // },\r\n    ],\r\n  };\r\n\r\n  const GeoGridRoutes: NestedMenuItems = {\r\n    id: \"geo-grid\",\r\n    title: \"Google Geo Grid\",\r\n    navigateTo: \"/geo-grid\",\r\n    icon: <GridOnIcon fontSize={FONTSIZE} />,\r\n    open: menuOpened,\r\n    // Using LocationManagement permission as it's the closest related permission\r\n    // TODO: Consider adding specific GeoGridManagement permission in future\r\n    isAccessible: Boolean(rbAccess && rbAccess.LocationManagement),\r\n  };\r\n\r\n  const ManageAssetsRoutes: NestedMenuItems = {\r\n    id: \"manage-assets\",\r\n    title: \"Manage Assets\",\r\n    navigateTo: \"/manage-assets\",\r\n    icon: <CloudUploadIcon fontSize={FONTSIZE} />,\r\n    open: menuOpened,\r\n    // Using BusinessManagement permission for asset management\r\n    isAccessible: Boolean(rbAccess && rbAccess.BusinessManagement),\r\n  };\r\n\r\n  return (\r\n    <div>\r\n      <Box sx={{ display: \"flex\" }}>\r\n        <CssBaseline />\r\n        {/* <AppBar\r\n          position=\"fixed\"\r\n          elevation={0}\r\n          sx={{\r\n            zIndex: (theme) => theme.zIndex.drawer + 1,\r\n            backgroundColor: \"transparent\",\r\n            boxShadow: \"none\",\r\n            backdropFilter: \"blur(0px)\", // optional: nice blur effect behind\r\n          }}\r\n          open={open}\r\n        > */}\r\n        {/* <Toolbar className=\"headerMenu\"> */}\r\n        {/* <IconButton\r\n              color=\"inherit\"\r\n              aria-label=\"open drawer\"\r\n              onClick={handleDrawerOpen}\r\n              edge=\"start\"\r\n              sx={[\r\n                {\r\n                  marginRight: 5,\r\n                },\r\n                open && { display: \"none\" },\r\n              ]}\r\n            >\r\n              <ArrowForwardIosRoundedIcon />\r\n            </IconButton> */}\r\n        <HeaderComponent />\r\n        {/* </Toolbar> */}\r\n        {/* </AppBar> */}\r\n\r\n        {rbAccess && (\r\n          <Drawer\r\n            variant=\"permanent\"\r\n            open={menuOpened}\r\n            style={\r\n              isMobile && menuOpened\r\n                ? { position: \"absolute\" }\r\n                : { position: \"unset\" }\r\n            }\r\n            // onMouseEnter={!isMobile ? () => setOpen(true) : undefined}\r\n            // onMouseLeave={!isMobile ? () => setOpen(false) : undefined}\r\n          >\r\n            <DrawerHeader className=\"navLogoPart\">\r\n              {menuOpened ? (\r\n                <Box sx={{ textAlign: \"center\", my: 2, py: 1 }}>\r\n                  <img\r\n                    alt=\"MyLocoBiz - Login\"\r\n                    className=\"widthLogo navFullLogo\"\r\n                    src={require(\"../../assets/common/Logo.png\")}\r\n                  />\r\n                </Box>\r\n              ) : (\r\n                <></>\r\n                // <Box className=\"navIcon\">\r\n                //   <img\r\n                //     alt=\"MyLocoBiz - Login\"\r\n                //     className=\" \"\r\n                //     src={require(\"../../assets/common/LogoIcon.png\")}\r\n                //   />\r\n                // </Box>\r\n              )}\r\n              {isMobile ? (\r\n                <IconButton onClick={() => openLeftMenu(!menuOpened)}>\r\n                  {menuOpened ? <ChevronLeftIcon /> : <ChevronRightIcon />}\r\n                </IconButton>\r\n              ) : (\r\n                <IconButton onClick={() => openLeftMenu(!menuOpened)}>\r\n                  {menuOpened ? <ChevronLeftIcon /> : <ChevronRightIcon />}\r\n                </IconButton>\r\n              )}\r\n            </DrawerHeader>\r\n\r\n            {/* <Divider /> */}\r\n\r\n            <List\r\n              sx={{\r\n                width: \"100%\",\r\n                height: \"100%\",\r\n                maxWidth: 360,\r\n                fontWeight: \"600\",\r\n              }}\r\n              component=\"nav\"\r\n              aria-labelledby=\"nested-list-subheader\"\r\n            >\r\n              <MenuListItemNestedComponent\r\n                key={\"home\"}\r\n                props={{\r\n                  ...DashboardRoutes,\r\n                  isToggled: toggledMenuId === DashboardRoutes.id,\r\n                  onToggle: () => handleToggle(DashboardRoutes.id),\r\n                }}\r\n              />\r\n\r\n              <MenuListItemNestedComponent\r\n                key={\"analytics\"}\r\n                props={{\r\n                  ...AnalyticsRoutes,\r\n                  isToggled: toggledMenuId === AnalyticsRoutes.id,\r\n                  onToggle: () => handleToggle(AnalyticsRoutes.id),\r\n                }}\r\n              />\r\n\r\n              {Boolean(rbAccess.UserManagement) && (\r\n                <MenuListItemNestedComponent\r\n                  key={\"User-Management-Routes\"}\r\n                  props={{\r\n                    ...UserManagementRoutes,\r\n                    isToggled: toggledMenuId === UserManagementRoutes.id,\r\n                    onToggle: () => handleToggle(UserManagementRoutes.id),\r\n                  }}\r\n                />\r\n              )}\r\n\r\n              {Boolean(rbAccess.RoleManagement) && (\r\n                <MenuListItemNestedComponent\r\n                  key={\"Role-Management-Routes\"}\r\n                  props={{\r\n                    ...RoleManagementRoutes,\r\n                    isToggled: toggledMenuId === RoleManagementRoutes.id,\r\n                    onToggle: () => handleToggle(RoleManagementRoutes.id),\r\n                  }}\r\n                />\r\n              )}\r\n\r\n              {Boolean(rbAccess.BusinessManagement) && (\r\n                <MenuListItemNestedComponent\r\n                  key={\"Business-Management-Routes\"}\r\n                  props={{\r\n                    ...BusinessManagementRoutes,\r\n                    isToggled: toggledMenuId === BusinessManagementRoutes.id,\r\n                    onToggle: () => handleToggle(BusinessManagementRoutes.id),\r\n                  }}\r\n                />\r\n              )}\r\n\r\n              {Boolean(rbAccess.ReviewsManagement) && (\r\n                <MenuListItemNestedComponent\r\n                  key={\"Review-Management-Routes\"}\r\n                  props={{\r\n                    ...ReviewManagementRoutes,\r\n                    isToggled: toggledMenuId === ReviewManagementRoutes.id,\r\n                    onToggle: () => handleToggle(ReviewManagementRoutes.id),\r\n                  }}\r\n                />\r\n              )}\r\n\r\n              {Boolean(rbAccess.QandAManagement) && (\r\n                <MenuListItemNestedComponent\r\n                  key={\"QandA-Management-Routes\"}\r\n                  props={{\r\n                    ...QandAManagementRoutes,\r\n                    isToggled: toggledMenuId === QandAManagementRoutes.id,\r\n                    onToggle: () => handleToggle(QandAManagementRoutes.id),\r\n                  }}\r\n                />\r\n              )}\r\n\r\n              {Boolean(rbAccess.PostsManagement) && (\r\n                <MenuListItemNestedComponent\r\n                  key={\"Posts-Management-Routes\"}\r\n                  props={{\r\n                    ...PostsManagementRoutes,\r\n                    isToggled: toggledMenuId === PostsManagementRoutes.id,\r\n                    onToggle: () => handleToggle(PostsManagementRoutes.id),\r\n                  }}\r\n                />\r\n              )}\r\n\r\n              {/* Google Geo Grid - Available to users with LocationManagement permission */}\r\n              {/* {Boolean(rbAccess.LocationManagement) && (\r\n                <MenuListItemNestedComponent\r\n                  key={\"geo-grid\"}\r\n                  props={{\r\n                    ...GeoGridRoutes,\r\n                    isToggled: toggledMenuId === GeoGridRoutes.id,\r\n                    onToggle: () => handleToggle(GeoGridRoutes.id),\r\n                  }}\r\n                />\r\n              )} */}\r\n\r\n              {/* Manage Assets - Available to users with BusinessManagement permission */}\r\n              {Boolean(rbAccess.BusinessManagement) && (\r\n                <MenuListItemNestedComponent\r\n                  key={\"manage-assets\"}\r\n                  props={{\r\n                    ...ManageAssetsRoutes,\r\n                    isToggled: toggledMenuId === ManageAssetsRoutes.id,\r\n                    onToggle: () => handleToggle(ManageAssetsRoutes.id),\r\n                  }}\r\n                />\r\n              )}\r\n            </List>\r\n            <List>\r\n              <ListItemButton\r\n                sx={[\r\n                  {\r\n                    minHeight: 48,\r\n                    px: 2.5,\r\n                  },\r\n                  menuOpened\r\n                    ? {\r\n                        justifyContent: \"initial\",\r\n                      }\r\n                    : {\r\n                        justifyContent: \"center\",\r\n                      },\r\n                ]}\r\n                onClick={() => {\r\n                  openLeftMenu(false);\r\n                  setOpenMenuId(\"\");\r\n                  setTimeout(() => {\r\n                    logoutUser();\r\n                  }, 1000);\r\n                }}\r\n              >\r\n                <Tooltip title={!menuOpened ? \"Logout\" : \"\"} placement=\"right\">\r\n                  <ListItemIcon\r\n                    sx={{\r\n                      minWidth: 0,\r\n                      justifyContent: \"center\",\r\n                      mr: menuOpened ? 3 : \"auto\",\r\n                    }}\r\n                  >\r\n                    <LogoutOutlinedIcon\r\n                      color={menuOpened ? \"error\" : \"inherit\"}\r\n                    />\r\n                  </ListItemIcon>\r\n                </Tooltip>\r\n\r\n                <ListItemText\r\n                  primaryTypographyProps={{ fontWeight: 600 }}\r\n                  sx={{\r\n                    opacity: menuOpened ? 1 : 0,\r\n                    transition: \"opacity 0.3s\",\r\n                  }}\r\n                  primary={\"Logout\"}\r\n                />\r\n                <></>\r\n              </ListItemButton>\r\n            </List>\r\n          </Drawer>\r\n        )}\r\n\r\n        <Box component=\"main\" sx={{ flexGrow: 1, p: 3 }}>\r\n          <DrawerHeader />\r\n          {children}\r\n        </Box>\r\n      </Box>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default LeftMenuComponent;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAkCC,SAAS,QAAQ,OAAO;AACtE,SAASC,MAAM,EAAEC,QAAQ,QAA0B,sBAAsB;AACzE,OAAOC,GAAG,MAAM,mBAAmB;AACnC,OAAOC,SAAS,MAAM,sBAAsB;AAC5C,OAAOC,SAAS,MAAyC,sBAAsB;AAE/E,OAAOC,IAAI,MAAM,oBAAoB;AACrC,OAAOC,WAAW,MAAM,2BAA2B;AAGnD,OAAOC,UAAU,MAAM,0BAA0B;AAEjD,OAAOC,eAAe,MAAM,iCAAiC;AAI7D,OAAOC,gBAAgB,MAAM,kCAAkC;AAE/D,OAAOC,2BAA2B,MAAM,oDAAoD;AAC5F,OAAOC,6BAA6B,MAAM,+CAA+C;AACzF,OAAOC,aAAa,MAAM,6BAA6B;AAEvD,OAAOC,0BAA0B,MAAM,4CAA4C;AACnF,OAAOC,qBAAqB,MAAM,uCAAuC;AACzE,OAAOC,cAAc,MAAM,8BAA8B;AACzD,OAAOC,YAAY,MAAM,4BAA4B;AACrD,OAAOC,YAAY,MAAM,4BAA4B;AACrD,SAAmBC,OAAO,QAAQ,eAAe;;AAEjD;;AAIA,SAASC,MAAM,QAAQ,4BAA4B;AACnD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,OAAOC,eAAe,MAAM,4BAA4B;AACxD,OAAOC,kBAAkB,MAAM,oCAAoC;AACnE,OAAOC,oBAAoB,MAAM,sCAAsC;AACvE,OAAOC,uBAAuB,MAAM,yCAAyC;AAC7E,OAAOC,gBAAgB,MAAM,kCAAkC;AAE/D,OAAOC,yBAAyB,MAAM,2CAA2C;AACjF,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,sBAAsB,MAAM,wCAAwC;AAC3E,OAAOC,kBAAkB,MAAM,oCAAoC;AACnE,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,WAAW,MAAM,6BAA6B;AACrD,OAAOC,WAAW,MAAM,6BAA6B;AACrD,OAAOC,aAAa,MAAM,+BAA+B;AACzD,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,YAAY,MAAM,8BAA8B;AACvD,OAAOC,eAAe,MAAM,iCAAiC;AAC7D,SAASC,QAAQ,EAAEC,UAAU,QAAQ,uCAAuC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE7E,MAAMC,WAAW,GAAG,GAAG;AAEvB,MAAMC,WAAW,GAAIC,KAAY,KAAiB;EAChDC,KAAK,EAAEH,WAAW;EAClBI,UAAU,EAAEF,KAAK,CAACG,WAAW,CAACC,MAAM,CAAC,OAAO,EAAE;IAC5CC,MAAM,EAAEL,KAAK,CAACG,WAAW,CAACE,MAAM,CAACC,KAAK;IACtCC,QAAQ,EAAEP,KAAK,CAACG,WAAW,CAACI,QAAQ,CAACC;EACvC,CAAC,CAAC;EACFC,SAAS,EAAE;AACb,CAAC,CAAC;AAEF,MAAMC,WAAW,GAAIV,KAAY,KAAiB;EAChDE,UAAU,EAAEF,KAAK,CAACG,WAAW,CAACC,MAAM,CAAC,OAAO,EAAE;IAC5CC,MAAM,EAAEL,KAAK,CAACG,WAAW,CAACE,MAAM,CAACC,KAAK;IACtCC,QAAQ,EAAEP,KAAK,CAACG,WAAW,CAACI,QAAQ,CAACI;EACvC,CAAC,CAAC;EACFF,SAAS,EAAE,QAAQ;EACnBR,KAAK,EAAE,QAAQD,KAAK,CAACY,OAAO,CAAC,CAAC,CAAC,SAAS;EACxC,CAACZ,KAAK,CAACa,WAAW,CAACC,EAAE,CAAC,IAAI,CAAC,GAAG;IAC5Bb,KAAK,EAAE,QAAQD,KAAK,CAACY,OAAO,CAAC,CAAC,CAAC;EACjC;AACF,CAAC,CAAC;AAEF,MAAMG,YAAY,GAAG9D,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;EAAE+C;AAAM,CAAC,MAAM;EACjDgB,OAAO,EAAE,MAAM;EACfC,UAAU,EAAE,QAAQ;EACpBC,cAAc,EAAE,UAAU;EAC1BC,OAAO,EAAEnB,KAAK,CAACY,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;EAC5B;EACA,GAAGZ,KAAK,CAACoB,MAAM,CAACC;AAClB,CAAC,CAAC,CAAC;AAACC,EAAA,GAPEP,YAAY;AAalB,MAAMQ,MAAM,GAAGtE,MAAM,CAACI,SAAS,EAAE;EAC/BmE,iBAAiB,EAAGC,IAAI,IAAKA,IAAI,KAAK;AACxC,CAAC,CAAC,CAAc,CAAC;EAAEzB;AAAM,CAAC,MAAM;EAC9B;EACAE,UAAU,EAAEF,KAAK,CAACG,WAAW,CAACC,MAAM,CAAC,CAAC,OAAO,EAAE,QAAQ,CAAC,EAAE;IACxDC,MAAM,EAAEL,KAAK,CAACG,WAAW,CAACE,MAAM,CAACC,KAAK;IACtCC,QAAQ,EAAEP,KAAK,CAACG,WAAW,CAACI,QAAQ,CAACI;EACvC,CAAC,CAAC;EACFe,QAAQ,EAAE,CACR;IACEC,KAAK,EAAEA,CAAC;MAAEC;IAAK,CAAC,KAAKA,IAAI;IACzBC,KAAK,EAAE;MACLC,UAAU,EAAEhC,WAAW;MACvBG,KAAK,EAAE,eAAeH,WAAW,KAAK;MACtCI,UAAU,EAAEF,KAAK,CAACG,WAAW,CAACC,MAAM,CAAC,CAAC,OAAO,EAAE,QAAQ,CAAC,EAAE;QACxDC,MAAM,EAAEL,KAAK,CAACG,WAAW,CAACE,MAAM,CAACC,KAAK;QACtCC,QAAQ,EAAEP,KAAK,CAACG,WAAW,CAACI,QAAQ,CAACC;MACvC,CAAC;IACH;EACF,CAAC;AAEL,CAAC,CAAC,CAAC;AAEH,MAAMuB,MAAM,GAAG9E,MAAM,CAACG,SAAS,EAAE;EAC/BoE,iBAAiB,EAAGC,IAAI,IAAKA,IAAI,KAAK;AACxC,CAAC,CAAC,CAAC,CAAC;EAAEzB;AAAM,CAAC,MAAM;EACjBC,KAAK,EAAEH,WAAW;EAClBkC,UAAU,EAAE,CAAC;EACbC,UAAU,EAAE,QAAQ;EACpBC,SAAS,EAAE,YAAY;EACvBR,QAAQ,EAAE,CACR;IACEC,KAAK,EAAEA,CAAC;MAAEC;IAAK,CAAC,KAAKA,IAAI;IACzBC,KAAK,EAAE;MACL,GAAG9B,WAAW,CAACC,KAAK,CAAC;MACrB,oBAAoB,EAAED,WAAW,CAACC,KAAK;IACzC;EACF,CAAC,EACD;IACE2B,KAAK,EAAEA,CAAC;MAAEC;IAAK,CAAC,KAAK,CAACA,IAAI;IAC1BC,KAAK,EAAE;MACL,GAAGnB,WAAW,CAACV,KAAK,CAAC;MACrB,oBAAoB,EAAEU,WAAW,CAACV,KAAK;IACzC;EACF,CAAC;AAEL,CAAC,CAAC,CAAC;AAACmC,GAAA,GAvBEJ,MAAM;AA6BZ,MAAMK,iBAA2C,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACpE,MAAMC,QAAQ,GAAGlE,WAAW,CAAC,CAAC;EAC9B,MAAMmE,UAAU,GAAGA,CAAA,KAAMD,QAAQ,CAAMnE,MAAM,CAAC,CAAC,CAAC;EAChD,MAAMqE,YAAY,GAAIC,IAAa,IAAKH,QAAQ,CAAM/C,QAAQ,CAACkD,IAAI,CAAC,CAAC;EACrE,MAAMC,aAAa,GAAIC,YAA2B,IAChDL,QAAQ,CAAM9C,UAAU,CAACmD,YAAY,CAAC,CAAC;EACzC,MAAM;IAAEC,QAAQ;IAAEC;EAAS,CAAC,GAAGxE,WAAW,CAAEyE,KAAU,IAAKA,KAAK,CAACC,WAAW,CAAC;EAC7E,MAAM;IAAEC,UAAU;IAAEC;EAAc,CAAC,GAAG5E,WAAW,CAC9CyE,KAAU,IAAKA,KAAK,CAACI,sBACxB,CAAC;EAED,MAAMC,QAAQ,GAAGvF,aAAa,CAAC,mBAAmB,CAAC;EACnD,MAAMmC,KAAK,GAAG9C,QAAQ,CAAC,CAAC;EAExB,MAAMmG,YAAY,GAAIC,EAAU,IAAK;IACnC,MAAMC,QAAQ,GAAGL,aAAa,KAAKI,EAAE,GAAG,IAAI,GAAGA,EAAE;IACjDX,aAAa,CAACY,QAAQ,CAAC;EACzB,CAAC;EAEDvG,SAAS,CAAC,MAAM;IACdwG,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEZ,QAAQ,CAAC;IACnCW,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEX,QAAQ,CAAC;IAC7C,IAAIM,QAAQ,IAAIH,UAAU,EAAE;MAC1BR,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMiB,mBAAmB,GAAIC,MAAe,IAAK,CAAC,CAAC;EAEnD,MAAMC,QAAQ,GAAG,OAAO;EAExB,MAAMC,eAAgC,GAAG;IACvCP,EAAE,EAAE,MAAM;IACVQ,KAAK,EAAE,WAAW;IAClBC,UAAU,EAAE,OAAO;IACnBC,IAAI,eAAErE,OAAA,CAAC/B,6BAA6B;MAACqG,QAAQ,EAAEL;IAAS;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC3DzC,IAAI,EAAEqB,UAAU;IAChBqB,YAAY,EAAE;EAChB,CAAC;EAED,MAAMC,eAAgC,GAAG;IACvCjB,EAAE,EAAE,WAAW;IACfQ,KAAK,EAAE,WAAW;IAClBC,UAAU,EAAE,YAAY;IACxBC,IAAI,eAAErE,OAAA,CAACP,aAAa;MAAC6E,QAAQ,EAAEL;IAAS;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC3CzC,IAAI,EAAEqB,UAAU;IAChBqB,YAAY,EAAE;EAChB,CAAC;EAED,MAAME,oBAAqC,GAAG;IAC5ClB,EAAE,EAAE,iBAAiB;IACrBQ,KAAK,EAAE,iBAAiB;IACxBC,UAAU,EAAE,EAAE;IACdC,IAAI,eAAErE,OAAA,CAAC5B,qBAAqB;MAACkG,QAAQ,EAAEL;IAAS;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACnDzC,IAAI,EAAEqB,UAAU;IAChBqB,YAAY,EAAEG,OAAO,CAAC3B,QAAQ,IAAIA,QAAQ,CAAC4B,cAAc,CAAC;IAC1DC,MAAM,EAAE,CACN;MACErB,EAAE,EAAE,EAAE;MACNQ,KAAK,EAAE,YAAY;MACnBC,UAAU,EAAE,wBAAwB;MACpCnC,IAAI,EAAEqB,UAAU;MAChBqB,YAAY,EAAEG,OAAO,CAAC3B,QAAQ,IAAIA,QAAQ,CAAC8B,UAAU,CAAC;MACtDZ,IAAI,eAAErE,OAAA,CAACb,SAAS;QAACmF,QAAQ,EAAEL;MAAS;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IACxC,CAAC;EAEL,CAAC;EAED,MAAMQ,oBAAqC,GAAG;IAC5CvB,EAAE,EAAE,kBAAkB;IACtBQ,KAAK,EAAE,iBAAiB;IACxBC,UAAU,EAAE,EAAE;IACdC,IAAI,eAAErE,OAAA,CAAC7B,0BAA0B;MAAAoG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACpCzC,IAAI,EAAEqB,UAAU;IAChBqB,YAAY,EAAEG,OAAO,CAAC3B,QAAQ,IAAIA,QAAQ,CAACgC,cAAc,CAAC;IAC1DH,MAAM,EAAE,CACN;MACErB,EAAE,EAAE,EAAE;MACNQ,KAAK,EAAE,cAAc;MACrBC,UAAU,EAAE,yBAAyB;MACrCnC,IAAI,EAAEqB,UAAU;MAChBqB,YAAY,EAAEG,OAAO,CAAC3B,QAAQ,IAAIA,QAAQ,CAACgC,cAAc,CAAC;MAC1Dd,IAAI,eAAErE,OAAA,CAACZ,sBAAsB;QAAAmF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IACjC,CAAC;EAEL,CAAC;EAED,MAAMU,wBAAyC,GAAG;IAChDzB,EAAE,EAAE,qBAAqB;IACzBQ,KAAK,EAAE,qBAAqB;IAC5BC,UAAU,EAAE,EAAE;IACdC,IAAI,eAAErE,OAAA,CAAClB,oBAAoB;MAACwF,QAAQ,EAAEL;IAAS;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAClDzC,IAAI,EAAEqB,UAAU;IAChBqB,YAAY,EAAEG,OAAO,CAAC3B,QAAQ,IAAIA,QAAQ,CAACkC,kBAAkB,CAAC;IAC9DL,MAAM,EAAE,CACN;MACErB,EAAE,EAAE,EAAE;MACNQ,KAAK,EAAE,iBAAiB;MACxBC,UAAU,EAAE,sCAAsC;MAClDnC,IAAI,EAAEqB,UAAU;MAChBqB,YAAY,EAAEG,OAAO,CAAC3B,QAAQ,IAAIA,QAAQ,CAACkC,kBAAkB,CAAC;MAC9DhB,IAAI,eAAErE,OAAA,CAACX,kBAAkB;QAACiF,QAAQ,EAAEL;MAAS;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IACjD,CAAC,EACD;MACEf,EAAE,EAAE,EAAE;MACNQ,KAAK,EAAE,gBAAgB;MACvBC,UAAU,EAAE,qCAAqC;MACjDnC,IAAI,EAAEqB,UAAU;MAChBqB,YAAY,EAAEG,OAAO,CAAC3B,QAAQ,IAAIA,QAAQ,CAACkC,kBAAkB,CAAC;MAC9DhB,IAAI,eAAErE,OAAA,CAACV,SAAS;QAACgF,QAAQ,EAAEL;MAAS;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IACxC;IACA;IACA;IACA;IACA;IACA;IACA;IAAA;EAEJ,CAAC;EAED,MAAMY,sBAAuC,GAAG;IAC9C3B,EAAE,EAAE,mBAAmB;IACvBQ,KAAK,EAAE,mBAAmB;IAC1BC,UAAU,EAAE,EAAE;IACdC,IAAI,eAAErE,OAAA,CAACjB,uBAAuB;MAACuF,QAAQ,EAAEL;IAAS;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACrDzC,IAAI,EAAEqB,UAAU;IAChBqB,YAAY,EAAEG,OAAO,CAAC3B,QAAQ,IAAIA,QAAQ,CAACoC,iBAAiB,CAAC;IAC7DP,MAAM,EAAE,CACN;MACErB,EAAE,EAAE,EAAE;MACNQ,KAAK,EAAE,8BAA8B;MACrCC,UAAU,EAAE,mCAAmC;MAC/CnC,IAAI,EAAEqB,UAAU;MAChBqB,YAAY,EAAE,IAAI;MAClBN,IAAI,eAAErE,OAAA,CAACd,cAAc;QAACoF,QAAQ,EAAEL;MAAS;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAC7C,CAAC,EACD;MACEf,EAAE,EAAE,EAAE;MACNQ,KAAK,EAAE,iBAAiB;MACxBC,UAAU,EAAE,oCAAoC;MAChDnC,IAAI,EAAEqB,UAAU;MAChBqB,YAAY,EAAE,IAAI;MAClBN,IAAI,eAAErE,OAAA,CAACL,YAAY;QAAC2E,QAAQ,EAAEL;MAAS;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAC3C,CAAC;EAEL,CAAC;EAED,MAAMc,qBAAsC,GAAG;IAC7C7B,EAAE,EAAE,SAAS;IACbQ,KAAK,EAAE,KAAK;IACZC,UAAU,EAAE,UAAU;IACtBC,IAAI,eAAErE,OAAA,CAACf,yBAAyB;MAACqF,QAAQ,EAAEL;IAAS;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACvDzC,IAAI,EAAEqB,UAAU;IAChBqB,YAAY,EAAEG,OAAO,CAAC3B,QAAQ,IAAIA,QAAQ,CAACsC,eAAe;EAC5D,CAAC;EAED,MAAMC,qBAAsC,GAAG;IAC7C/B,EAAE,EAAE,iBAAiB;IACrBQ,KAAK,EAAE,kBAAkB;IACzBC,UAAU,EAAE,EAAE;IACdC,IAAI,eAAErE,OAAA,CAAChB,gBAAgB;MAACsF,QAAQ,EAAEL;IAAS;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC9CzC,IAAI,EAAEqB,UAAU;IAChBqB,YAAY,EAAEG,OAAO,CAAC3B,QAAQ,IAAIA,QAAQ,CAACwC,eAAe,CAAC;IAC3DX,MAAM,EAAE,CACN;MACErB,EAAE,EAAE,EAAE;MACNQ,KAAK,EAAE,mBAAmB;MAC1BC,UAAU,EAAE,wBAAwB;MACpCnC,IAAI,EAAEqB,UAAU;MAChBqB,YAAY,EAAE,IAAI;MAClBN,IAAI,eAAErE,OAAA,CAACT,WAAW;QAAC+E,QAAQ,EAAEL;MAAS;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAC1C,CAAC;IACD;IACA;IACA;IACA;IACA;IACA;IACA;MACEf,EAAE,EAAE,EAAE;MACNQ,KAAK,EAAE,cAAc;MACrBC,UAAU,EAAE,qCAAqC;MACjDnC,IAAI,EAAEqB,UAAU;MAChBqB,YAAY,EAAEG,OAAO,CAAC3B,QAAQ,IAAIA,QAAQ,CAACyC,WAAW,CAAC;MACvDvB,IAAI,eAAErE,OAAA,CAACR,WAAW;QAAC8E,QAAQ,EAAEL;MAAS;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAC1C;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAAA;EAEJ,CAAC;EAED,MAAMmB,aAA8B,GAAG;IACrClC,EAAE,EAAE,UAAU;IACdQ,KAAK,EAAE,iBAAiB;IACxBC,UAAU,EAAE,WAAW;IACvBC,IAAI,eAAErE,OAAA,CAACN,UAAU;MAAC4E,QAAQ,EAAEL;IAAS;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxCzC,IAAI,EAAEqB,UAAU;IAChB;IACA;IACAqB,YAAY,EAAEG,OAAO,CAAC3B,QAAQ,IAAIA,QAAQ,CAAC2C,kBAAkB;EAC/D,CAAC;EAED,MAAMC,kBAAmC,GAAG;IAC1CpC,EAAE,EAAE,eAAe;IACnBQ,KAAK,EAAE,eAAe;IACtBC,UAAU,EAAE,gBAAgB;IAC5BC,IAAI,eAAErE,OAAA,CAACJ,eAAe;MAAC0E,QAAQ,EAAEL;IAAS;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC7CzC,IAAI,EAAEqB,UAAU;IAChB;IACAqB,YAAY,EAAEG,OAAO,CAAC3B,QAAQ,IAAIA,QAAQ,CAACkC,kBAAkB;EAC/D,CAAC;EAED,oBACErF,OAAA;IAAA0C,QAAA,eACE1C,OAAA,CAACxC,GAAG;MAACwI,EAAE,EAAE;QAAE3E,OAAO,EAAE;MAAO,CAAE;MAAAqB,QAAA,gBAC3B1C,OAAA,CAACpC,WAAW;QAAA2G,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eA2Bf1E,OAAA,CAACpB,eAAe;QAAA2F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EAIlBvB,QAAQ,iBACPnD,OAAA,CAACoC,MAAM;QACL6D,OAAO,EAAC,WAAW;QACnBhE,IAAI,EAAEqB,UAAW;QACjBpB,KAAK,EACHuB,QAAQ,IAAIH,UAAU,GAClB;UAAE4C,QAAQ,EAAE;QAAW,CAAC,GACxB;UAAEA,QAAQ,EAAE;QAAQ;QAE1B;QACA;QAAA;QAAAxD,QAAA,gBAEA1C,OAAA,CAACoB,YAAY;UAAC+E,SAAS,EAAC,aAAa;UAAAzD,QAAA,GAClCY,UAAU,gBACTtD,OAAA,CAACxC,GAAG;YAACwI,EAAE,EAAE;cAAEI,SAAS,EAAE,QAAQ;cAAEC,EAAE,EAAE,CAAC;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAA5D,QAAA,eAC7C1C,OAAA;cACEuG,GAAG,EAAC,mBAAmB;cACvBJ,SAAS,EAAC,uBAAuB;cACjCK,GAAG,EAAEC,OAAO,CAAC,8BAA8B;YAAE;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,gBAEN1E,OAAA,CAAAE,SAAA,mBAAI;UACJ;UACA;UACA;UACA;UACA;UACA;UACA;UACD,EACAuD,QAAQ,gBACPzD,OAAA,CAACnC,UAAU;YAAC6I,OAAO,EAAEA,CAAA,KAAM5D,YAAY,CAAC,CAACQ,UAAU,CAAE;YAAAZ,QAAA,EAClDY,UAAU,gBAAGtD,OAAA,CAAClC,eAAe;cAAAyG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAG1E,OAAA,CAACjC,gBAAgB;cAAAwG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,gBAEb1E,OAAA,CAACnC,UAAU;YAAC6I,OAAO,EAAEA,CAAA,KAAM5D,YAAY,CAAC,CAACQ,UAAU,CAAE;YAAAZ,QAAA,EAClDY,UAAU,gBAAGtD,OAAA,CAAClC,eAAe;cAAAyG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAG1E,OAAA,CAACjC,gBAAgB;cAAAwG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CACb;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACW,CAAC,eAIf1E,OAAA,CAACrC,IAAI;UACHqI,EAAE,EAAE;YACF1F,KAAK,EAAE,MAAM;YACbqG,MAAM,EAAE,MAAM;YACdC,QAAQ,EAAE,GAAG;YACbC,UAAU,EAAE;UACd,CAAE;UACFC,SAAS,EAAC,KAAK;UACf,mBAAgB,uBAAuB;UAAApE,QAAA,gBAEvC1C,OAAA,CAAChC,2BAA2B;YAE1BgE,KAAK,EAAE;cACL,GAAGkC,eAAe;cAClB6C,SAAS,EAAExD,aAAa,KAAKW,eAAe,CAACP,EAAE;cAC/CqD,QAAQ,EAAEA,CAAA,KAAMtD,YAAY,CAACQ,eAAe,CAACP,EAAE;YACjD;UAAE,GALG,MAAM;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAMZ,CAAC,eAEF1E,OAAA,CAAChC,2BAA2B;YAE1BgE,KAAK,EAAE;cACL,GAAG4C,eAAe;cAClBmC,SAAS,EAAExD,aAAa,KAAKqB,eAAe,CAACjB,EAAE;cAC/CqD,QAAQ,EAAEA,CAAA,KAAMtD,YAAY,CAACkB,eAAe,CAACjB,EAAE;YACjD;UAAE,GALG,WAAW;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAMjB,CAAC,EAEDI,OAAO,CAAC3B,QAAQ,CAAC4B,cAAc,CAAC,iBAC/B/E,OAAA,CAAChC,2BAA2B;YAE1BgE,KAAK,EAAE;cACL,GAAG6C,oBAAoB;cACvBkC,SAAS,EAAExD,aAAa,KAAKsB,oBAAoB,CAAClB,EAAE;cACpDqD,QAAQ,EAAEA,CAAA,KAAMtD,YAAY,CAACmB,oBAAoB,CAAClB,EAAE;YACtD;UAAE,GALG,wBAAwB;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAM9B,CACF,EAEAI,OAAO,CAAC3B,QAAQ,CAACgC,cAAc,CAAC,iBAC/BnF,OAAA,CAAChC,2BAA2B;YAE1BgE,KAAK,EAAE;cACL,GAAGkD,oBAAoB;cACvB6B,SAAS,EAAExD,aAAa,KAAK2B,oBAAoB,CAACvB,EAAE;cACpDqD,QAAQ,EAAEA,CAAA,KAAMtD,YAAY,CAACwB,oBAAoB,CAACvB,EAAE;YACtD;UAAE,GALG,wBAAwB;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAM9B,CACF,EAEAI,OAAO,CAAC3B,QAAQ,CAACkC,kBAAkB,CAAC,iBACnCrF,OAAA,CAAChC,2BAA2B;YAE1BgE,KAAK,EAAE;cACL,GAAGoD,wBAAwB;cAC3B2B,SAAS,EAAExD,aAAa,KAAK6B,wBAAwB,CAACzB,EAAE;cACxDqD,QAAQ,EAAEA,CAAA,KAAMtD,YAAY,CAAC0B,wBAAwB,CAACzB,EAAE;YAC1D;UAAE,GALG,4BAA4B;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAMlC,CACF,EAEAI,OAAO,CAAC3B,QAAQ,CAACoC,iBAAiB,CAAC,iBAClCvF,OAAA,CAAChC,2BAA2B;YAE1BgE,KAAK,EAAE;cACL,GAAGsD,sBAAsB;cACzByB,SAAS,EAAExD,aAAa,KAAK+B,sBAAsB,CAAC3B,EAAE;cACtDqD,QAAQ,EAAEA,CAAA,KAAMtD,YAAY,CAAC4B,sBAAsB,CAAC3B,EAAE;YACxD;UAAE,GALG,0BAA0B;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAMhC,CACF,EAEAI,OAAO,CAAC3B,QAAQ,CAACsC,eAAe,CAAC,iBAChCzF,OAAA,CAAChC,2BAA2B;YAE1BgE,KAAK,EAAE;cACL,GAAGwD,qBAAqB;cACxBuB,SAAS,EAAExD,aAAa,KAAKiC,qBAAqB,CAAC7B,EAAE;cACrDqD,QAAQ,EAAEA,CAAA,KAAMtD,YAAY,CAAC8B,qBAAqB,CAAC7B,EAAE;YACvD;UAAE,GALG,yBAAyB;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAM/B,CACF,EAEAI,OAAO,CAAC3B,QAAQ,CAACwC,eAAe,CAAC,iBAChC3F,OAAA,CAAChC,2BAA2B;YAE1BgE,KAAK,EAAE;cACL,GAAG0D,qBAAqB;cACxBqB,SAAS,EAAExD,aAAa,KAAKmC,qBAAqB,CAAC/B,EAAE;cACrDqD,QAAQ,EAAEA,CAAA,KAAMtD,YAAY,CAACgC,qBAAqB,CAAC/B,EAAE;YACvD;UAAE,GALG,yBAAyB;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAM/B,CACF,EAeAI,OAAO,CAAC3B,QAAQ,CAACkC,kBAAkB,CAAC,iBACnCrF,OAAA,CAAChC,2BAA2B;YAE1BgE,KAAK,EAAE;cACL,GAAG+D,kBAAkB;cACrBgB,SAAS,EAAExD,aAAa,KAAKwC,kBAAkB,CAACpC,EAAE;cAClDqD,QAAQ,EAAEA,CAAA,KAAMtD,YAAY,CAACqC,kBAAkB,CAACpC,EAAE;YACpD;UAAE,GALG,eAAe;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAMrB,CACF;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,eACP1E,OAAA,CAACrC,IAAI;UAAA+E,QAAA,eACH1C,OAAA,CAAC3B,cAAc;YACb2H,EAAE,EAAE,CACF;cACEiB,SAAS,EAAE,EAAE;cACbC,EAAE,EAAE;YACN,CAAC,EACD5D,UAAU,GACN;cACE/B,cAAc,EAAE;YAClB,CAAC,GACD;cACEA,cAAc,EAAE;YAClB,CAAC,CACL;YACFmF,OAAO,EAAEA,CAAA,KAAM;cACb5D,YAAY,CAAC,KAAK,CAAC;cACnBE,aAAa,CAAC,EAAE,CAAC;cACjBmE,UAAU,CAAC,MAAM;gBACftE,UAAU,CAAC,CAAC;cACd,CAAC,EAAE,IAAI,CAAC;YACV,CAAE;YAAAH,QAAA,gBAEF1C,OAAA,CAACxB,OAAO;cAAC2F,KAAK,EAAE,CAACb,UAAU,GAAG,QAAQ,GAAG,EAAG;cAAC8D,SAAS,EAAC,OAAO;cAAA1E,QAAA,eAC5D1C,OAAA,CAAC1B,YAAY;gBACX0H,EAAE,EAAE;kBACFqB,QAAQ,EAAE,CAAC;kBACX9F,cAAc,EAAE,QAAQ;kBACxB+F,EAAE,EAAEhE,UAAU,GAAG,CAAC,GAAG;gBACvB,CAAE;gBAAAZ,QAAA,eAEF1C,OAAA,CAACnB,kBAAkB;kBACjB0I,KAAK,EAAEjE,UAAU,GAAG,OAAO,GAAG;gBAAU;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACU;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eAEV1E,OAAA,CAACzB,YAAY;cACXiJ,sBAAsB,EAAE;gBAAEX,UAAU,EAAE;cAAI,CAAE;cAC5Cb,EAAE,EAAE;gBACFyB,OAAO,EAAEnE,UAAU,GAAG,CAAC,GAAG,CAAC;gBAC3B/C,UAAU,EAAE;cACd,CAAE;cACFmH,OAAO,EAAE;YAAS;cAAAnD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC,eACF1E,OAAA,CAAAE,SAAA,mBAAI,CAAC;UAAA;YAAAqE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACT,eAED1E,OAAA,CAACxC,GAAG;QAACsJ,SAAS,EAAC,MAAM;QAACd,EAAE,EAAE;UAAE2B,QAAQ,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAAAlF,QAAA,gBAC9C1C,OAAA,CAACoB,YAAY;UAAAmD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EACfhC,QAAQ;MAAA;QAAA6B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC/B,EAAA,CAvdIF,iBAA2C;EAAA,QAC9B/D,WAAW,EAKGC,WAAW,EACJA,WAAW,EAIhCT,aAAa,EAChBX,QAAQ;AAAA;AAAAsK,GAAA,GAZlBpF,iBAA2C;AAydjD,eAAeA,iBAAiB;AAAC,IAAAd,EAAA,EAAAa,GAAA,EAAAqF,GAAA;AAAAC,YAAA,CAAAnG,EAAA;AAAAmG,YAAA,CAAAtF,GAAA;AAAAsF,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}