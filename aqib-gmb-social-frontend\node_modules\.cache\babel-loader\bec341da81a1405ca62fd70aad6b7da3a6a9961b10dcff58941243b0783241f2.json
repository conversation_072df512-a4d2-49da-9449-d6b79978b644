{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { unstable_useId as useId, unstable_useForkRef as useForkRef } from '@mui/utils';\nimport { useTabsContext } from '../Tabs';\nimport { extractEventHandlers } from '../utils/extractEventHandlers';\nimport { useCompoundItem } from '../useCompound';\nimport { useListItem } from '../useList';\nimport { useButton } from '../useButton';\nimport { combineHooksSlotProps } from '../utils/combineHooksSlotProps';\nfunction tabValueGenerator(otherTabValues) {\n  return otherTabValues.size;\n}\n\n/**\n *\n * Demos:\n *\n * - [Tabs](https://mui.com/base-ui/react-tabs/#hooks)\n *\n * API:\n *\n * - [useTab API](https://mui.com/base-ui/react-tabs/hooks-api/#use-tab)\n */\nfunction useTab(parameters) {\n  const {\n    value: valueParam,\n    rootRef: externalRef,\n    disabled = false,\n    id: idParam\n  } = parameters;\n  const tabRef = React.useRef(null);\n  const id = useId(idParam);\n  const {\n    value: selectedValue,\n    selectionFollowsFocus,\n    getTabPanelId\n  } = useTabsContext();\n  const tabMetadata = React.useMemo(() => ({\n    disabled,\n    ref: tabRef,\n    id\n  }), [disabled, tabRef, id]);\n  const {\n    id: value,\n    index,\n    totalItemCount: totalTabsCount\n  } = useCompoundItem(valueParam != null ? valueParam : tabValueGenerator, tabMetadata);\n  const {\n    getRootProps: getTabProps,\n    highlighted,\n    selected\n  } = useListItem({\n    item: value\n  });\n  const {\n    getRootProps: getButtonProps,\n    rootRef: buttonRefHandler,\n    active,\n    focusVisible,\n    setFocusVisible\n  } = useButton({\n    disabled,\n    focusableWhenDisabled: !selectionFollowsFocus,\n    type: 'button'\n  });\n  const handleRef = useForkRef(tabRef, externalRef, buttonRefHandler);\n  const tabPanelId = value !== undefined ? getTabPanelId(value) : undefined;\n  const getRootProps = function () {\n    let externalProps = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    const externalEventHandlers = extractEventHandlers(externalProps);\n    const getCombinedRootProps = combineHooksSlotProps(getTabProps, getButtonProps);\n    return _extends({}, externalProps, getCombinedRootProps(externalEventHandlers), {\n      role: 'tab',\n      'aria-controls': tabPanelId,\n      'aria-selected': selected,\n      id,\n      ref: handleRef\n    });\n  };\n  return {\n    getRootProps,\n    active,\n    focusVisible,\n    highlighted,\n    index,\n    rootRef: handleRef,\n    // the `selected` state isn't set on the server (it relies on effects to be calculated),\n    // so we fall back to checking the `value` prop with the selectedValue from the TabsContext\n    selected: selected || value === selectedValue,\n    setFocusVisible,\n    totalTabsCount\n  };\n}\nexport { useTab };", "map": {"version": 3, "names": ["_extends", "React", "unstable_useId", "useId", "unstable_useForkRef", "useForkRef", "useTabsContext", "extractEventHandlers", "useCompoundItem", "useListItem", "useButton", "combineHooksSlotProps", "tabValueGenerator", "otherTabValues", "size", "useTab", "parameters", "value", "valueParam", "rootRef", "externalRef", "disabled", "id", "idParam", "tabRef", "useRef", "selected<PERSON><PERSON><PERSON>", "selectionFollowsFocus", "getTabPanelId", "tabMetadata", "useMemo", "ref", "index", "totalItemCount", "totalTabsCount", "getRootProps", "getTabProps", "highlighted", "selected", "item", "getButtonProps", "buttonRefHandler", "active", "focusVisible", "setFocusVisible", "focusableWhenDisabled", "type", "handleRef", "tabPanelId", "undefined", "externalProps", "arguments", "length", "externalEventHandlers", "getCombinedRootProps", "role"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/node_modules/@mui/base/useTab/useTab.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { unstable_useId as useId, unstable_useForkRef as useForkRef } from '@mui/utils';\nimport { useTabsContext } from '../Tabs';\nimport { extractEventHandlers } from '../utils/extractEventHandlers';\nimport { useCompoundItem } from '../useCompound';\nimport { useListItem } from '../useList';\nimport { useButton } from '../useButton';\nimport { combineHooksSlotProps } from '../utils/combineHooksSlotProps';\nfunction tabValueGenerator(otherTabValues) {\n  return otherTabValues.size;\n}\n\n/**\n *\n * Demos:\n *\n * - [Tabs](https://mui.com/base-ui/react-tabs/#hooks)\n *\n * API:\n *\n * - [useTab API](https://mui.com/base-ui/react-tabs/hooks-api/#use-tab)\n */\nfunction useTab(parameters) {\n  const {\n    value: valueParam,\n    rootRef: externalRef,\n    disabled = false,\n    id: idParam\n  } = parameters;\n  const tabRef = React.useRef(null);\n  const id = useId(idParam);\n  const {\n    value: selectedValue,\n    selectionFollowsFocus,\n    getTabPanelId\n  } = useTabsContext();\n  const tabMetadata = React.useMemo(() => ({\n    disabled,\n    ref: tabRef,\n    id\n  }), [disabled, tabRef, id]);\n  const {\n    id: value,\n    index,\n    totalItemCount: totalTabsCount\n  } = useCompoundItem(valueParam != null ? valueParam : tabValueGenerator, tabMetadata);\n  const {\n    getRootProps: getTabProps,\n    highlighted,\n    selected\n  } = useListItem({\n    item: value\n  });\n  const {\n    getRootProps: getButtonProps,\n    rootRef: buttonRefHandler,\n    active,\n    focusVisible,\n    setFocusVisible\n  } = useButton({\n    disabled,\n    focusableWhenDisabled: !selectionFollowsFocus,\n    type: 'button'\n  });\n  const handleRef = useForkRef(tabRef, externalRef, buttonRefHandler);\n  const tabPanelId = value !== undefined ? getTabPanelId(value) : undefined;\n  const getRootProps = (externalProps = {}) => {\n    const externalEventHandlers = extractEventHandlers(externalProps);\n    const getCombinedRootProps = combineHooksSlotProps(getTabProps, getButtonProps);\n    return _extends({}, externalProps, getCombinedRootProps(externalEventHandlers), {\n      role: 'tab',\n      'aria-controls': tabPanelId,\n      'aria-selected': selected,\n      id,\n      ref: handleRef\n    });\n  };\n  return {\n    getRootProps,\n    active,\n    focusVisible,\n    highlighted,\n    index,\n    rootRef: handleRef,\n    // the `selected` state isn't set on the server (it relies on effects to be calculated),\n    // so we fall back to checking the `value` prop with the selectedValue from the TabsContext\n    selected: selected || value === selectedValue,\n    setFocusVisible,\n    totalTabsCount\n  };\n}\nexport { useTab };"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,cAAc,IAAIC,KAAK,EAAEC,mBAAmB,IAAIC,UAAU,QAAQ,YAAY;AACvF,SAASC,cAAc,QAAQ,SAAS;AACxC,SAASC,oBAAoB,QAAQ,+BAA+B;AACpE,SAASC,eAAe,QAAQ,gBAAgB;AAChD,SAASC,WAAW,QAAQ,YAAY;AACxC,SAASC,SAAS,QAAQ,cAAc;AACxC,SAASC,qBAAqB,QAAQ,gCAAgC;AACtE,SAASC,iBAAiBA,CAACC,cAAc,EAAE;EACzC,OAAOA,cAAc,CAACC,IAAI;AAC5B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,MAAMA,CAACC,UAAU,EAAE;EAC1B,MAAM;IACJC,KAAK,EAAEC,UAAU;IACjBC,OAAO,EAAEC,WAAW;IACpBC,QAAQ,GAAG,KAAK;IAChBC,EAAE,EAAEC;EACN,CAAC,GAAGP,UAAU;EACd,MAAMQ,MAAM,GAAGvB,KAAK,CAACwB,MAAM,CAAC,IAAI,CAAC;EACjC,MAAMH,EAAE,GAAGnB,KAAK,CAACoB,OAAO,CAAC;EACzB,MAAM;IACJN,KAAK,EAAES,aAAa;IACpBC,qBAAqB;IACrBC;EACF,CAAC,GAAGtB,cAAc,CAAC,CAAC;EACpB,MAAMuB,WAAW,GAAG5B,KAAK,CAAC6B,OAAO,CAAC,OAAO;IACvCT,QAAQ;IACRU,GAAG,EAAEP,MAAM;IACXF;EACF,CAAC,CAAC,EAAE,CAACD,QAAQ,EAAEG,MAAM,EAAEF,EAAE,CAAC,CAAC;EAC3B,MAAM;IACJA,EAAE,EAAEL,KAAK;IACTe,KAAK;IACLC,cAAc,EAAEC;EAClB,CAAC,GAAG1B,eAAe,CAACU,UAAU,IAAI,IAAI,GAAGA,UAAU,GAAGN,iBAAiB,EAAEiB,WAAW,CAAC;EACrF,MAAM;IACJM,YAAY,EAAEC,WAAW;IACzBC,WAAW;IACXC;EACF,CAAC,GAAG7B,WAAW,CAAC;IACd8B,IAAI,EAAEtB;EACR,CAAC,CAAC;EACF,MAAM;IACJkB,YAAY,EAAEK,cAAc;IAC5BrB,OAAO,EAAEsB,gBAAgB;IACzBC,MAAM;IACNC,YAAY;IACZC;EACF,CAAC,GAAGlC,SAAS,CAAC;IACZW,QAAQ;IACRwB,qBAAqB,EAAE,CAAClB,qBAAqB;IAC7CmB,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAMC,SAAS,GAAG1C,UAAU,CAACmB,MAAM,EAAEJ,WAAW,EAAEqB,gBAAgB,CAAC;EACnE,MAAMO,UAAU,GAAG/B,KAAK,KAAKgC,SAAS,GAAGrB,aAAa,CAACX,KAAK,CAAC,GAAGgC,SAAS;EACzE,MAAMd,YAAY,GAAG,SAAAA,CAAA,EAAwB;IAAA,IAAvBe,aAAa,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAF,SAAA,GAAAE,SAAA,MAAG,CAAC,CAAC;IACtC,MAAME,qBAAqB,GAAG9C,oBAAoB,CAAC2C,aAAa,CAAC;IACjE,MAAMI,oBAAoB,GAAG3C,qBAAqB,CAACyB,WAAW,EAAEI,cAAc,CAAC;IAC/E,OAAOxC,QAAQ,CAAC,CAAC,CAAC,EAAEkD,aAAa,EAAEI,oBAAoB,CAACD,qBAAqB,CAAC,EAAE;MAC9EE,IAAI,EAAE,KAAK;MACX,eAAe,EAAEP,UAAU;MAC3B,eAAe,EAAEV,QAAQ;MACzBhB,EAAE;MACFS,GAAG,EAAEgB;IACP,CAAC,CAAC;EACJ,CAAC;EACD,OAAO;IACLZ,YAAY;IACZO,MAAM;IACNC,YAAY;IACZN,WAAW;IACXL,KAAK;IACLb,OAAO,EAAE4B,SAAS;IAClB;IACA;IACAT,QAAQ,EAAEA,QAAQ,IAAIrB,KAAK,KAAKS,aAAa;IAC7CkB,eAAe;IACfV;EACF,CAAC;AACH;AACA,SAASnB,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}