{"ast": null, "code": "import * as React from 'react';\nconst DropdownContext = /*#__PURE__*/React.createContext(null);\nif (process.env.NODE_ENV !== 'production') {\n  DropdownContext.displayName = 'DropdownContext';\n}\nexport { DropdownContext };", "map": {"version": 3, "names": ["React", "DropdownContext", "createContext", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/node_modules/@mui/base/useDropdown/DropdownContext.js"], "sourcesContent": ["import * as React from 'react';\nconst DropdownContext = /*#__PURE__*/React.createContext(null);\nif (process.env.NODE_ENV !== 'production') {\n  DropdownContext.displayName = 'DropdownContext';\n}\nexport { DropdownContext };"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,MAAMC,eAAe,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,IAAI,CAAC;AAC9D,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,eAAe,CAACK,WAAW,GAAG,iBAAiB;AACjD;AACA,SAASL,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}