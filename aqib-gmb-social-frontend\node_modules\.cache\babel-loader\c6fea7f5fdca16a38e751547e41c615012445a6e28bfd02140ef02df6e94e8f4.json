{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport { TransitionContext } from './TransitionContext';\n/**\n * Allows an element to be transitioned in and out.\n * The transition is triggerred by a `TransitionContext` placed above in the component tree.\n *\n * Demos:\n *\n * - [Transitions](https://mui.com/base-ui/react-transitions/#hooks)\n *\n * API:\n *\n * - [useTransitionStateManager API](https://mui.com/base-ui/react-transitions/hooks-api/#use-transition-state-manager)\n */\nexport function useTransitionStateManager() {\n  const transitionContext = React.useContext(TransitionContext);\n  if (!transitionContext) {\n    throw new Error('Missing transition context');\n  }\n  const {\n    registerTransition,\n    requestedEnter,\n    onExited\n  } = transitionContext;\n  React.useEffect(() => {\n    return registerTransition();\n  }, [registerTransition]);\n  return {\n    onExited,\n    requestedEnter\n  };\n}", "map": {"version": 3, "names": ["React", "TransitionContext", "useTransitionStateManager", "transitionContext", "useContext", "Error", "registerTransition", "requestedEnter", "onExited", "useEffect"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/node_modules/@mui/base/useTransition/useTransitionStateManager.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport { TransitionContext } from './TransitionContext';\n/**\n * Allows an element to be transitioned in and out.\n * The transition is triggerred by a `TransitionContext` placed above in the component tree.\n *\n * Demos:\n *\n * - [Transitions](https://mui.com/base-ui/react-transitions/#hooks)\n *\n * API:\n *\n * - [useTransitionStateManager API](https://mui.com/base-ui/react-transitions/hooks-api/#use-transition-state-manager)\n */\nexport function useTransitionStateManager() {\n  const transitionContext = React.useContext(TransitionContext);\n  if (!transitionContext) {\n    throw new Error('Missing transition context');\n  }\n  const {\n    registerTransition,\n    requestedEnter,\n    onExited\n  } = transitionContext;\n  React.useEffect(() => {\n    return registerTransition();\n  }, [registerTransition]);\n  return {\n    onExited,\n    requestedEnter\n  };\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,yBAAyBA,CAAA,EAAG;EAC1C,MAAMC,iBAAiB,GAAGH,KAAK,CAACI,UAAU,CAACH,iBAAiB,CAAC;EAC7D,IAAI,CAACE,iBAAiB,EAAE;IACtB,MAAM,IAAIE,KAAK,CAAC,4BAA4B,CAAC;EAC/C;EACA,MAAM;IACJC,kBAAkB;IAClBC,cAAc;IACdC;EACF,CAAC,GAAGL,iBAAiB;EACrBH,KAAK,CAACS,SAAS,CAAC,MAAM;IACpB,OAAOH,kBAAkB,CAAC,CAAC;EAC7B,CAAC,EAAE,CAACA,kBAAkB,CAAC,CAAC;EACxB,OAAO;IACLE,QAAQ;IACRD;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}