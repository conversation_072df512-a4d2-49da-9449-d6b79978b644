{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"children\", \"className\", \"lastTransitionedPropertyOnExit\", \"enterClassName\", \"exitClassName\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport { useTransitionStateManager } from '../useTransition';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\n/**\n * A utility component that hooks up to the Base UI transitions API and\n * applies a CSS transition to its children when necessary.\n *\n * Demos:\n *\n * - [Transitions](https://mui.com/base-ui/react-transitions/)\n *\n * API:\n *\n * - [CssTransition API](https://mui.com/base-ui/react-transitions/components-api/#css-transition)\n */\nconst CssTransition = /*#__PURE__*/React.forwardRef(function CssTransition(props, forwardedRef) {\n  const {\n      children,\n      className,\n      lastTransitionedPropertyOnExit,\n      enterClassName,\n      exitClassName\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    requestedEnter,\n    onExited\n  } = useTransitionStateManager();\n  const [isEntering, setIsEntering] = React.useState(false);\n\n  // The `isEntering` state (which is used to determine the right CSS class to apply)\n  // is updated slightly (one animation frame) after the `requestedEnter` state is updated.\n  // Thanks to this, elements that are mounted will have their enter transition applied\n  // (if the `enterClassName` was applied when the element was mounted, the transition would not be fired).\n  React.useEffect(() => {\n    if (requestedEnter) {\n      requestAnimationFrame(() => {\n        setIsEntering(true);\n      });\n    } else {\n      setIsEntering(false);\n    }\n  }, [requestedEnter]);\n  const handleTransitionEnd = React.useCallback(event => {\n    if (!requestedEnter && (lastTransitionedPropertyOnExit == null || event.propertyName === lastTransitionedPropertyOnExit)) {\n      onExited();\n    }\n  }, [onExited, requestedEnter, lastTransitionedPropertyOnExit]);\n  return /*#__PURE__*/_jsx(\"div\", _extends({\n    onTransitionEnd: handleTransitionEnd,\n    className: clsx(className, isEntering ? enterClassName : exitClassName)\n  }, other, {\n    ref: forwardedRef,\n    children: children\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? CssTransition.propTypes = {\n  children: PropTypes.node,\n  className: PropTypes.string,\n  enterClassName: PropTypes.string,\n  exitClassName: PropTypes.string,\n  lastTransitionedPropertyOnEnter: PropTypes.string,\n  lastTransitionedPropertyOnExit: PropTypes.string\n} : void 0;\nexport { CssTransition };", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "PropTypes", "clsx", "useTransitionStateManager", "jsx", "_jsx", "CssTransition", "forwardRef", "props", "forwardedRef", "children", "className", "lastTransitionedPropertyOnExit", "enterClassName", "exitClassName", "other", "requestedEnter", "onExited", "isEntering", "setIsEntering", "useState", "useEffect", "requestAnimationFrame", "handleTransitionEnd", "useCallback", "event", "propertyName", "onTransitionEnd", "ref", "process", "env", "NODE_ENV", "propTypes", "node", "string", "lastTransitionedPropertyOnEnter"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/node_modules/@mui/base/Transitions/CssTransition.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"children\", \"className\", \"lastTransitionedPropertyOnExit\", \"enterClassName\", \"exitClassName\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport { useTransitionStateManager } from '../useTransition';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\n/**\n * A utility component that hooks up to the Base UI transitions API and\n * applies a CSS transition to its children when necessary.\n *\n * Demos:\n *\n * - [Transitions](https://mui.com/base-ui/react-transitions/)\n *\n * API:\n *\n * - [CssTransition API](https://mui.com/base-ui/react-transitions/components-api/#css-transition)\n */\nconst CssTransition = /*#__PURE__*/React.forwardRef(function CssTransition(props, forwardedRef) {\n  const {\n      children,\n      className,\n      lastTransitionedPropertyOnExit,\n      enterClassName,\n      exitClassName\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    requestedEnter,\n    onExited\n  } = useTransitionStateManager();\n  const [isEntering, setIsEntering] = React.useState(false);\n\n  // The `isEntering` state (which is used to determine the right CSS class to apply)\n  // is updated slightly (one animation frame) after the `requestedEnter` state is updated.\n  // Thanks to this, elements that are mounted will have their enter transition applied\n  // (if the `enterClassName` was applied when the element was mounted, the transition would not be fired).\n  React.useEffect(() => {\n    if (requestedEnter) {\n      requestAnimationFrame(() => {\n        setIsEntering(true);\n      });\n    } else {\n      setIsEntering(false);\n    }\n  }, [requestedEnter]);\n  const handleTransitionEnd = React.useCallback(event => {\n    if (!requestedEnter && (lastTransitionedPropertyOnExit == null || event.propertyName === lastTransitionedPropertyOnExit)) {\n      onExited();\n    }\n  }, [onExited, requestedEnter, lastTransitionedPropertyOnExit]);\n  return /*#__PURE__*/_jsx(\"div\", _extends({\n    onTransitionEnd: handleTransitionEnd,\n    className: clsx(className, isEntering ? enterClassName : exitClassName)\n  }, other, {\n    ref: forwardedRef,\n    children: children\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? CssTransition.propTypes = {\n  children: PropTypes.node,\n  className: PropTypes.string,\n  enterClassName: PropTypes.string,\n  exitClassName: PropTypes.string,\n  lastTransitionedPropertyOnEnter: PropTypes.string,\n  lastTransitionedPropertyOnExit: PropTypes.string\n} : void 0;\nexport { CssTransition };"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,UAAU,EAAE,WAAW,EAAE,gCAAgC,EAAE,gBAAgB,EAAE,eAAe,CAAC;AAChH,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,SAASC,yBAAyB,QAAQ,kBAAkB;AAC5D,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,aAAa,GAAG,aAAaN,KAAK,CAACO,UAAU,CAAC,SAASD,aAAaA,CAACE,KAAK,EAAEC,YAAY,EAAE;EAC9F,MAAM;MACFC,QAAQ;MACRC,SAAS;MACTC,8BAA8B;MAC9BC,cAAc;MACdC;IACF,CAAC,GAAGN,KAAK;IACTO,KAAK,GAAGjB,6BAA6B,CAACU,KAAK,EAAET,SAAS,CAAC;EACzD,MAAM;IACJiB,cAAc;IACdC;EACF,CAAC,GAAGd,yBAAyB,CAAC,CAAC;EAC/B,MAAM,CAACe,UAAU,EAAEC,aAAa,CAAC,GAAGnB,KAAK,CAACoB,QAAQ,CAAC,KAAK,CAAC;;EAEzD;EACA;EACA;EACA;EACApB,KAAK,CAACqB,SAAS,CAAC,MAAM;IACpB,IAAIL,cAAc,EAAE;MAClBM,qBAAqB,CAAC,MAAM;QAC1BH,aAAa,CAAC,IAAI,CAAC;MACrB,CAAC,CAAC;IACJ,CAAC,MAAM;MACLA,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC,EAAE,CAACH,cAAc,CAAC,CAAC;EACpB,MAAMO,mBAAmB,GAAGvB,KAAK,CAACwB,WAAW,CAACC,KAAK,IAAI;IACrD,IAAI,CAACT,cAAc,KAAKJ,8BAA8B,IAAI,IAAI,IAAIa,KAAK,CAACC,YAAY,KAAKd,8BAA8B,CAAC,EAAE;MACxHK,QAAQ,CAAC,CAAC;IACZ;EACF,CAAC,EAAE,CAACA,QAAQ,EAAED,cAAc,EAAEJ,8BAA8B,CAAC,CAAC;EAC9D,OAAO,aAAaP,IAAI,CAAC,KAAK,EAAER,QAAQ,CAAC;IACvC8B,eAAe,EAAEJ,mBAAmB;IACpCZ,SAAS,EAAET,IAAI,CAACS,SAAS,EAAEO,UAAU,GAAGL,cAAc,GAAGC,aAAa;EACxE,CAAC,EAAEC,KAAK,EAAE;IACRa,GAAG,EAAEnB,YAAY;IACjBC,QAAQ,EAAEA;EACZ,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFmB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGzB,aAAa,CAAC0B,SAAS,GAAG;EAChEtB,QAAQ,EAAET,SAAS,CAACgC,IAAI;EACxBtB,SAAS,EAAEV,SAAS,CAACiC,MAAM;EAC3BrB,cAAc,EAAEZ,SAAS,CAACiC,MAAM;EAChCpB,aAAa,EAAEb,SAAS,CAACiC,MAAM;EAC/BC,+BAA+B,EAAElC,SAAS,CAACiC,MAAM;EACjDtB,8BAA8B,EAAEX,SAAS,CAACiC;AAC5C,CAAC,GAAG,KAAK,CAAC;AACV,SAAS5B,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}