{"ast": null, "code": "import _objectWithoutProperties from \"C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nimport _objectSpread from \"C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nconst _excluded = [\"crossAxis\", \"alignment\", \"allowedPlacements\", \"autoAlignment\"],\n  _excluded2 = [\"mainAxis\", \"crossAxis\", \"fallbackPlacements\", \"fallbackStrategy\", \"fallbackAxisSideDirection\", \"flipAlignment\"],\n  _excluded3 = [\"strategy\"],\n  _excluded4 = [\"mainAxis\", \"crossAxis\", \"limiter\"],\n  _excluded5 = [\"apply\"];\nimport { getSideAxis, getAlignmentAxis, getAxisLength, getSide, getAlignment, evaluate, getPaddingObject, rectToClientRect, min, clamp, placements, getAlignmentSides, getOppositeAlignmentPlacement, getOppositePlacement, getExpandedPlacements, getOppositeAxisPlacements, sides, max, getOppositeAxis } from '@floating-ui/utils';\nexport { rectToClientRect } from '@floating-ui/utils';\nfunction computeCoordsFromPlacement(_ref, placement, rtl) {\n  let {\n    reference,\n    floating\n  } = _ref;\n  const sideAxis = getSideAxis(placement);\n  const alignmentAxis = getAlignmentAxis(placement);\n  const alignLength = getAxisLength(alignmentAxis);\n  const side = getSide(placement);\n  const isVertical = sideAxis === 'y';\n  const commonX = reference.x + reference.width / 2 - floating.width / 2;\n  const commonY = reference.y + reference.height / 2 - floating.height / 2;\n  const commonAlign = reference[alignLength] / 2 - floating[alignLength] / 2;\n  let coords;\n  switch (side) {\n    case 'top':\n      coords = {\n        x: commonX,\n        y: reference.y - floating.height\n      };\n      break;\n    case 'bottom':\n      coords = {\n        x: commonX,\n        y: reference.y + reference.height\n      };\n      break;\n    case 'right':\n      coords = {\n        x: reference.x + reference.width,\n        y: commonY\n      };\n      break;\n    case 'left':\n      coords = {\n        x: reference.x - floating.width,\n        y: commonY\n      };\n      break;\n    default:\n      coords = {\n        x: reference.x,\n        y: reference.y\n      };\n  }\n  switch (getAlignment(placement)) {\n    case 'start':\n      coords[alignmentAxis] -= commonAlign * (rtl && isVertical ? -1 : 1);\n      break;\n    case 'end':\n      coords[alignmentAxis] += commonAlign * (rtl && isVertical ? -1 : 1);\n      break;\n  }\n  return coords;\n}\n\n/**\n * Computes the `x` and `y` coordinates that will place the floating element\n * next to a given reference element.\n *\n * This export does not have any `platform` interface logic. You will need to\n * write one for the platform you are using Floating UI with.\n */\nconst computePosition = async (reference, floating, config) => {\n  const {\n    placement = 'bottom',\n    strategy = 'absolute',\n    middleware = [],\n    platform\n  } = config;\n  const validMiddleware = middleware.filter(Boolean);\n  const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(floating));\n  let rects = await platform.getElementRects({\n    reference,\n    floating,\n    strategy\n  });\n  let {\n    x,\n    y\n  } = computeCoordsFromPlacement(rects, placement, rtl);\n  let statefulPlacement = placement;\n  let middlewareData = {};\n  let resetCount = 0;\n  for (let i = 0; i < validMiddleware.length; i++) {\n    const {\n      name,\n      fn\n    } = validMiddleware[i];\n    const {\n      x: nextX,\n      y: nextY,\n      data,\n      reset\n    } = await fn({\n      x,\n      y,\n      initialPlacement: placement,\n      placement: statefulPlacement,\n      strategy,\n      middlewareData,\n      rects,\n      platform,\n      elements: {\n        reference,\n        floating\n      }\n    });\n    x = nextX != null ? nextX : x;\n    y = nextY != null ? nextY : y;\n    middlewareData = _objectSpread(_objectSpread({}, middlewareData), {}, {\n      [name]: _objectSpread(_objectSpread({}, middlewareData[name]), data)\n    });\n    if (reset && resetCount <= 50) {\n      resetCount++;\n      if (typeof reset === 'object') {\n        if (reset.placement) {\n          statefulPlacement = reset.placement;\n        }\n        if (reset.rects) {\n          rects = reset.rects === true ? await platform.getElementRects({\n            reference,\n            floating,\n            strategy\n          }) : reset.rects;\n        }\n        ({\n          x,\n          y\n        } = computeCoordsFromPlacement(rects, statefulPlacement, rtl));\n      }\n      i = -1;\n    }\n  }\n  return {\n    x,\n    y,\n    placement: statefulPlacement,\n    strategy,\n    middlewareData\n  };\n};\n\n/**\n * Resolves with an object of overflow side offsets that determine how much the\n * element is overflowing a given clipping boundary on each side.\n * - positive = overflowing the boundary by that number of pixels\n * - negative = how many pixels left before it will overflow\n * - 0 = lies flush with the boundary\n * @see https://floating-ui.com/docs/detectOverflow\n */\nasync function detectOverflow(state, options) {\n  var _await$platform$isEle;\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    x,\n    y,\n    platform,\n    rects,\n    elements,\n    strategy\n  } = state;\n  const {\n    boundary = 'clippingAncestors',\n    rootBoundary = 'viewport',\n    elementContext = 'floating',\n    altBoundary = false,\n    padding = 0\n  } = evaluate(options, state);\n  const paddingObject = getPaddingObject(padding);\n  const altContext = elementContext === 'floating' ? 'reference' : 'floating';\n  const element = elements[altBoundary ? altContext : elementContext];\n  const clippingClientRect = rectToClientRect(await platform.getClippingRect({\n    element: ((_await$platform$isEle = await (platform.isElement == null ? void 0 : platform.isElement(element))) != null ? _await$platform$isEle : true) ? element : element.contextElement || (await (platform.getDocumentElement == null ? void 0 : platform.getDocumentElement(elements.floating))),\n    boundary,\n    rootBoundary,\n    strategy\n  }));\n  const rect = elementContext === 'floating' ? {\n    x,\n    y,\n    width: rects.floating.width,\n    height: rects.floating.height\n  } : rects.reference;\n  const offsetParent = await (platform.getOffsetParent == null ? void 0 : platform.getOffsetParent(elements.floating));\n  const offsetScale = (await (platform.isElement == null ? void 0 : platform.isElement(offsetParent))) ? (await (platform.getScale == null ? void 0 : platform.getScale(offsetParent))) || {\n    x: 1,\n    y: 1\n  } : {\n    x: 1,\n    y: 1\n  };\n  const elementClientRect = rectToClientRect(platform.convertOffsetParentRelativeRectToViewportRelativeRect ? await platform.convertOffsetParentRelativeRectToViewportRelativeRect({\n    elements,\n    rect,\n    offsetParent,\n    strategy\n  }) : rect);\n  return {\n    top: (clippingClientRect.top - elementClientRect.top + paddingObject.top) / offsetScale.y,\n    bottom: (elementClientRect.bottom - clippingClientRect.bottom + paddingObject.bottom) / offsetScale.y,\n    left: (clippingClientRect.left - elementClientRect.left + paddingObject.left) / offsetScale.x,\n    right: (elementClientRect.right - clippingClientRect.right + paddingObject.right) / offsetScale.x\n  };\n}\n\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * @see https://floating-ui.com/docs/arrow\n */\nconst arrow = options => ({\n  name: 'arrow',\n  options,\n  async fn(state) {\n    const {\n      x,\n      y,\n      placement,\n      rects,\n      platform,\n      elements,\n      middlewareData\n    } = state;\n    // Since `element` is required, we don't Partial<> the type.\n    const {\n      element,\n      padding = 0\n    } = evaluate(options, state) || {};\n    if (element == null) {\n      return {};\n    }\n    const paddingObject = getPaddingObject(padding);\n    const coords = {\n      x,\n      y\n    };\n    const axis = getAlignmentAxis(placement);\n    const length = getAxisLength(axis);\n    const arrowDimensions = await platform.getDimensions(element);\n    const isYAxis = axis === 'y';\n    const minProp = isYAxis ? 'top' : 'left';\n    const maxProp = isYAxis ? 'bottom' : 'right';\n    const clientProp = isYAxis ? 'clientHeight' : 'clientWidth';\n    const endDiff = rects.reference[length] + rects.reference[axis] - coords[axis] - rects.floating[length];\n    const startDiff = coords[axis] - rects.reference[axis];\n    const arrowOffsetParent = await (platform.getOffsetParent == null ? void 0 : platform.getOffsetParent(element));\n    let clientSize = arrowOffsetParent ? arrowOffsetParent[clientProp] : 0;\n\n    // DOM platform can return `window` as the `offsetParent`.\n    if (!clientSize || !(await (platform.isElement == null ? void 0 : platform.isElement(arrowOffsetParent)))) {\n      clientSize = elements.floating[clientProp] || rects.floating[length];\n    }\n    const centerToReference = endDiff / 2 - startDiff / 2;\n\n    // If the padding is large enough that it causes the arrow to no longer be\n    // centered, modify the padding so that it is centered.\n    const largestPossiblePadding = clientSize / 2 - arrowDimensions[length] / 2 - 1;\n    const minPadding = min(paddingObject[minProp], largestPossiblePadding);\n    const maxPadding = min(paddingObject[maxProp], largestPossiblePadding);\n\n    // Make sure the arrow doesn't overflow the floating element if the center\n    // point is outside the floating element's bounds.\n    const min$1 = minPadding;\n    const max = clientSize - arrowDimensions[length] - maxPadding;\n    const center = clientSize / 2 - arrowDimensions[length] / 2 + centerToReference;\n    const offset = clamp(min$1, center, max);\n\n    // If the reference is small enough that the arrow's padding causes it to\n    // to point to nothing for an aligned placement, adjust the offset of the\n    // floating element itself. To ensure `shift()` continues to take action,\n    // a single reset is performed when this is true.\n    const shouldAddOffset = !middlewareData.arrow && getAlignment(placement) != null && center !== offset && rects.reference[length] / 2 - (center < min$1 ? minPadding : maxPadding) - arrowDimensions[length] / 2 < 0;\n    const alignmentOffset = shouldAddOffset ? center < min$1 ? center - min$1 : center - max : 0;\n    return {\n      [axis]: coords[axis] + alignmentOffset,\n      data: _objectSpread({\n        [axis]: offset,\n        centerOffset: center - offset - alignmentOffset\n      }, shouldAddOffset && {\n        alignmentOffset\n      }),\n      reset: shouldAddOffset\n    };\n  }\n});\nfunction getPlacementList(alignment, autoAlignment, allowedPlacements) {\n  const allowedPlacementsSortedByAlignment = alignment ? [...allowedPlacements.filter(placement => getAlignment(placement) === alignment), ...allowedPlacements.filter(placement => getAlignment(placement) !== alignment)] : allowedPlacements.filter(placement => getSide(placement) === placement);\n  return allowedPlacementsSortedByAlignment.filter(placement => {\n    if (alignment) {\n      return getAlignment(placement) === alignment || (autoAlignment ? getOppositeAlignmentPlacement(placement) !== placement : false);\n    }\n    return true;\n  });\n}\n/**\n * Optimizes the visibility of the floating element by choosing the placement\n * that has the most space available automatically, without needing to specify a\n * preferred placement. Alternative to `flip`.\n * @see https://floating-ui.com/docs/autoPlacement\n */\nconst autoPlacement = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'autoPlacement',\n    options,\n    async fn(state) {\n      var _middlewareData$autoP, _middlewareData$autoP2, _placementsThatFitOnE;\n      const {\n        rects,\n        middlewareData,\n        placement,\n        platform,\n        elements\n      } = state;\n      const _evaluate = evaluate(options, state),\n        {\n          crossAxis = false,\n          alignment,\n          allowedPlacements = placements,\n          autoAlignment = true\n        } = _evaluate,\n        detectOverflowOptions = _objectWithoutProperties(_evaluate, _excluded);\n      const placements$1 = alignment !== undefined || allowedPlacements === placements ? getPlacementList(alignment || null, autoAlignment, allowedPlacements) : allowedPlacements;\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const currentIndex = ((_middlewareData$autoP = middlewareData.autoPlacement) == null ? void 0 : _middlewareData$autoP.index) || 0;\n      const currentPlacement = placements$1[currentIndex];\n      if (currentPlacement == null) {\n        return {};\n      }\n      const alignmentSides = getAlignmentSides(currentPlacement, rects, await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating)));\n\n      // Make `computeCoords` start from the right place.\n      if (placement !== currentPlacement) {\n        return {\n          reset: {\n            placement: placements$1[0]\n          }\n        };\n      }\n      const currentOverflows = [overflow[getSide(currentPlacement)], overflow[alignmentSides[0]], overflow[alignmentSides[1]]];\n      const allOverflows = [...(((_middlewareData$autoP2 = middlewareData.autoPlacement) == null ? void 0 : _middlewareData$autoP2.overflows) || []), {\n        placement: currentPlacement,\n        overflows: currentOverflows\n      }];\n      const nextPlacement = placements$1[currentIndex + 1];\n\n      // There are more placements to check.\n      if (nextPlacement) {\n        return {\n          data: {\n            index: currentIndex + 1,\n            overflows: allOverflows\n          },\n          reset: {\n            placement: nextPlacement\n          }\n        };\n      }\n      const placementsSortedByMostSpace = allOverflows.map(d => {\n        const alignment = getAlignment(d.placement);\n        return [d.placement, alignment && crossAxis ?\n        // Check along the mainAxis and main crossAxis side.\n        d.overflows.slice(0, 2).reduce((acc, v) => acc + v, 0) :\n        // Check only the mainAxis.\n        d.overflows[0], d.overflows];\n      }).sort((a, b) => a[1] - b[1]);\n      const placementsThatFitOnEachSide = placementsSortedByMostSpace.filter(d => d[2].slice(0,\n      // Aligned placements should not check their opposite crossAxis\n      // side.\n      getAlignment(d[0]) ? 2 : 3).every(v => v <= 0));\n      const resetPlacement = ((_placementsThatFitOnE = placementsThatFitOnEachSide[0]) == null ? void 0 : _placementsThatFitOnE[0]) || placementsSortedByMostSpace[0][0];\n      if (resetPlacement !== placement) {\n        return {\n          data: {\n            index: currentIndex + 1,\n            overflows: allOverflows\n          },\n          reset: {\n            placement: resetPlacement\n          }\n        };\n      }\n      return {};\n    }\n  };\n};\n\n/**\n * Optimizes the visibility of the floating element by flipping the `placement`\n * in order to keep it in view when the preferred placement(s) will overflow the\n * clipping boundary. Alternative to `autoPlacement`.\n * @see https://floating-ui.com/docs/flip\n */\nconst flip = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'flip',\n    options,\n    async fn(state) {\n      var _middlewareData$arrow, _middlewareData$flip;\n      const {\n        placement,\n        middlewareData,\n        rects,\n        initialPlacement,\n        platform,\n        elements\n      } = state;\n      const _evaluate2 = evaluate(options, state),\n        {\n          mainAxis: checkMainAxis = true,\n          crossAxis: checkCrossAxis = true,\n          fallbackPlacements: specifiedFallbackPlacements,\n          fallbackStrategy = 'bestFit',\n          fallbackAxisSideDirection = 'none',\n          flipAlignment = true\n        } = _evaluate2,\n        detectOverflowOptions = _objectWithoutProperties(_evaluate2, _excluded2);\n\n      // If a reset by the arrow was caused due to an alignment offset being\n      // added, we should skip any logic now since `flip()` has already done its\n      // work.\n      // https://github.com/floating-ui/floating-ui/issues/2549#issuecomment-1719601643\n      if ((_middlewareData$arrow = middlewareData.arrow) != null && _middlewareData$arrow.alignmentOffset) {\n        return {};\n      }\n      const side = getSide(placement);\n      const initialSideAxis = getSideAxis(initialPlacement);\n      const isBasePlacement = getSide(initialPlacement) === initialPlacement;\n      const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating));\n      const fallbackPlacements = specifiedFallbackPlacements || (isBasePlacement || !flipAlignment ? [getOppositePlacement(initialPlacement)] : getExpandedPlacements(initialPlacement));\n      const hasFallbackAxisSideDirection = fallbackAxisSideDirection !== 'none';\n      if (!specifiedFallbackPlacements && hasFallbackAxisSideDirection) {\n        fallbackPlacements.push(...getOppositeAxisPlacements(initialPlacement, flipAlignment, fallbackAxisSideDirection, rtl));\n      }\n      const placements = [initialPlacement, ...fallbackPlacements];\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const overflows = [];\n      let overflowsData = ((_middlewareData$flip = middlewareData.flip) == null ? void 0 : _middlewareData$flip.overflows) || [];\n      if (checkMainAxis) {\n        overflows.push(overflow[side]);\n      }\n      if (checkCrossAxis) {\n        const sides = getAlignmentSides(placement, rects, rtl);\n        overflows.push(overflow[sides[0]], overflow[sides[1]]);\n      }\n      overflowsData = [...overflowsData, {\n        placement,\n        overflows\n      }];\n\n      // One or more sides is overflowing.\n      if (!overflows.every(side => side <= 0)) {\n        var _middlewareData$flip2, _overflowsData$filter;\n        const nextIndex = (((_middlewareData$flip2 = middlewareData.flip) == null ? void 0 : _middlewareData$flip2.index) || 0) + 1;\n        const nextPlacement = placements[nextIndex];\n        if (nextPlacement) {\n          var _overflowsData$;\n          const ignoreCrossAxisOverflow = checkCrossAxis === 'alignment' ? initialSideAxis !== getSideAxis(nextPlacement) : false;\n          const hasInitialMainAxisOverflow = ((_overflowsData$ = overflowsData[0]) == null ? void 0 : _overflowsData$.overflows[0]) > 0;\n          if (!ignoreCrossAxisOverflow || hasInitialMainAxisOverflow) {\n            // Try next placement and re-run the lifecycle.\n            return {\n              data: {\n                index: nextIndex,\n                overflows: overflowsData\n              },\n              reset: {\n                placement: nextPlacement\n              }\n            };\n          }\n        }\n\n        // First, find the candidates that fit on the mainAxis side of overflow,\n        // then find the placement that fits the best on the main crossAxis side.\n        let resetPlacement = (_overflowsData$filter = overflowsData.filter(d => d.overflows[0] <= 0).sort((a, b) => a.overflows[1] - b.overflows[1])[0]) == null ? void 0 : _overflowsData$filter.placement;\n\n        // Otherwise fallback.\n        if (!resetPlacement) {\n          switch (fallbackStrategy) {\n            case 'bestFit':\n              {\n                var _overflowsData$filter2;\n                const placement = (_overflowsData$filter2 = overflowsData.filter(d => {\n                  if (hasFallbackAxisSideDirection) {\n                    const currentSideAxis = getSideAxis(d.placement);\n                    return currentSideAxis === initialSideAxis ||\n                    // Create a bias to the `y` side axis due to horizontal\n                    // reading directions favoring greater width.\n                    currentSideAxis === 'y';\n                  }\n                  return true;\n                }).map(d => [d.placement, d.overflows.filter(overflow => overflow > 0).reduce((acc, overflow) => acc + overflow, 0)]).sort((a, b) => a[1] - b[1])[0]) == null ? void 0 : _overflowsData$filter2[0];\n                if (placement) {\n                  resetPlacement = placement;\n                }\n                break;\n              }\n            case 'initialPlacement':\n              resetPlacement = initialPlacement;\n              break;\n          }\n        }\n        if (placement !== resetPlacement) {\n          return {\n            reset: {\n              placement: resetPlacement\n            }\n          };\n        }\n      }\n      return {};\n    }\n  };\n};\nfunction getSideOffsets(overflow, rect) {\n  return {\n    top: overflow.top - rect.height,\n    right: overflow.right - rect.width,\n    bottom: overflow.bottom - rect.height,\n    left: overflow.left - rect.width\n  };\n}\nfunction isAnySideFullyClipped(overflow) {\n  return sides.some(side => overflow[side] >= 0);\n}\n/**\n * Provides data to hide the floating element in applicable situations, such as\n * when it is not in the same clipping context as the reference element.\n * @see https://floating-ui.com/docs/hide\n */\nconst hide = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'hide',\n    options,\n    async fn(state) {\n      const {\n        rects\n      } = state;\n      const _evaluate3 = evaluate(options, state),\n        {\n          strategy = 'referenceHidden'\n        } = _evaluate3,\n        detectOverflowOptions = _objectWithoutProperties(_evaluate3, _excluded3);\n      switch (strategy) {\n        case 'referenceHidden':\n          {\n            const overflow = await detectOverflow(state, _objectSpread(_objectSpread({}, detectOverflowOptions), {}, {\n              elementContext: 'reference'\n            }));\n            const offsets = getSideOffsets(overflow, rects.reference);\n            return {\n              data: {\n                referenceHiddenOffsets: offsets,\n                referenceHidden: isAnySideFullyClipped(offsets)\n              }\n            };\n          }\n        case 'escaped':\n          {\n            const overflow = await detectOverflow(state, _objectSpread(_objectSpread({}, detectOverflowOptions), {}, {\n              altBoundary: true\n            }));\n            const offsets = getSideOffsets(overflow, rects.floating);\n            return {\n              data: {\n                escapedOffsets: offsets,\n                escaped: isAnySideFullyClipped(offsets)\n              }\n            };\n          }\n        default:\n          {\n            return {};\n          }\n      }\n    }\n  };\n};\nfunction getBoundingRect(rects) {\n  const minX = min(...rects.map(rect => rect.left));\n  const minY = min(...rects.map(rect => rect.top));\n  const maxX = max(...rects.map(rect => rect.right));\n  const maxY = max(...rects.map(rect => rect.bottom));\n  return {\n    x: minX,\n    y: minY,\n    width: maxX - minX,\n    height: maxY - minY\n  };\n}\nfunction getRectsByLine(rects) {\n  const sortedRects = rects.slice().sort((a, b) => a.y - b.y);\n  const groups = [];\n  let prevRect = null;\n  for (let i = 0; i < sortedRects.length; i++) {\n    const rect = sortedRects[i];\n    if (!prevRect || rect.y - prevRect.y > prevRect.height / 2) {\n      groups.push([rect]);\n    } else {\n      groups[groups.length - 1].push(rect);\n    }\n    prevRect = rect;\n  }\n  return groups.map(rect => rectToClientRect(getBoundingRect(rect)));\n}\n/**\n * Provides improved positioning for inline reference elements that can span\n * over multiple lines, such as hyperlinks or range selections.\n * @see https://floating-ui.com/docs/inline\n */\nconst inline = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'inline',\n    options,\n    async fn(state) {\n      const {\n        placement,\n        elements,\n        rects,\n        platform,\n        strategy\n      } = state;\n      // A MouseEvent's client{X,Y} coords can be up to 2 pixels off a\n      // ClientRect's bounds, despite the event listener being triggered. A\n      // padding of 2 seems to handle this issue.\n      const {\n        padding = 2,\n        x,\n        y\n      } = evaluate(options, state);\n      const nativeClientRects = Array.from((await (platform.getClientRects == null ? void 0 : platform.getClientRects(elements.reference))) || []);\n      const clientRects = getRectsByLine(nativeClientRects);\n      const fallback = rectToClientRect(getBoundingRect(nativeClientRects));\n      const paddingObject = getPaddingObject(padding);\n      function getBoundingClientRect() {\n        // There are two rects and they are disjoined.\n        if (clientRects.length === 2 && clientRects[0].left > clientRects[1].right && x != null && y != null) {\n          // Find the first rect in which the point is fully inside.\n          return clientRects.find(rect => x > rect.left - paddingObject.left && x < rect.right + paddingObject.right && y > rect.top - paddingObject.top && y < rect.bottom + paddingObject.bottom) || fallback;\n        }\n\n        // There are 2 or more connected rects.\n        if (clientRects.length >= 2) {\n          if (getSideAxis(placement) === 'y') {\n            const firstRect = clientRects[0];\n            const lastRect = clientRects[clientRects.length - 1];\n            const isTop = getSide(placement) === 'top';\n            const top = firstRect.top;\n            const bottom = lastRect.bottom;\n            const left = isTop ? firstRect.left : lastRect.left;\n            const right = isTop ? firstRect.right : lastRect.right;\n            const width = right - left;\n            const height = bottom - top;\n            return {\n              top,\n              bottom,\n              left,\n              right,\n              width,\n              height,\n              x: left,\n              y: top\n            };\n          }\n          const isLeftSide = getSide(placement) === 'left';\n          const maxRight = max(...clientRects.map(rect => rect.right));\n          const minLeft = min(...clientRects.map(rect => rect.left));\n          const measureRects = clientRects.filter(rect => isLeftSide ? rect.left === minLeft : rect.right === maxRight);\n          const top = measureRects[0].top;\n          const bottom = measureRects[measureRects.length - 1].bottom;\n          const left = minLeft;\n          const right = maxRight;\n          const width = right - left;\n          const height = bottom - top;\n          return {\n            top,\n            bottom,\n            left,\n            right,\n            width,\n            height,\n            x: left,\n            y: top\n          };\n        }\n        return fallback;\n      }\n      const resetRects = await platform.getElementRects({\n        reference: {\n          getBoundingClientRect\n        },\n        floating: elements.floating,\n        strategy\n      });\n      if (rects.reference.x !== resetRects.reference.x || rects.reference.y !== resetRects.reference.y || rects.reference.width !== resetRects.reference.width || rects.reference.height !== resetRects.reference.height) {\n        return {\n          reset: {\n            rects: resetRects\n          }\n        };\n      }\n      return {};\n    }\n  };\n};\n\n// For type backwards-compatibility, the `OffsetOptions` type was also\n// Derivable.\n\nasync function convertValueToCoords(state, options) {\n  const {\n    placement,\n    platform,\n    elements\n  } = state;\n  const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating));\n  const side = getSide(placement);\n  const alignment = getAlignment(placement);\n  const isVertical = getSideAxis(placement) === 'y';\n  const mainAxisMulti = ['left', 'top'].includes(side) ? -1 : 1;\n  const crossAxisMulti = rtl && isVertical ? -1 : 1;\n  const rawValue = evaluate(options, state);\n\n  // eslint-disable-next-line prefer-const\n  let {\n    mainAxis,\n    crossAxis,\n    alignmentAxis\n  } = typeof rawValue === 'number' ? {\n    mainAxis: rawValue,\n    crossAxis: 0,\n    alignmentAxis: null\n  } : {\n    mainAxis: rawValue.mainAxis || 0,\n    crossAxis: rawValue.crossAxis || 0,\n    alignmentAxis: rawValue.alignmentAxis\n  };\n  if (alignment && typeof alignmentAxis === 'number') {\n    crossAxis = alignment === 'end' ? alignmentAxis * -1 : alignmentAxis;\n  }\n  return isVertical ? {\n    x: crossAxis * crossAxisMulti,\n    y: mainAxis * mainAxisMulti\n  } : {\n    x: mainAxis * mainAxisMulti,\n    y: crossAxis * crossAxisMulti\n  };\n}\n\n/**\n * Modifies the placement by translating the floating element along the\n * specified axes.\n * A number (shorthand for `mainAxis` or distance), or an axes configuration\n * object may be passed.\n * @see https://floating-ui.com/docs/offset\n */\nconst offset = function (options) {\n  if (options === void 0) {\n    options = 0;\n  }\n  return {\n    name: 'offset',\n    options,\n    async fn(state) {\n      var _middlewareData$offse, _middlewareData$arrow;\n      const {\n        x,\n        y,\n        placement,\n        middlewareData\n      } = state;\n      const diffCoords = await convertValueToCoords(state, options);\n\n      // If the placement is the same and the arrow caused an alignment offset\n      // then we don't need to change the positioning coordinates.\n      if (placement === ((_middlewareData$offse = middlewareData.offset) == null ? void 0 : _middlewareData$offse.placement) && (_middlewareData$arrow = middlewareData.arrow) != null && _middlewareData$arrow.alignmentOffset) {\n        return {};\n      }\n      return {\n        x: x + diffCoords.x,\n        y: y + diffCoords.y,\n        data: _objectSpread(_objectSpread({}, diffCoords), {}, {\n          placement\n        })\n      };\n    }\n  };\n};\n\n/**\n * Optimizes the visibility of the floating element by shifting it in order to\n * keep it in view when it will overflow the clipping boundary.\n * @see https://floating-ui.com/docs/shift\n */\nconst shift = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'shift',\n    options,\n    async fn(state) {\n      const {\n        x,\n        y,\n        placement\n      } = state;\n      const _evaluate4 = evaluate(options, state),\n        {\n          mainAxis: checkMainAxis = true,\n          crossAxis: checkCrossAxis = false,\n          limiter = {\n            fn: _ref => {\n              let {\n                x,\n                y\n              } = _ref;\n              return {\n                x,\n                y\n              };\n            }\n          }\n        } = _evaluate4,\n        detectOverflowOptions = _objectWithoutProperties(_evaluate4, _excluded4);\n      const coords = {\n        x,\n        y\n      };\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const crossAxis = getSideAxis(getSide(placement));\n      const mainAxis = getOppositeAxis(crossAxis);\n      let mainAxisCoord = coords[mainAxis];\n      let crossAxisCoord = coords[crossAxis];\n      if (checkMainAxis) {\n        const minSide = mainAxis === 'y' ? 'top' : 'left';\n        const maxSide = mainAxis === 'y' ? 'bottom' : 'right';\n        const min = mainAxisCoord + overflow[minSide];\n        const max = mainAxisCoord - overflow[maxSide];\n        mainAxisCoord = clamp(min, mainAxisCoord, max);\n      }\n      if (checkCrossAxis) {\n        const minSide = crossAxis === 'y' ? 'top' : 'left';\n        const maxSide = crossAxis === 'y' ? 'bottom' : 'right';\n        const min = crossAxisCoord + overflow[minSide];\n        const max = crossAxisCoord - overflow[maxSide];\n        crossAxisCoord = clamp(min, crossAxisCoord, max);\n      }\n      const limitedCoords = limiter.fn(_objectSpread(_objectSpread({}, state), {}, {\n        [mainAxis]: mainAxisCoord,\n        [crossAxis]: crossAxisCoord\n      }));\n      return _objectSpread(_objectSpread({}, limitedCoords), {}, {\n        data: {\n          x: limitedCoords.x - x,\n          y: limitedCoords.y - y,\n          enabled: {\n            [mainAxis]: checkMainAxis,\n            [crossAxis]: checkCrossAxis\n          }\n        }\n      });\n    }\n  };\n};\n/**\n * Built-in `limiter` that will stop `shift()` at a certain point.\n */\nconst limitShift = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    options,\n    fn(state) {\n      const {\n        x,\n        y,\n        placement,\n        rects,\n        middlewareData\n      } = state;\n      const {\n        offset = 0,\n        mainAxis: checkMainAxis = true,\n        crossAxis: checkCrossAxis = true\n      } = evaluate(options, state);\n      const coords = {\n        x,\n        y\n      };\n      const crossAxis = getSideAxis(placement);\n      const mainAxis = getOppositeAxis(crossAxis);\n      let mainAxisCoord = coords[mainAxis];\n      let crossAxisCoord = coords[crossAxis];\n      const rawOffset = evaluate(offset, state);\n      const computedOffset = typeof rawOffset === 'number' ? {\n        mainAxis: rawOffset,\n        crossAxis: 0\n      } : _objectSpread({\n        mainAxis: 0,\n        crossAxis: 0\n      }, rawOffset);\n      if (checkMainAxis) {\n        const len = mainAxis === 'y' ? 'height' : 'width';\n        const limitMin = rects.reference[mainAxis] - rects.floating[len] + computedOffset.mainAxis;\n        const limitMax = rects.reference[mainAxis] + rects.reference[len] - computedOffset.mainAxis;\n        if (mainAxisCoord < limitMin) {\n          mainAxisCoord = limitMin;\n        } else if (mainAxisCoord > limitMax) {\n          mainAxisCoord = limitMax;\n        }\n      }\n      if (checkCrossAxis) {\n        var _middlewareData$offse, _middlewareData$offse2;\n        const len = mainAxis === 'y' ? 'width' : 'height';\n        const isOriginSide = ['top', 'left'].includes(getSide(placement));\n        const limitMin = rects.reference[crossAxis] - rects.floating[len] + (isOriginSide ? ((_middlewareData$offse = middlewareData.offset) == null ? void 0 : _middlewareData$offse[crossAxis]) || 0 : 0) + (isOriginSide ? 0 : computedOffset.crossAxis);\n        const limitMax = rects.reference[crossAxis] + rects.reference[len] + (isOriginSide ? 0 : ((_middlewareData$offse2 = middlewareData.offset) == null ? void 0 : _middlewareData$offse2[crossAxis]) || 0) - (isOriginSide ? computedOffset.crossAxis : 0);\n        if (crossAxisCoord < limitMin) {\n          crossAxisCoord = limitMin;\n        } else if (crossAxisCoord > limitMax) {\n          crossAxisCoord = limitMax;\n        }\n      }\n      return {\n        [mainAxis]: mainAxisCoord,\n        [crossAxis]: crossAxisCoord\n      };\n    }\n  };\n};\n\n/**\n * Provides data that allows you to change the size of the floating element —\n * for instance, prevent it from overflowing the clipping boundary or match the\n * width of the reference element.\n * @see https://floating-ui.com/docs/size\n */\nconst size = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'size',\n    options,\n    async fn(state) {\n      var _state$middlewareData, _state$middlewareData2;\n      const {\n        placement,\n        rects,\n        platform,\n        elements\n      } = state;\n      const _evaluate5 = evaluate(options, state),\n        {\n          apply = () => {}\n        } = _evaluate5,\n        detectOverflowOptions = _objectWithoutProperties(_evaluate5, _excluded5);\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const side = getSide(placement);\n      const alignment = getAlignment(placement);\n      const isYAxis = getSideAxis(placement) === 'y';\n      const {\n        width,\n        height\n      } = rects.floating;\n      let heightSide;\n      let widthSide;\n      if (side === 'top' || side === 'bottom') {\n        heightSide = side;\n        widthSide = alignment === ((await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating))) ? 'start' : 'end') ? 'left' : 'right';\n      } else {\n        widthSide = side;\n        heightSide = alignment === 'end' ? 'top' : 'bottom';\n      }\n      const maximumClippingHeight = height - overflow.top - overflow.bottom;\n      const maximumClippingWidth = width - overflow.left - overflow.right;\n      const overflowAvailableHeight = min(height - overflow[heightSide], maximumClippingHeight);\n      const overflowAvailableWidth = min(width - overflow[widthSide], maximumClippingWidth);\n      const noShift = !state.middlewareData.shift;\n      let availableHeight = overflowAvailableHeight;\n      let availableWidth = overflowAvailableWidth;\n      if ((_state$middlewareData = state.middlewareData.shift) != null && _state$middlewareData.enabled.x) {\n        availableWidth = maximumClippingWidth;\n      }\n      if ((_state$middlewareData2 = state.middlewareData.shift) != null && _state$middlewareData2.enabled.y) {\n        availableHeight = maximumClippingHeight;\n      }\n      if (noShift && !alignment) {\n        const xMin = max(overflow.left, 0);\n        const xMax = max(overflow.right, 0);\n        const yMin = max(overflow.top, 0);\n        const yMax = max(overflow.bottom, 0);\n        if (isYAxis) {\n          availableWidth = width - 2 * (xMin !== 0 || xMax !== 0 ? xMin + xMax : max(overflow.left, overflow.right));\n        } else {\n          availableHeight = height - 2 * (yMin !== 0 || yMax !== 0 ? yMin + yMax : max(overflow.top, overflow.bottom));\n        }\n      }\n      await apply(_objectSpread(_objectSpread({}, state), {}, {\n        availableWidth,\n        availableHeight\n      }));\n      const nextDimensions = await platform.getDimensions(elements.floating);\n      if (width !== nextDimensions.width || height !== nextDimensions.height) {\n        return {\n          reset: {\n            rects: true\n          }\n        };\n      }\n      return {};\n    }\n  };\n};\nexport { arrow, autoPlacement, computePosition, detectOverflow, flip, hide, inline, limitShift, offset, shift, size };", "map": {"version": 3, "names": ["getSideAxis", "getAlignmentAxis", "getAxisLength", "getSide", "getAlignment", "evaluate", "getPaddingObject", "rectToClientRect", "min", "clamp", "placements", "getAlignmentSides", "getOppositeAlignmentPlacement", "getOppositePlacement", "getExpandedPlacements", "getOppositeAxisPlacements", "sides", "max", "getOppositeAxis", "computeCoordsFromPlacement", "_ref", "placement", "rtl", "reference", "floating", "sideAxis", "alignmentAxis", "align<PERSON><PERSON><PERSON>", "side", "isVertical", "commonX", "x", "width", "commonY", "y", "height", "commonAlign", "coords", "computePosition", "config", "strategy", "middleware", "platform", "validMiddleware", "filter", "Boolean", "isRTL", "rects", "getElementRects", "statefulPlacement", "middlewareData", "resetCount", "i", "length", "name", "fn", "nextX", "nextY", "data", "reset", "initialPlacement", "elements", "_objectSpread", "detectOverflow", "state", "options", "_await$platform$isEle", "boundary", "rootBoundary", "elementContext", "altBoundary", "padding", "paddingObject", "altContext", "element", "clippingClientRect", "getClippingRect", "isElement", "contextElement", "getDocumentElement", "rect", "offsetParent", "getOffsetParent", "offsetScale", "getScale", "elementClientRect", "convertOffsetParentRelativeRectToViewportRelativeRect", "top", "bottom", "left", "right", "arrow", "axis", "arrowDimensions", "getDimensions", "isYAxis", "minProp", "maxProp", "clientProp", "endDiff", "startDiff", "arrowOffsetParent", "clientSize", "centerToReference", "largestPossiblePadding", "minPadding", "maxPadding", "min$1", "center", "offset", "shouldAddOffset", "alignmentOffset", "centerOffset", "getPlacementList", "alignment", "autoAlignment", "allowedPlacements", "allowedPlacementsSortedByAlignment", "autoPlacement", "_middlewareData$autoP", "_middlewareData$autoP2", "_placementsThatFitOnE", "_evaluate", "crossAxis", "detectOverflowOptions", "_objectWithoutProperties", "_excluded", "placements$1", "undefined", "overflow", "currentIndex", "index", "currentPlacement", "alignmentSides", "currentOverflows", "allOverflows", "overflows", "nextPlacement", "placementsSortedByMostSpace", "map", "d", "slice", "reduce", "acc", "v", "sort", "a", "b", "placementsThatFitOnEachSide", "every", "resetPlacement", "flip", "_middlewareData$arrow", "_middlewareData$flip", "_evaluate2", "mainAxis", "checkMainAxis", "checkCrossAxis", "fallbackPlacements", "specifiedFallbackPlacements", "fallbackStrategy", "fallbackAxisSideDirection", "flipAlignment", "_excluded2", "initialSideAxis", "isBasePlacement", "hasFallbackAxisSideDirection", "push", "overflowsData", "_middlewareData$flip2", "_overflowsData$filter", "nextIndex", "_overflowsData$", "ignoreCrossAxisOverflow", "hasInitialMainAxisOverflow", "_overflowsData$filter2", "currentSideAxis", "getSideOffsets", "isAnySideFullyClipped", "some", "hide", "_evaluate3", "_excluded3", "offsets", "referenceHiddenOffsets", "referenceHidden", "escapedOffsets", "escaped", "getBoundingRect", "minX", "minY", "maxX", "maxY", "getRectsByLine", "sortedRects", "groups", "prevRect", "inline", "nativeClientRects", "Array", "from", "getClientRects", "clientRects", "fallback", "getBoundingClientRect", "find", "firstRect", "lastRect", "isTop", "isLeftSide", "maxRight", "minLeft", "measureRects", "resetRects", "convertValueToCoords", "mainAxisMulti", "includes", "crossAxisMulti", "rawValue", "_middlewareData$offse", "diffCoords", "shift", "_evaluate4", "limiter", "_excluded4", "mainAxisCoord", "crossAxisCoord", "minSide", "maxSide", "limitedCoords", "enabled", "limitShift", "rawOffset", "computedOffset", "len", "limitMin", "limitMax", "_middlewareData$offse2", "isOriginSide", "size", "_state$middlewareData", "_state$middlewareData2", "_evaluate5", "apply", "_excluded5", "heightSide", "widthSide", "maximumClippingHeight", "maximumClippingWidth", "overflowAvailableHeight", "overflowAvailableWidth", "noShift", "availableHeight", "availableWidth", "xMin", "xMax", "yMin", "yMax", "nextDimensions"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/node_modules/@floating-ui/core/dist/floating-ui.core.mjs"], "sourcesContent": ["import { getSideAxis, getAlignmentAxis, getAxisLength, getSide, getAlignment, evaluate, getPaddingObject, rectToClientRect, min, clamp, placements, getAlignmentSides, getOppositeAlignmentPlacement, getOppositePlacement, getExpandedPlacements, getOppositeAxisPlacements, sides, max, getOppositeAxis } from '@floating-ui/utils';\nexport { rectToClientRect } from '@floating-ui/utils';\n\nfunction computeCoordsFromPlacement(_ref, placement, rtl) {\n  let {\n    reference,\n    floating\n  } = _ref;\n  const sideAxis = getSideAxis(placement);\n  const alignmentAxis = getAlignmentAxis(placement);\n  const alignLength = getAxisLength(alignmentAxis);\n  const side = getSide(placement);\n  const isVertical = sideAxis === 'y';\n  const commonX = reference.x + reference.width / 2 - floating.width / 2;\n  const commonY = reference.y + reference.height / 2 - floating.height / 2;\n  const commonAlign = reference[alignLength] / 2 - floating[alignLength] / 2;\n  let coords;\n  switch (side) {\n    case 'top':\n      coords = {\n        x: commonX,\n        y: reference.y - floating.height\n      };\n      break;\n    case 'bottom':\n      coords = {\n        x: commonX,\n        y: reference.y + reference.height\n      };\n      break;\n    case 'right':\n      coords = {\n        x: reference.x + reference.width,\n        y: commonY\n      };\n      break;\n    case 'left':\n      coords = {\n        x: reference.x - floating.width,\n        y: commonY\n      };\n      break;\n    default:\n      coords = {\n        x: reference.x,\n        y: reference.y\n      };\n  }\n  switch (getAlignment(placement)) {\n    case 'start':\n      coords[alignmentAxis] -= commonAlign * (rtl && isVertical ? -1 : 1);\n      break;\n    case 'end':\n      coords[alignmentAxis] += commonAlign * (rtl && isVertical ? -1 : 1);\n      break;\n  }\n  return coords;\n}\n\n/**\n * Computes the `x` and `y` coordinates that will place the floating element\n * next to a given reference element.\n *\n * This export does not have any `platform` interface logic. You will need to\n * write one for the platform you are using Floating UI with.\n */\nconst computePosition = async (reference, floating, config) => {\n  const {\n    placement = 'bottom',\n    strategy = 'absolute',\n    middleware = [],\n    platform\n  } = config;\n  const validMiddleware = middleware.filter(Boolean);\n  const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(floating));\n  let rects = await platform.getElementRects({\n    reference,\n    floating,\n    strategy\n  });\n  let {\n    x,\n    y\n  } = computeCoordsFromPlacement(rects, placement, rtl);\n  let statefulPlacement = placement;\n  let middlewareData = {};\n  let resetCount = 0;\n  for (let i = 0; i < validMiddleware.length; i++) {\n    const {\n      name,\n      fn\n    } = validMiddleware[i];\n    const {\n      x: nextX,\n      y: nextY,\n      data,\n      reset\n    } = await fn({\n      x,\n      y,\n      initialPlacement: placement,\n      placement: statefulPlacement,\n      strategy,\n      middlewareData,\n      rects,\n      platform,\n      elements: {\n        reference,\n        floating\n      }\n    });\n    x = nextX != null ? nextX : x;\n    y = nextY != null ? nextY : y;\n    middlewareData = {\n      ...middlewareData,\n      [name]: {\n        ...middlewareData[name],\n        ...data\n      }\n    };\n    if (reset && resetCount <= 50) {\n      resetCount++;\n      if (typeof reset === 'object') {\n        if (reset.placement) {\n          statefulPlacement = reset.placement;\n        }\n        if (reset.rects) {\n          rects = reset.rects === true ? await platform.getElementRects({\n            reference,\n            floating,\n            strategy\n          }) : reset.rects;\n        }\n        ({\n          x,\n          y\n        } = computeCoordsFromPlacement(rects, statefulPlacement, rtl));\n      }\n      i = -1;\n    }\n  }\n  return {\n    x,\n    y,\n    placement: statefulPlacement,\n    strategy,\n    middlewareData\n  };\n};\n\n/**\n * Resolves with an object of overflow side offsets that determine how much the\n * element is overflowing a given clipping boundary on each side.\n * - positive = overflowing the boundary by that number of pixels\n * - negative = how many pixels left before it will overflow\n * - 0 = lies flush with the boundary\n * @see https://floating-ui.com/docs/detectOverflow\n */\nasync function detectOverflow(state, options) {\n  var _await$platform$isEle;\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    x,\n    y,\n    platform,\n    rects,\n    elements,\n    strategy\n  } = state;\n  const {\n    boundary = 'clippingAncestors',\n    rootBoundary = 'viewport',\n    elementContext = 'floating',\n    altBoundary = false,\n    padding = 0\n  } = evaluate(options, state);\n  const paddingObject = getPaddingObject(padding);\n  const altContext = elementContext === 'floating' ? 'reference' : 'floating';\n  const element = elements[altBoundary ? altContext : elementContext];\n  const clippingClientRect = rectToClientRect(await platform.getClippingRect({\n    element: ((_await$platform$isEle = await (platform.isElement == null ? void 0 : platform.isElement(element))) != null ? _await$platform$isEle : true) ? element : element.contextElement || (await (platform.getDocumentElement == null ? void 0 : platform.getDocumentElement(elements.floating))),\n    boundary,\n    rootBoundary,\n    strategy\n  }));\n  const rect = elementContext === 'floating' ? {\n    x,\n    y,\n    width: rects.floating.width,\n    height: rects.floating.height\n  } : rects.reference;\n  const offsetParent = await (platform.getOffsetParent == null ? void 0 : platform.getOffsetParent(elements.floating));\n  const offsetScale = (await (platform.isElement == null ? void 0 : platform.isElement(offsetParent))) ? (await (platform.getScale == null ? void 0 : platform.getScale(offsetParent))) || {\n    x: 1,\n    y: 1\n  } : {\n    x: 1,\n    y: 1\n  };\n  const elementClientRect = rectToClientRect(platform.convertOffsetParentRelativeRectToViewportRelativeRect ? await platform.convertOffsetParentRelativeRectToViewportRelativeRect({\n    elements,\n    rect,\n    offsetParent,\n    strategy\n  }) : rect);\n  return {\n    top: (clippingClientRect.top - elementClientRect.top + paddingObject.top) / offsetScale.y,\n    bottom: (elementClientRect.bottom - clippingClientRect.bottom + paddingObject.bottom) / offsetScale.y,\n    left: (clippingClientRect.left - elementClientRect.left + paddingObject.left) / offsetScale.x,\n    right: (elementClientRect.right - clippingClientRect.right + paddingObject.right) / offsetScale.x\n  };\n}\n\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * @see https://floating-ui.com/docs/arrow\n */\nconst arrow = options => ({\n  name: 'arrow',\n  options,\n  async fn(state) {\n    const {\n      x,\n      y,\n      placement,\n      rects,\n      platform,\n      elements,\n      middlewareData\n    } = state;\n    // Since `element` is required, we don't Partial<> the type.\n    const {\n      element,\n      padding = 0\n    } = evaluate(options, state) || {};\n    if (element == null) {\n      return {};\n    }\n    const paddingObject = getPaddingObject(padding);\n    const coords = {\n      x,\n      y\n    };\n    const axis = getAlignmentAxis(placement);\n    const length = getAxisLength(axis);\n    const arrowDimensions = await platform.getDimensions(element);\n    const isYAxis = axis === 'y';\n    const minProp = isYAxis ? 'top' : 'left';\n    const maxProp = isYAxis ? 'bottom' : 'right';\n    const clientProp = isYAxis ? 'clientHeight' : 'clientWidth';\n    const endDiff = rects.reference[length] + rects.reference[axis] - coords[axis] - rects.floating[length];\n    const startDiff = coords[axis] - rects.reference[axis];\n    const arrowOffsetParent = await (platform.getOffsetParent == null ? void 0 : platform.getOffsetParent(element));\n    let clientSize = arrowOffsetParent ? arrowOffsetParent[clientProp] : 0;\n\n    // DOM platform can return `window` as the `offsetParent`.\n    if (!clientSize || !(await (platform.isElement == null ? void 0 : platform.isElement(arrowOffsetParent)))) {\n      clientSize = elements.floating[clientProp] || rects.floating[length];\n    }\n    const centerToReference = endDiff / 2 - startDiff / 2;\n\n    // If the padding is large enough that it causes the arrow to no longer be\n    // centered, modify the padding so that it is centered.\n    const largestPossiblePadding = clientSize / 2 - arrowDimensions[length] / 2 - 1;\n    const minPadding = min(paddingObject[minProp], largestPossiblePadding);\n    const maxPadding = min(paddingObject[maxProp], largestPossiblePadding);\n\n    // Make sure the arrow doesn't overflow the floating element if the center\n    // point is outside the floating element's bounds.\n    const min$1 = minPadding;\n    const max = clientSize - arrowDimensions[length] - maxPadding;\n    const center = clientSize / 2 - arrowDimensions[length] / 2 + centerToReference;\n    const offset = clamp(min$1, center, max);\n\n    // If the reference is small enough that the arrow's padding causes it to\n    // to point to nothing for an aligned placement, adjust the offset of the\n    // floating element itself. To ensure `shift()` continues to take action,\n    // a single reset is performed when this is true.\n    const shouldAddOffset = !middlewareData.arrow && getAlignment(placement) != null && center !== offset && rects.reference[length] / 2 - (center < min$1 ? minPadding : maxPadding) - arrowDimensions[length] / 2 < 0;\n    const alignmentOffset = shouldAddOffset ? center < min$1 ? center - min$1 : center - max : 0;\n    return {\n      [axis]: coords[axis] + alignmentOffset,\n      data: {\n        [axis]: offset,\n        centerOffset: center - offset - alignmentOffset,\n        ...(shouldAddOffset && {\n          alignmentOffset\n        })\n      },\n      reset: shouldAddOffset\n    };\n  }\n});\n\nfunction getPlacementList(alignment, autoAlignment, allowedPlacements) {\n  const allowedPlacementsSortedByAlignment = alignment ? [...allowedPlacements.filter(placement => getAlignment(placement) === alignment), ...allowedPlacements.filter(placement => getAlignment(placement) !== alignment)] : allowedPlacements.filter(placement => getSide(placement) === placement);\n  return allowedPlacementsSortedByAlignment.filter(placement => {\n    if (alignment) {\n      return getAlignment(placement) === alignment || (autoAlignment ? getOppositeAlignmentPlacement(placement) !== placement : false);\n    }\n    return true;\n  });\n}\n/**\n * Optimizes the visibility of the floating element by choosing the placement\n * that has the most space available automatically, without needing to specify a\n * preferred placement. Alternative to `flip`.\n * @see https://floating-ui.com/docs/autoPlacement\n */\nconst autoPlacement = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'autoPlacement',\n    options,\n    async fn(state) {\n      var _middlewareData$autoP, _middlewareData$autoP2, _placementsThatFitOnE;\n      const {\n        rects,\n        middlewareData,\n        placement,\n        platform,\n        elements\n      } = state;\n      const {\n        crossAxis = false,\n        alignment,\n        allowedPlacements = placements,\n        autoAlignment = true,\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      const placements$1 = alignment !== undefined || allowedPlacements === placements ? getPlacementList(alignment || null, autoAlignment, allowedPlacements) : allowedPlacements;\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const currentIndex = ((_middlewareData$autoP = middlewareData.autoPlacement) == null ? void 0 : _middlewareData$autoP.index) || 0;\n      const currentPlacement = placements$1[currentIndex];\n      if (currentPlacement == null) {\n        return {};\n      }\n      const alignmentSides = getAlignmentSides(currentPlacement, rects, await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating)));\n\n      // Make `computeCoords` start from the right place.\n      if (placement !== currentPlacement) {\n        return {\n          reset: {\n            placement: placements$1[0]\n          }\n        };\n      }\n      const currentOverflows = [overflow[getSide(currentPlacement)], overflow[alignmentSides[0]], overflow[alignmentSides[1]]];\n      const allOverflows = [...(((_middlewareData$autoP2 = middlewareData.autoPlacement) == null ? void 0 : _middlewareData$autoP2.overflows) || []), {\n        placement: currentPlacement,\n        overflows: currentOverflows\n      }];\n      const nextPlacement = placements$1[currentIndex + 1];\n\n      // There are more placements to check.\n      if (nextPlacement) {\n        return {\n          data: {\n            index: currentIndex + 1,\n            overflows: allOverflows\n          },\n          reset: {\n            placement: nextPlacement\n          }\n        };\n      }\n      const placementsSortedByMostSpace = allOverflows.map(d => {\n        const alignment = getAlignment(d.placement);\n        return [d.placement, alignment && crossAxis ?\n        // Check along the mainAxis and main crossAxis side.\n        d.overflows.slice(0, 2).reduce((acc, v) => acc + v, 0) :\n        // Check only the mainAxis.\n        d.overflows[0], d.overflows];\n      }).sort((a, b) => a[1] - b[1]);\n      const placementsThatFitOnEachSide = placementsSortedByMostSpace.filter(d => d[2].slice(0,\n      // Aligned placements should not check their opposite crossAxis\n      // side.\n      getAlignment(d[0]) ? 2 : 3).every(v => v <= 0));\n      const resetPlacement = ((_placementsThatFitOnE = placementsThatFitOnEachSide[0]) == null ? void 0 : _placementsThatFitOnE[0]) || placementsSortedByMostSpace[0][0];\n      if (resetPlacement !== placement) {\n        return {\n          data: {\n            index: currentIndex + 1,\n            overflows: allOverflows\n          },\n          reset: {\n            placement: resetPlacement\n          }\n        };\n      }\n      return {};\n    }\n  };\n};\n\n/**\n * Optimizes the visibility of the floating element by flipping the `placement`\n * in order to keep it in view when the preferred placement(s) will overflow the\n * clipping boundary. Alternative to `autoPlacement`.\n * @see https://floating-ui.com/docs/flip\n */\nconst flip = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'flip',\n    options,\n    async fn(state) {\n      var _middlewareData$arrow, _middlewareData$flip;\n      const {\n        placement,\n        middlewareData,\n        rects,\n        initialPlacement,\n        platform,\n        elements\n      } = state;\n      const {\n        mainAxis: checkMainAxis = true,\n        crossAxis: checkCrossAxis = true,\n        fallbackPlacements: specifiedFallbackPlacements,\n        fallbackStrategy = 'bestFit',\n        fallbackAxisSideDirection = 'none',\n        flipAlignment = true,\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n\n      // If a reset by the arrow was caused due to an alignment offset being\n      // added, we should skip any logic now since `flip()` has already done its\n      // work.\n      // https://github.com/floating-ui/floating-ui/issues/2549#issuecomment-1719601643\n      if ((_middlewareData$arrow = middlewareData.arrow) != null && _middlewareData$arrow.alignmentOffset) {\n        return {};\n      }\n      const side = getSide(placement);\n      const initialSideAxis = getSideAxis(initialPlacement);\n      const isBasePlacement = getSide(initialPlacement) === initialPlacement;\n      const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating));\n      const fallbackPlacements = specifiedFallbackPlacements || (isBasePlacement || !flipAlignment ? [getOppositePlacement(initialPlacement)] : getExpandedPlacements(initialPlacement));\n      const hasFallbackAxisSideDirection = fallbackAxisSideDirection !== 'none';\n      if (!specifiedFallbackPlacements && hasFallbackAxisSideDirection) {\n        fallbackPlacements.push(...getOppositeAxisPlacements(initialPlacement, flipAlignment, fallbackAxisSideDirection, rtl));\n      }\n      const placements = [initialPlacement, ...fallbackPlacements];\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const overflows = [];\n      let overflowsData = ((_middlewareData$flip = middlewareData.flip) == null ? void 0 : _middlewareData$flip.overflows) || [];\n      if (checkMainAxis) {\n        overflows.push(overflow[side]);\n      }\n      if (checkCrossAxis) {\n        const sides = getAlignmentSides(placement, rects, rtl);\n        overflows.push(overflow[sides[0]], overflow[sides[1]]);\n      }\n      overflowsData = [...overflowsData, {\n        placement,\n        overflows\n      }];\n\n      // One or more sides is overflowing.\n      if (!overflows.every(side => side <= 0)) {\n        var _middlewareData$flip2, _overflowsData$filter;\n        const nextIndex = (((_middlewareData$flip2 = middlewareData.flip) == null ? void 0 : _middlewareData$flip2.index) || 0) + 1;\n        const nextPlacement = placements[nextIndex];\n        if (nextPlacement) {\n          var _overflowsData$;\n          const ignoreCrossAxisOverflow = checkCrossAxis === 'alignment' ? initialSideAxis !== getSideAxis(nextPlacement) : false;\n          const hasInitialMainAxisOverflow = ((_overflowsData$ = overflowsData[0]) == null ? void 0 : _overflowsData$.overflows[0]) > 0;\n          if (!ignoreCrossAxisOverflow || hasInitialMainAxisOverflow) {\n            // Try next placement and re-run the lifecycle.\n            return {\n              data: {\n                index: nextIndex,\n                overflows: overflowsData\n              },\n              reset: {\n                placement: nextPlacement\n              }\n            };\n          }\n        }\n\n        // First, find the candidates that fit on the mainAxis side of overflow,\n        // then find the placement that fits the best on the main crossAxis side.\n        let resetPlacement = (_overflowsData$filter = overflowsData.filter(d => d.overflows[0] <= 0).sort((a, b) => a.overflows[1] - b.overflows[1])[0]) == null ? void 0 : _overflowsData$filter.placement;\n\n        // Otherwise fallback.\n        if (!resetPlacement) {\n          switch (fallbackStrategy) {\n            case 'bestFit':\n              {\n                var _overflowsData$filter2;\n                const placement = (_overflowsData$filter2 = overflowsData.filter(d => {\n                  if (hasFallbackAxisSideDirection) {\n                    const currentSideAxis = getSideAxis(d.placement);\n                    return currentSideAxis === initialSideAxis ||\n                    // Create a bias to the `y` side axis due to horizontal\n                    // reading directions favoring greater width.\n                    currentSideAxis === 'y';\n                  }\n                  return true;\n                }).map(d => [d.placement, d.overflows.filter(overflow => overflow > 0).reduce((acc, overflow) => acc + overflow, 0)]).sort((a, b) => a[1] - b[1])[0]) == null ? void 0 : _overflowsData$filter2[0];\n                if (placement) {\n                  resetPlacement = placement;\n                }\n                break;\n              }\n            case 'initialPlacement':\n              resetPlacement = initialPlacement;\n              break;\n          }\n        }\n        if (placement !== resetPlacement) {\n          return {\n            reset: {\n              placement: resetPlacement\n            }\n          };\n        }\n      }\n      return {};\n    }\n  };\n};\n\nfunction getSideOffsets(overflow, rect) {\n  return {\n    top: overflow.top - rect.height,\n    right: overflow.right - rect.width,\n    bottom: overflow.bottom - rect.height,\n    left: overflow.left - rect.width\n  };\n}\nfunction isAnySideFullyClipped(overflow) {\n  return sides.some(side => overflow[side] >= 0);\n}\n/**\n * Provides data to hide the floating element in applicable situations, such as\n * when it is not in the same clipping context as the reference element.\n * @see https://floating-ui.com/docs/hide\n */\nconst hide = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'hide',\n    options,\n    async fn(state) {\n      const {\n        rects\n      } = state;\n      const {\n        strategy = 'referenceHidden',\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      switch (strategy) {\n        case 'referenceHidden':\n          {\n            const overflow = await detectOverflow(state, {\n              ...detectOverflowOptions,\n              elementContext: 'reference'\n            });\n            const offsets = getSideOffsets(overflow, rects.reference);\n            return {\n              data: {\n                referenceHiddenOffsets: offsets,\n                referenceHidden: isAnySideFullyClipped(offsets)\n              }\n            };\n          }\n        case 'escaped':\n          {\n            const overflow = await detectOverflow(state, {\n              ...detectOverflowOptions,\n              altBoundary: true\n            });\n            const offsets = getSideOffsets(overflow, rects.floating);\n            return {\n              data: {\n                escapedOffsets: offsets,\n                escaped: isAnySideFullyClipped(offsets)\n              }\n            };\n          }\n        default:\n          {\n            return {};\n          }\n      }\n    }\n  };\n};\n\nfunction getBoundingRect(rects) {\n  const minX = min(...rects.map(rect => rect.left));\n  const minY = min(...rects.map(rect => rect.top));\n  const maxX = max(...rects.map(rect => rect.right));\n  const maxY = max(...rects.map(rect => rect.bottom));\n  return {\n    x: minX,\n    y: minY,\n    width: maxX - minX,\n    height: maxY - minY\n  };\n}\nfunction getRectsByLine(rects) {\n  const sortedRects = rects.slice().sort((a, b) => a.y - b.y);\n  const groups = [];\n  let prevRect = null;\n  for (let i = 0; i < sortedRects.length; i++) {\n    const rect = sortedRects[i];\n    if (!prevRect || rect.y - prevRect.y > prevRect.height / 2) {\n      groups.push([rect]);\n    } else {\n      groups[groups.length - 1].push(rect);\n    }\n    prevRect = rect;\n  }\n  return groups.map(rect => rectToClientRect(getBoundingRect(rect)));\n}\n/**\n * Provides improved positioning for inline reference elements that can span\n * over multiple lines, such as hyperlinks or range selections.\n * @see https://floating-ui.com/docs/inline\n */\nconst inline = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'inline',\n    options,\n    async fn(state) {\n      const {\n        placement,\n        elements,\n        rects,\n        platform,\n        strategy\n      } = state;\n      // A MouseEvent's client{X,Y} coords can be up to 2 pixels off a\n      // ClientRect's bounds, despite the event listener being triggered. A\n      // padding of 2 seems to handle this issue.\n      const {\n        padding = 2,\n        x,\n        y\n      } = evaluate(options, state);\n      const nativeClientRects = Array.from((await (platform.getClientRects == null ? void 0 : platform.getClientRects(elements.reference))) || []);\n      const clientRects = getRectsByLine(nativeClientRects);\n      const fallback = rectToClientRect(getBoundingRect(nativeClientRects));\n      const paddingObject = getPaddingObject(padding);\n      function getBoundingClientRect() {\n        // There are two rects and they are disjoined.\n        if (clientRects.length === 2 && clientRects[0].left > clientRects[1].right && x != null && y != null) {\n          // Find the first rect in which the point is fully inside.\n          return clientRects.find(rect => x > rect.left - paddingObject.left && x < rect.right + paddingObject.right && y > rect.top - paddingObject.top && y < rect.bottom + paddingObject.bottom) || fallback;\n        }\n\n        // There are 2 or more connected rects.\n        if (clientRects.length >= 2) {\n          if (getSideAxis(placement) === 'y') {\n            const firstRect = clientRects[0];\n            const lastRect = clientRects[clientRects.length - 1];\n            const isTop = getSide(placement) === 'top';\n            const top = firstRect.top;\n            const bottom = lastRect.bottom;\n            const left = isTop ? firstRect.left : lastRect.left;\n            const right = isTop ? firstRect.right : lastRect.right;\n            const width = right - left;\n            const height = bottom - top;\n            return {\n              top,\n              bottom,\n              left,\n              right,\n              width,\n              height,\n              x: left,\n              y: top\n            };\n          }\n          const isLeftSide = getSide(placement) === 'left';\n          const maxRight = max(...clientRects.map(rect => rect.right));\n          const minLeft = min(...clientRects.map(rect => rect.left));\n          const measureRects = clientRects.filter(rect => isLeftSide ? rect.left === minLeft : rect.right === maxRight);\n          const top = measureRects[0].top;\n          const bottom = measureRects[measureRects.length - 1].bottom;\n          const left = minLeft;\n          const right = maxRight;\n          const width = right - left;\n          const height = bottom - top;\n          return {\n            top,\n            bottom,\n            left,\n            right,\n            width,\n            height,\n            x: left,\n            y: top\n          };\n        }\n        return fallback;\n      }\n      const resetRects = await platform.getElementRects({\n        reference: {\n          getBoundingClientRect\n        },\n        floating: elements.floating,\n        strategy\n      });\n      if (rects.reference.x !== resetRects.reference.x || rects.reference.y !== resetRects.reference.y || rects.reference.width !== resetRects.reference.width || rects.reference.height !== resetRects.reference.height) {\n        return {\n          reset: {\n            rects: resetRects\n          }\n        };\n      }\n      return {};\n    }\n  };\n};\n\n// For type backwards-compatibility, the `OffsetOptions` type was also\n// Derivable.\n\nasync function convertValueToCoords(state, options) {\n  const {\n    placement,\n    platform,\n    elements\n  } = state;\n  const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating));\n  const side = getSide(placement);\n  const alignment = getAlignment(placement);\n  const isVertical = getSideAxis(placement) === 'y';\n  const mainAxisMulti = ['left', 'top'].includes(side) ? -1 : 1;\n  const crossAxisMulti = rtl && isVertical ? -1 : 1;\n  const rawValue = evaluate(options, state);\n\n  // eslint-disable-next-line prefer-const\n  let {\n    mainAxis,\n    crossAxis,\n    alignmentAxis\n  } = typeof rawValue === 'number' ? {\n    mainAxis: rawValue,\n    crossAxis: 0,\n    alignmentAxis: null\n  } : {\n    mainAxis: rawValue.mainAxis || 0,\n    crossAxis: rawValue.crossAxis || 0,\n    alignmentAxis: rawValue.alignmentAxis\n  };\n  if (alignment && typeof alignmentAxis === 'number') {\n    crossAxis = alignment === 'end' ? alignmentAxis * -1 : alignmentAxis;\n  }\n  return isVertical ? {\n    x: crossAxis * crossAxisMulti,\n    y: mainAxis * mainAxisMulti\n  } : {\n    x: mainAxis * mainAxisMulti,\n    y: crossAxis * crossAxisMulti\n  };\n}\n\n/**\n * Modifies the placement by translating the floating element along the\n * specified axes.\n * A number (shorthand for `mainAxis` or distance), or an axes configuration\n * object may be passed.\n * @see https://floating-ui.com/docs/offset\n */\nconst offset = function (options) {\n  if (options === void 0) {\n    options = 0;\n  }\n  return {\n    name: 'offset',\n    options,\n    async fn(state) {\n      var _middlewareData$offse, _middlewareData$arrow;\n      const {\n        x,\n        y,\n        placement,\n        middlewareData\n      } = state;\n      const diffCoords = await convertValueToCoords(state, options);\n\n      // If the placement is the same and the arrow caused an alignment offset\n      // then we don't need to change the positioning coordinates.\n      if (placement === ((_middlewareData$offse = middlewareData.offset) == null ? void 0 : _middlewareData$offse.placement) && (_middlewareData$arrow = middlewareData.arrow) != null && _middlewareData$arrow.alignmentOffset) {\n        return {};\n      }\n      return {\n        x: x + diffCoords.x,\n        y: y + diffCoords.y,\n        data: {\n          ...diffCoords,\n          placement\n        }\n      };\n    }\n  };\n};\n\n/**\n * Optimizes the visibility of the floating element by shifting it in order to\n * keep it in view when it will overflow the clipping boundary.\n * @see https://floating-ui.com/docs/shift\n */\nconst shift = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'shift',\n    options,\n    async fn(state) {\n      const {\n        x,\n        y,\n        placement\n      } = state;\n      const {\n        mainAxis: checkMainAxis = true,\n        crossAxis: checkCrossAxis = false,\n        limiter = {\n          fn: _ref => {\n            let {\n              x,\n              y\n            } = _ref;\n            return {\n              x,\n              y\n            };\n          }\n        },\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      const coords = {\n        x,\n        y\n      };\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const crossAxis = getSideAxis(getSide(placement));\n      const mainAxis = getOppositeAxis(crossAxis);\n      let mainAxisCoord = coords[mainAxis];\n      let crossAxisCoord = coords[crossAxis];\n      if (checkMainAxis) {\n        const minSide = mainAxis === 'y' ? 'top' : 'left';\n        const maxSide = mainAxis === 'y' ? 'bottom' : 'right';\n        const min = mainAxisCoord + overflow[minSide];\n        const max = mainAxisCoord - overflow[maxSide];\n        mainAxisCoord = clamp(min, mainAxisCoord, max);\n      }\n      if (checkCrossAxis) {\n        const minSide = crossAxis === 'y' ? 'top' : 'left';\n        const maxSide = crossAxis === 'y' ? 'bottom' : 'right';\n        const min = crossAxisCoord + overflow[minSide];\n        const max = crossAxisCoord - overflow[maxSide];\n        crossAxisCoord = clamp(min, crossAxisCoord, max);\n      }\n      const limitedCoords = limiter.fn({\n        ...state,\n        [mainAxis]: mainAxisCoord,\n        [crossAxis]: crossAxisCoord\n      });\n      return {\n        ...limitedCoords,\n        data: {\n          x: limitedCoords.x - x,\n          y: limitedCoords.y - y,\n          enabled: {\n            [mainAxis]: checkMainAxis,\n            [crossAxis]: checkCrossAxis\n          }\n        }\n      };\n    }\n  };\n};\n/**\n * Built-in `limiter` that will stop `shift()` at a certain point.\n */\nconst limitShift = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    options,\n    fn(state) {\n      const {\n        x,\n        y,\n        placement,\n        rects,\n        middlewareData\n      } = state;\n      const {\n        offset = 0,\n        mainAxis: checkMainAxis = true,\n        crossAxis: checkCrossAxis = true\n      } = evaluate(options, state);\n      const coords = {\n        x,\n        y\n      };\n      const crossAxis = getSideAxis(placement);\n      const mainAxis = getOppositeAxis(crossAxis);\n      let mainAxisCoord = coords[mainAxis];\n      let crossAxisCoord = coords[crossAxis];\n      const rawOffset = evaluate(offset, state);\n      const computedOffset = typeof rawOffset === 'number' ? {\n        mainAxis: rawOffset,\n        crossAxis: 0\n      } : {\n        mainAxis: 0,\n        crossAxis: 0,\n        ...rawOffset\n      };\n      if (checkMainAxis) {\n        const len = mainAxis === 'y' ? 'height' : 'width';\n        const limitMin = rects.reference[mainAxis] - rects.floating[len] + computedOffset.mainAxis;\n        const limitMax = rects.reference[mainAxis] + rects.reference[len] - computedOffset.mainAxis;\n        if (mainAxisCoord < limitMin) {\n          mainAxisCoord = limitMin;\n        } else if (mainAxisCoord > limitMax) {\n          mainAxisCoord = limitMax;\n        }\n      }\n      if (checkCrossAxis) {\n        var _middlewareData$offse, _middlewareData$offse2;\n        const len = mainAxis === 'y' ? 'width' : 'height';\n        const isOriginSide = ['top', 'left'].includes(getSide(placement));\n        const limitMin = rects.reference[crossAxis] - rects.floating[len] + (isOriginSide ? ((_middlewareData$offse = middlewareData.offset) == null ? void 0 : _middlewareData$offse[crossAxis]) || 0 : 0) + (isOriginSide ? 0 : computedOffset.crossAxis);\n        const limitMax = rects.reference[crossAxis] + rects.reference[len] + (isOriginSide ? 0 : ((_middlewareData$offse2 = middlewareData.offset) == null ? void 0 : _middlewareData$offse2[crossAxis]) || 0) - (isOriginSide ? computedOffset.crossAxis : 0);\n        if (crossAxisCoord < limitMin) {\n          crossAxisCoord = limitMin;\n        } else if (crossAxisCoord > limitMax) {\n          crossAxisCoord = limitMax;\n        }\n      }\n      return {\n        [mainAxis]: mainAxisCoord,\n        [crossAxis]: crossAxisCoord\n      };\n    }\n  };\n};\n\n/**\n * Provides data that allows you to change the size of the floating element —\n * for instance, prevent it from overflowing the clipping boundary or match the\n * width of the reference element.\n * @see https://floating-ui.com/docs/size\n */\nconst size = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'size',\n    options,\n    async fn(state) {\n      var _state$middlewareData, _state$middlewareData2;\n      const {\n        placement,\n        rects,\n        platform,\n        elements\n      } = state;\n      const {\n        apply = () => {},\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const side = getSide(placement);\n      const alignment = getAlignment(placement);\n      const isYAxis = getSideAxis(placement) === 'y';\n      const {\n        width,\n        height\n      } = rects.floating;\n      let heightSide;\n      let widthSide;\n      if (side === 'top' || side === 'bottom') {\n        heightSide = side;\n        widthSide = alignment === ((await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating))) ? 'start' : 'end') ? 'left' : 'right';\n      } else {\n        widthSide = side;\n        heightSide = alignment === 'end' ? 'top' : 'bottom';\n      }\n      const maximumClippingHeight = height - overflow.top - overflow.bottom;\n      const maximumClippingWidth = width - overflow.left - overflow.right;\n      const overflowAvailableHeight = min(height - overflow[heightSide], maximumClippingHeight);\n      const overflowAvailableWidth = min(width - overflow[widthSide], maximumClippingWidth);\n      const noShift = !state.middlewareData.shift;\n      let availableHeight = overflowAvailableHeight;\n      let availableWidth = overflowAvailableWidth;\n      if ((_state$middlewareData = state.middlewareData.shift) != null && _state$middlewareData.enabled.x) {\n        availableWidth = maximumClippingWidth;\n      }\n      if ((_state$middlewareData2 = state.middlewareData.shift) != null && _state$middlewareData2.enabled.y) {\n        availableHeight = maximumClippingHeight;\n      }\n      if (noShift && !alignment) {\n        const xMin = max(overflow.left, 0);\n        const xMax = max(overflow.right, 0);\n        const yMin = max(overflow.top, 0);\n        const yMax = max(overflow.bottom, 0);\n        if (isYAxis) {\n          availableWidth = width - 2 * (xMin !== 0 || xMax !== 0 ? xMin + xMax : max(overflow.left, overflow.right));\n        } else {\n          availableHeight = height - 2 * (yMin !== 0 || yMax !== 0 ? yMin + yMax : max(overflow.top, overflow.bottom));\n        }\n      }\n      await apply({\n        ...state,\n        availableWidth,\n        availableHeight\n      });\n      const nextDimensions = await platform.getDimensions(elements.floating);\n      if (width !== nextDimensions.width || height !== nextDimensions.height) {\n        return {\n          reset: {\n            rects: true\n          }\n        };\n      }\n      return {};\n    }\n  };\n};\n\nexport { arrow, autoPlacement, computePosition, detectOverflow, flip, hide, inline, limitShift, offset, shift, size };\n"], "mappings": ";;;;;;;AAAA,SAASA,WAAW,EAAEC,gBAAgB,EAAEC,aAAa,EAAEC,OAAO,EAAEC,YAAY,EAAEC,QAAQ,EAAEC,gBAAgB,EAAEC,gBAAgB,EAAEC,GAAG,EAAEC,KAAK,EAAEC,UAAU,EAAEC,iBAAiB,EAAEC,6BAA6B,EAAEC,oBAAoB,EAAEC,qBAAqB,EAAEC,yBAAyB,EAAEC,KAAK,EAAEC,GAAG,EAAEC,eAAe,QAAQ,oBAAoB;AACrU,SAASX,gBAAgB,QAAQ,oBAAoB;AAErD,SAASY,0BAA0BA,CAACC,IAAI,EAAEC,SAAS,EAAEC,GAAG,EAAE;EACxD,IAAI;IACFC,SAAS;IACTC;EACF,CAAC,GAAGJ,IAAI;EACR,MAAMK,QAAQ,GAAGzB,WAAW,CAACqB,SAAS,CAAC;EACvC,MAAMK,aAAa,GAAGzB,gBAAgB,CAACoB,SAAS,CAAC;EACjD,MAAMM,WAAW,GAAGzB,aAAa,CAACwB,aAAa,CAAC;EAChD,MAAME,IAAI,GAAGzB,OAAO,CAACkB,SAAS,CAAC;EAC/B,MAAMQ,UAAU,GAAGJ,QAAQ,KAAK,GAAG;EACnC,MAAMK,OAAO,GAAGP,SAAS,CAACQ,CAAC,GAAGR,SAAS,CAACS,KAAK,GAAG,CAAC,GAAGR,QAAQ,CAACQ,KAAK,GAAG,CAAC;EACtE,MAAMC,OAAO,GAAGV,SAAS,CAACW,CAAC,GAAGX,SAAS,CAACY,MAAM,GAAG,CAAC,GAAGX,QAAQ,CAACW,MAAM,GAAG,CAAC;EACxE,MAAMC,WAAW,GAAGb,SAAS,CAACI,WAAW,CAAC,GAAG,CAAC,GAAGH,QAAQ,CAACG,WAAW,CAAC,GAAG,CAAC;EAC1E,IAAIU,MAAM;EACV,QAAQT,IAAI;IACV,KAAK,KAAK;MACRS,MAAM,GAAG;QACPN,CAAC,EAAED,OAAO;QACVI,CAAC,EAAEX,SAAS,CAACW,CAAC,GAAGV,QAAQ,CAACW;MAC5B,CAAC;MACD;IACF,KAAK,QAAQ;MACXE,MAAM,GAAG;QACPN,CAAC,EAAED,OAAO;QACVI,CAAC,EAAEX,SAAS,CAACW,CAAC,GAAGX,SAAS,CAACY;MAC7B,CAAC;MACD;IACF,KAAK,OAAO;MACVE,MAAM,GAAG;QACPN,CAAC,EAAER,SAAS,CAACQ,CAAC,GAAGR,SAAS,CAACS,KAAK;QAChCE,CAAC,EAAED;MACL,CAAC;MACD;IACF,KAAK,MAAM;MACTI,MAAM,GAAG;QACPN,CAAC,EAAER,SAAS,CAACQ,CAAC,GAAGP,QAAQ,CAACQ,KAAK;QAC/BE,CAAC,EAAED;MACL,CAAC;MACD;IACF;MACEI,MAAM,GAAG;QACPN,CAAC,EAAER,SAAS,CAACQ,CAAC;QACdG,CAAC,EAAEX,SAAS,CAACW;MACf,CAAC;EACL;EACA,QAAQ9B,YAAY,CAACiB,SAAS,CAAC;IAC7B,KAAK,OAAO;MACVgB,MAAM,CAACX,aAAa,CAAC,IAAIU,WAAW,IAAId,GAAG,IAAIO,UAAU,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;MACnE;IACF,KAAK,KAAK;MACRQ,MAAM,CAACX,aAAa,CAAC,IAAIU,WAAW,IAAId,GAAG,IAAIO,UAAU,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;MACnE;EACJ;EACA,OAAOQ,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,eAAe,GAAG,MAAAA,CAAOf,SAAS,EAAEC,QAAQ,EAAEe,MAAM,KAAK;EAC7D,MAAM;IACJlB,SAAS,GAAG,QAAQ;IACpBmB,QAAQ,GAAG,UAAU;IACrBC,UAAU,GAAG,EAAE;IACfC;EACF,CAAC,GAAGH,MAAM;EACV,MAAMI,eAAe,GAAGF,UAAU,CAACG,MAAM,CAACC,OAAO,CAAC;EAClD,MAAMvB,GAAG,GAAG,OAAOoB,QAAQ,CAACI,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGJ,QAAQ,CAACI,KAAK,CAACtB,QAAQ,CAAC,CAAC;EAC9E,IAAIuB,KAAK,GAAG,MAAML,QAAQ,CAACM,eAAe,CAAC;IACzCzB,SAAS;IACTC,QAAQ;IACRgB;EACF,CAAC,CAAC;EACF,IAAI;IACFT,CAAC;IACDG;EACF,CAAC,GAAGf,0BAA0B,CAAC4B,KAAK,EAAE1B,SAAS,EAAEC,GAAG,CAAC;EACrD,IAAI2B,iBAAiB,GAAG5B,SAAS;EACjC,IAAI6B,cAAc,GAAG,CAAC,CAAC;EACvB,IAAIC,UAAU,GAAG,CAAC;EAClB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,eAAe,CAACU,MAAM,EAAED,CAAC,EAAE,EAAE;IAC/C,MAAM;MACJE,IAAI;MACJC;IACF,CAAC,GAAGZ,eAAe,CAACS,CAAC,CAAC;IACtB,MAAM;MACJrB,CAAC,EAAEyB,KAAK;MACRtB,CAAC,EAAEuB,KAAK;MACRC,IAAI;MACJC;IACF,CAAC,GAAG,MAAMJ,EAAE,CAAC;MACXxB,CAAC;MACDG,CAAC;MACD0B,gBAAgB,EAAEvC,SAAS;MAC3BA,SAAS,EAAE4B,iBAAiB;MAC5BT,QAAQ;MACRU,cAAc;MACdH,KAAK;MACLL,QAAQ;MACRmB,QAAQ,EAAE;QACRtC,SAAS;QACTC;MACF;IACF,CAAC,CAAC;IACFO,CAAC,GAAGyB,KAAK,IAAI,IAAI,GAAGA,KAAK,GAAGzB,CAAC;IAC7BG,CAAC,GAAGuB,KAAK,IAAI,IAAI,GAAGA,KAAK,GAAGvB,CAAC;IAC7BgB,cAAc,GAAAY,aAAA,CAAAA,aAAA,KACTZ,cAAc;MACjB,CAACI,IAAI,GAAAQ,aAAA,CAAAA,aAAA,KACAZ,cAAc,CAACI,IAAI,CAAC,GACpBI,IAAI;IACR,EACF;IACD,IAAIC,KAAK,IAAIR,UAAU,IAAI,EAAE,EAAE;MAC7BA,UAAU,EAAE;MACZ,IAAI,OAAOQ,KAAK,KAAK,QAAQ,EAAE;QAC7B,IAAIA,KAAK,CAACtC,SAAS,EAAE;UACnB4B,iBAAiB,GAAGU,KAAK,CAACtC,SAAS;QACrC;QACA,IAAIsC,KAAK,CAACZ,KAAK,EAAE;UACfA,KAAK,GAAGY,KAAK,CAACZ,KAAK,KAAK,IAAI,GAAG,MAAML,QAAQ,CAACM,eAAe,CAAC;YAC5DzB,SAAS;YACTC,QAAQ;YACRgB;UACF,CAAC,CAAC,GAAGmB,KAAK,CAACZ,KAAK;QAClB;QACA,CAAC;UACChB,CAAC;UACDG;QACF,CAAC,GAAGf,0BAA0B,CAAC4B,KAAK,EAAEE,iBAAiB,EAAE3B,GAAG,CAAC;MAC/D;MACA8B,CAAC,GAAG,CAAC,CAAC;IACR;EACF;EACA,OAAO;IACLrB,CAAC;IACDG,CAAC;IACDb,SAAS,EAAE4B,iBAAiB;IAC5BT,QAAQ;IACRU;EACF,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAea,cAAcA,CAACC,KAAK,EAAEC,OAAO,EAAE;EAC5C,IAAIC,qBAAqB;EACzB,IAAID,OAAO,KAAK,KAAK,CAAC,EAAE;IACtBA,OAAO,GAAG,CAAC,CAAC;EACd;EACA,MAAM;IACJlC,CAAC;IACDG,CAAC;IACDQ,QAAQ;IACRK,KAAK;IACLc,QAAQ;IACRrB;EACF,CAAC,GAAGwB,KAAK;EACT,MAAM;IACJG,QAAQ,GAAG,mBAAmB;IAC9BC,YAAY,GAAG,UAAU;IACzBC,cAAc,GAAG,UAAU;IAC3BC,WAAW,GAAG,KAAK;IACnBC,OAAO,GAAG;EACZ,CAAC,GAAGlE,QAAQ,CAAC4D,OAAO,EAAED,KAAK,CAAC;EAC5B,MAAMQ,aAAa,GAAGlE,gBAAgB,CAACiE,OAAO,CAAC;EAC/C,MAAME,UAAU,GAAGJ,cAAc,KAAK,UAAU,GAAG,WAAW,GAAG,UAAU;EAC3E,MAAMK,OAAO,GAAGb,QAAQ,CAACS,WAAW,GAAGG,UAAU,GAAGJ,cAAc,CAAC;EACnE,MAAMM,kBAAkB,GAAGpE,gBAAgB,CAAC,MAAMmC,QAAQ,CAACkC,eAAe,CAAC;IACzEF,OAAO,EAAE,CAAC,CAACR,qBAAqB,GAAG,OAAOxB,QAAQ,CAACmC,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGnC,QAAQ,CAACmC,SAAS,CAACH,OAAO,CAAC,CAAC,KAAK,IAAI,GAAGR,qBAAqB,GAAG,IAAI,IAAIQ,OAAO,GAAGA,OAAO,CAACI,cAAc,KAAK,OAAOpC,QAAQ,CAACqC,kBAAkB,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGrC,QAAQ,CAACqC,kBAAkB,CAAClB,QAAQ,CAACrC,QAAQ,CAAC,CAAC,CAAC;IACnS2C,QAAQ;IACRC,YAAY;IACZ5B;EACF,CAAC,CAAC,CAAC;EACH,MAAMwC,IAAI,GAAGX,cAAc,KAAK,UAAU,GAAG;IAC3CtC,CAAC;IACDG,CAAC;IACDF,KAAK,EAAEe,KAAK,CAACvB,QAAQ,CAACQ,KAAK;IAC3BG,MAAM,EAAEY,KAAK,CAACvB,QAAQ,CAACW;EACzB,CAAC,GAAGY,KAAK,CAACxB,SAAS;EACnB,MAAM0D,YAAY,GAAG,OAAOvC,QAAQ,CAACwC,eAAe,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGxC,QAAQ,CAACwC,eAAe,CAACrB,QAAQ,CAACrC,QAAQ,CAAC,CAAC;EACpH,MAAM2D,WAAW,GAAG,CAAC,OAAOzC,QAAQ,CAACmC,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGnC,QAAQ,CAACmC,SAAS,CAACI,YAAY,CAAC,CAAC,IAAI,CAAC,OAAOvC,QAAQ,CAAC0C,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG1C,QAAQ,CAAC0C,QAAQ,CAACH,YAAY,CAAC,CAAC,KAAK;IACvLlD,CAAC,EAAE,CAAC;IACJG,CAAC,EAAE;EACL,CAAC,GAAG;IACFH,CAAC,EAAE,CAAC;IACJG,CAAC,EAAE;EACL,CAAC;EACD,MAAMmD,iBAAiB,GAAG9E,gBAAgB,CAACmC,QAAQ,CAAC4C,qDAAqD,GAAG,MAAM5C,QAAQ,CAAC4C,qDAAqD,CAAC;IAC/KzB,QAAQ;IACRmB,IAAI;IACJC,YAAY;IACZzC;EACF,CAAC,CAAC,GAAGwC,IAAI,CAAC;EACV,OAAO;IACLO,GAAG,EAAE,CAACZ,kBAAkB,CAACY,GAAG,GAAGF,iBAAiB,CAACE,GAAG,GAAGf,aAAa,CAACe,GAAG,IAAIJ,WAAW,CAACjD,CAAC;IACzFsD,MAAM,EAAE,CAACH,iBAAiB,CAACG,MAAM,GAAGb,kBAAkB,CAACa,MAAM,GAAGhB,aAAa,CAACgB,MAAM,IAAIL,WAAW,CAACjD,CAAC;IACrGuD,IAAI,EAAE,CAACd,kBAAkB,CAACc,IAAI,GAAGJ,iBAAiB,CAACI,IAAI,GAAGjB,aAAa,CAACiB,IAAI,IAAIN,WAAW,CAACpD,CAAC;IAC7F2D,KAAK,EAAE,CAACL,iBAAiB,CAACK,KAAK,GAAGf,kBAAkB,CAACe,KAAK,GAAGlB,aAAa,CAACkB,KAAK,IAAIP,WAAW,CAACpD;EAClG,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAM4D,KAAK,GAAG1B,OAAO,KAAK;EACxBX,IAAI,EAAE,OAAO;EACbW,OAAO;EACP,MAAMV,EAAEA,CAACS,KAAK,EAAE;IACd,MAAM;MACJjC,CAAC;MACDG,CAAC;MACDb,SAAS;MACT0B,KAAK;MACLL,QAAQ;MACRmB,QAAQ;MACRX;IACF,CAAC,GAAGc,KAAK;IACT;IACA,MAAM;MACJU,OAAO;MACPH,OAAO,GAAG;IACZ,CAAC,GAAGlE,QAAQ,CAAC4D,OAAO,EAAED,KAAK,CAAC,IAAI,CAAC,CAAC;IAClC,IAAIU,OAAO,IAAI,IAAI,EAAE;MACnB,OAAO,CAAC,CAAC;IACX;IACA,MAAMF,aAAa,GAAGlE,gBAAgB,CAACiE,OAAO,CAAC;IAC/C,MAAMlC,MAAM,GAAG;MACbN,CAAC;MACDG;IACF,CAAC;IACD,MAAM0D,IAAI,GAAG3F,gBAAgB,CAACoB,SAAS,CAAC;IACxC,MAAMgC,MAAM,GAAGnD,aAAa,CAAC0F,IAAI,CAAC;IAClC,MAAMC,eAAe,GAAG,MAAMnD,QAAQ,CAACoD,aAAa,CAACpB,OAAO,CAAC;IAC7D,MAAMqB,OAAO,GAAGH,IAAI,KAAK,GAAG;IAC5B,MAAMI,OAAO,GAAGD,OAAO,GAAG,KAAK,GAAG,MAAM;IACxC,MAAME,OAAO,GAAGF,OAAO,GAAG,QAAQ,GAAG,OAAO;IAC5C,MAAMG,UAAU,GAAGH,OAAO,GAAG,cAAc,GAAG,aAAa;IAC3D,MAAMI,OAAO,GAAGpD,KAAK,CAACxB,SAAS,CAAC8B,MAAM,CAAC,GAAGN,KAAK,CAACxB,SAAS,CAACqE,IAAI,CAAC,GAAGvD,MAAM,CAACuD,IAAI,CAAC,GAAG7C,KAAK,CAACvB,QAAQ,CAAC6B,MAAM,CAAC;IACvG,MAAM+C,SAAS,GAAG/D,MAAM,CAACuD,IAAI,CAAC,GAAG7C,KAAK,CAACxB,SAAS,CAACqE,IAAI,CAAC;IACtD,MAAMS,iBAAiB,GAAG,OAAO3D,QAAQ,CAACwC,eAAe,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGxC,QAAQ,CAACwC,eAAe,CAACR,OAAO,CAAC,CAAC;IAC/G,IAAI4B,UAAU,GAAGD,iBAAiB,GAAGA,iBAAiB,CAACH,UAAU,CAAC,GAAG,CAAC;;IAEtE;IACA,IAAI,CAACI,UAAU,IAAI,EAAE,OAAO5D,QAAQ,CAACmC,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGnC,QAAQ,CAACmC,SAAS,CAACwB,iBAAiB,CAAC,CAAC,CAAC,EAAE;MACzGC,UAAU,GAAGzC,QAAQ,CAACrC,QAAQ,CAAC0E,UAAU,CAAC,IAAInD,KAAK,CAACvB,QAAQ,CAAC6B,MAAM,CAAC;IACtE;IACA,MAAMkD,iBAAiB,GAAGJ,OAAO,GAAG,CAAC,GAAGC,SAAS,GAAG,CAAC;;IAErD;IACA;IACA,MAAMI,sBAAsB,GAAGF,UAAU,GAAG,CAAC,GAAGT,eAAe,CAACxC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC;IAC/E,MAAMoD,UAAU,GAAGjG,GAAG,CAACgE,aAAa,CAACwB,OAAO,CAAC,EAAEQ,sBAAsB,CAAC;IACtE,MAAME,UAAU,GAAGlG,GAAG,CAACgE,aAAa,CAACyB,OAAO,CAAC,EAAEO,sBAAsB,CAAC;;IAEtE;IACA;IACA,MAAMG,KAAK,GAAGF,UAAU;IACxB,MAAMxF,GAAG,GAAGqF,UAAU,GAAGT,eAAe,CAACxC,MAAM,CAAC,GAAGqD,UAAU;IAC7D,MAAME,MAAM,GAAGN,UAAU,GAAG,CAAC,GAAGT,eAAe,CAACxC,MAAM,CAAC,GAAG,CAAC,GAAGkD,iBAAiB;IAC/E,MAAMM,MAAM,GAAGpG,KAAK,CAACkG,KAAK,EAAEC,MAAM,EAAE3F,GAAG,CAAC;;IAExC;IACA;IACA;IACA;IACA,MAAM6F,eAAe,GAAG,CAAC5D,cAAc,CAACyC,KAAK,IAAIvF,YAAY,CAACiB,SAAS,CAAC,IAAI,IAAI,IAAIuF,MAAM,KAAKC,MAAM,IAAI9D,KAAK,CAACxB,SAAS,CAAC8B,MAAM,CAAC,GAAG,CAAC,IAAIuD,MAAM,GAAGD,KAAK,GAAGF,UAAU,GAAGC,UAAU,CAAC,GAAGb,eAAe,CAACxC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC;IACnN,MAAM0D,eAAe,GAAGD,eAAe,GAAGF,MAAM,GAAGD,KAAK,GAAGC,MAAM,GAAGD,KAAK,GAAGC,MAAM,GAAG3F,GAAG,GAAG,CAAC;IAC5F,OAAO;MACL,CAAC2E,IAAI,GAAGvD,MAAM,CAACuD,IAAI,CAAC,GAAGmB,eAAe;MACtCrD,IAAI,EAAAI,aAAA;QACF,CAAC8B,IAAI,GAAGiB,MAAM;QACdG,YAAY,EAAEJ,MAAM,GAAGC,MAAM,GAAGE;MAAe,GAC3CD,eAAe,IAAI;QACrBC;MACF,CAAC,CACF;MACDpD,KAAK,EAAEmD;IACT,CAAC;EACH;AACF,CAAC,CAAC;AAEF,SAASG,gBAAgBA,CAACC,SAAS,EAAEC,aAAa,EAAEC,iBAAiB,EAAE;EACrE,MAAMC,kCAAkC,GAAGH,SAAS,GAAG,CAAC,GAAGE,iBAAiB,CAACxE,MAAM,CAACvB,SAAS,IAAIjB,YAAY,CAACiB,SAAS,CAAC,KAAK6F,SAAS,CAAC,EAAE,GAAGE,iBAAiB,CAACxE,MAAM,CAACvB,SAAS,IAAIjB,YAAY,CAACiB,SAAS,CAAC,KAAK6F,SAAS,CAAC,CAAC,GAAGE,iBAAiB,CAACxE,MAAM,CAACvB,SAAS,IAAIlB,OAAO,CAACkB,SAAS,CAAC,KAAKA,SAAS,CAAC;EACnS,OAAOgG,kCAAkC,CAACzE,MAAM,CAACvB,SAAS,IAAI;IAC5D,IAAI6F,SAAS,EAAE;MACb,OAAO9G,YAAY,CAACiB,SAAS,CAAC,KAAK6F,SAAS,KAAKC,aAAa,GAAGvG,6BAA6B,CAACS,SAAS,CAAC,KAAKA,SAAS,GAAG,KAAK,CAAC;IAClI;IACA,OAAO,IAAI;EACb,CAAC,CAAC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMiG,aAAa,GAAG,SAAAA,CAAUrD,OAAO,EAAE;EACvC,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IACtBA,OAAO,GAAG,CAAC,CAAC;EACd;EACA,OAAO;IACLX,IAAI,EAAE,eAAe;IACrBW,OAAO;IACP,MAAMV,EAAEA,CAACS,KAAK,EAAE;MACd,IAAIuD,qBAAqB,EAAEC,sBAAsB,EAAEC,qBAAqB;MACxE,MAAM;QACJ1E,KAAK;QACLG,cAAc;QACd7B,SAAS;QACTqB,QAAQ;QACRmB;MACF,CAAC,GAAGG,KAAK;MACT,MAAA0D,SAAA,GAMIrH,QAAQ,CAAC4D,OAAO,EAAED,KAAK,CAAC;QANtB;UACJ2D,SAAS,GAAG,KAAK;UACjBT,SAAS;UACTE,iBAAiB,GAAG1G,UAAU;UAC9ByG,aAAa,GAAG;QAElB,CAAC,GAAAO,SAAA;QADIE,qBAAqB,GAAAC,wBAAA,CAAAH,SAAA,EAAAI,SAAA;MAE1B,MAAMC,YAAY,GAAGb,SAAS,KAAKc,SAAS,IAAIZ,iBAAiB,KAAK1G,UAAU,GAAGuG,gBAAgB,CAACC,SAAS,IAAI,IAAI,EAAEC,aAAa,EAAEC,iBAAiB,CAAC,GAAGA,iBAAiB;MAC5K,MAAMa,QAAQ,GAAG,MAAMlE,cAAc,CAACC,KAAK,EAAE4D,qBAAqB,CAAC;MACnE,MAAMM,YAAY,GAAG,CAAC,CAACX,qBAAqB,GAAGrE,cAAc,CAACoE,aAAa,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGC,qBAAqB,CAACY,KAAK,KAAK,CAAC;MACjI,MAAMC,gBAAgB,GAAGL,YAAY,CAACG,YAAY,CAAC;MACnD,IAAIE,gBAAgB,IAAI,IAAI,EAAE;QAC5B,OAAO,CAAC,CAAC;MACX;MACA,MAAMC,cAAc,GAAG1H,iBAAiB,CAACyH,gBAAgB,EAAErF,KAAK,EAAE,OAAOL,QAAQ,CAACI,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGJ,QAAQ,CAACI,KAAK,CAACe,QAAQ,CAACrC,QAAQ,CAAC,CAAC,CAAC;;MAE9I;MACA,IAAIH,SAAS,KAAK+G,gBAAgB,EAAE;QAClC,OAAO;UACLzE,KAAK,EAAE;YACLtC,SAAS,EAAE0G,YAAY,CAAC,CAAC;UAC3B;QACF,CAAC;MACH;MACA,MAAMO,gBAAgB,GAAG,CAACL,QAAQ,CAAC9H,OAAO,CAACiI,gBAAgB,CAAC,CAAC,EAAEH,QAAQ,CAACI,cAAc,CAAC,CAAC,CAAC,CAAC,EAAEJ,QAAQ,CAACI,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;MACxH,MAAME,YAAY,GAAG,CAAC,IAAI,CAAC,CAACf,sBAAsB,GAAGtE,cAAc,CAACoE,aAAa,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGE,sBAAsB,CAACgB,SAAS,KAAK,EAAE,CAAC,EAAE;QAC9InH,SAAS,EAAE+G,gBAAgB;QAC3BI,SAAS,EAAEF;MACb,CAAC,CAAC;MACF,MAAMG,aAAa,GAAGV,YAAY,CAACG,YAAY,GAAG,CAAC,CAAC;;MAEpD;MACA,IAAIO,aAAa,EAAE;QACjB,OAAO;UACL/E,IAAI,EAAE;YACJyE,KAAK,EAAED,YAAY,GAAG,CAAC;YACvBM,SAAS,EAAED;UACb,CAAC;UACD5E,KAAK,EAAE;YACLtC,SAAS,EAAEoH;UACb;QACF,CAAC;MACH;MACA,MAAMC,2BAA2B,GAAGH,YAAY,CAACI,GAAG,CAACC,CAAC,IAAI;QACxD,MAAM1B,SAAS,GAAG9G,YAAY,CAACwI,CAAC,CAACvH,SAAS,CAAC;QAC3C,OAAO,CAACuH,CAAC,CAACvH,SAAS,EAAE6F,SAAS,IAAIS,SAAS;QAC3C;QACAiB,CAAC,CAACJ,SAAS,CAACK,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEC,CAAC,KAAKD,GAAG,GAAGC,CAAC,EAAE,CAAC,CAAC;QACtD;QACAJ,CAAC,CAACJ,SAAS,CAAC,CAAC,CAAC,EAAEI,CAAC,CAACJ,SAAS,CAAC;MAC9B,CAAC,CAAC,CAACS,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9B,MAAMC,2BAA2B,GAAGV,2BAA2B,CAAC9F,MAAM,CAACgG,CAAC,IAAIA,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;MACxF;MACA;MACAzI,YAAY,CAACwI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAACS,KAAK,CAACL,CAAC,IAAIA,CAAC,IAAI,CAAC,CAAC,CAAC;MAC/C,MAAMM,cAAc,GAAG,CAAC,CAAC7B,qBAAqB,GAAG2B,2BAA2B,CAAC,CAAC,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG3B,qBAAqB,CAAC,CAAC,CAAC,KAAKiB,2BAA2B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClK,IAAIY,cAAc,KAAKjI,SAAS,EAAE;QAChC,OAAO;UACLqC,IAAI,EAAE;YACJyE,KAAK,EAAED,YAAY,GAAG,CAAC;YACvBM,SAAS,EAAED;UACb,CAAC;UACD5E,KAAK,EAAE;YACLtC,SAAS,EAAEiI;UACb;QACF,CAAC;MACH;MACA,OAAO,CAAC,CAAC;IACX;EACF,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,IAAI,GAAG,SAAAA,CAAUtF,OAAO,EAAE;EAC9B,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IACtBA,OAAO,GAAG,CAAC,CAAC;EACd;EACA,OAAO;IACLX,IAAI,EAAE,MAAM;IACZW,OAAO;IACP,MAAMV,EAAEA,CAACS,KAAK,EAAE;MACd,IAAIwF,qBAAqB,EAAEC,oBAAoB;MAC/C,MAAM;QACJpI,SAAS;QACT6B,cAAc;QACdH,KAAK;QACLa,gBAAgB;QAChBlB,QAAQ;QACRmB;MACF,CAAC,GAAGG,KAAK;MACT,MAAA0F,UAAA,GAQIrJ,QAAQ,CAAC4D,OAAO,EAAED,KAAK,CAAC;QARtB;UACJ2F,QAAQ,EAAEC,aAAa,GAAG,IAAI;UAC9BjC,SAAS,EAAEkC,cAAc,GAAG,IAAI;UAChCC,kBAAkB,EAAEC,2BAA2B;UAC/CC,gBAAgB,GAAG,SAAS;UAC5BC,yBAAyB,GAAG,MAAM;UAClCC,aAAa,GAAG;QAElB,CAAC,GAAAR,UAAA;QADI9B,qBAAqB,GAAAC,wBAAA,CAAA6B,UAAA,EAAAS,UAAA;;MAG1B;MACA;MACA;MACA;MACA,IAAI,CAACX,qBAAqB,GAAGtG,cAAc,CAACyC,KAAK,KAAK,IAAI,IAAI6D,qBAAqB,CAACzC,eAAe,EAAE;QACnG,OAAO,CAAC,CAAC;MACX;MACA,MAAMnF,IAAI,GAAGzB,OAAO,CAACkB,SAAS,CAAC;MAC/B,MAAM+I,eAAe,GAAGpK,WAAW,CAAC4D,gBAAgB,CAAC;MACrD,MAAMyG,eAAe,GAAGlK,OAAO,CAACyD,gBAAgB,CAAC,KAAKA,gBAAgB;MACtE,MAAMtC,GAAG,GAAG,OAAOoB,QAAQ,CAACI,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGJ,QAAQ,CAACI,KAAK,CAACe,QAAQ,CAACrC,QAAQ,CAAC,CAAC;MACvF,MAAMsI,kBAAkB,GAAGC,2BAA2B,KAAKM,eAAe,IAAI,CAACH,aAAa,GAAG,CAACrJ,oBAAoB,CAAC+C,gBAAgB,CAAC,CAAC,GAAG9C,qBAAqB,CAAC8C,gBAAgB,CAAC,CAAC;MAClL,MAAM0G,4BAA4B,GAAGL,yBAAyB,KAAK,MAAM;MACzE,IAAI,CAACF,2BAA2B,IAAIO,4BAA4B,EAAE;QAChER,kBAAkB,CAACS,IAAI,CAAC,GAAGxJ,yBAAyB,CAAC6C,gBAAgB,EAAEsG,aAAa,EAAED,yBAAyB,EAAE3I,GAAG,CAAC,CAAC;MACxH;MACA,MAAMZ,UAAU,GAAG,CAACkD,gBAAgB,EAAE,GAAGkG,kBAAkB,CAAC;MAC5D,MAAM7B,QAAQ,GAAG,MAAMlE,cAAc,CAACC,KAAK,EAAE4D,qBAAqB,CAAC;MACnE,MAAMY,SAAS,GAAG,EAAE;MACpB,IAAIgC,aAAa,GAAG,CAAC,CAACf,oBAAoB,GAAGvG,cAAc,CAACqG,IAAI,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGE,oBAAoB,CAACjB,SAAS,KAAK,EAAE;MAC1H,IAAIoB,aAAa,EAAE;QACjBpB,SAAS,CAAC+B,IAAI,CAACtC,QAAQ,CAACrG,IAAI,CAAC,CAAC;MAChC;MACA,IAAIiI,cAAc,EAAE;QAClB,MAAM7I,KAAK,GAAGL,iBAAiB,CAACU,SAAS,EAAE0B,KAAK,EAAEzB,GAAG,CAAC;QACtDkH,SAAS,CAAC+B,IAAI,CAACtC,QAAQ,CAACjH,KAAK,CAAC,CAAC,CAAC,CAAC,EAAEiH,QAAQ,CAACjH,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;MACxD;MACAwJ,aAAa,GAAG,CAAC,GAAGA,aAAa,EAAE;QACjCnJ,SAAS;QACTmH;MACF,CAAC,CAAC;;MAEF;MACA,IAAI,CAACA,SAAS,CAACa,KAAK,CAACzH,IAAI,IAAIA,IAAI,IAAI,CAAC,CAAC,EAAE;QACvC,IAAI6I,qBAAqB,EAAEC,qBAAqB;QAChD,MAAMC,SAAS,GAAG,CAAC,CAAC,CAACF,qBAAqB,GAAGvH,cAAc,CAACqG,IAAI,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGkB,qBAAqB,CAACtC,KAAK,KAAK,CAAC,IAAI,CAAC;QAC3H,MAAMM,aAAa,GAAG/H,UAAU,CAACiK,SAAS,CAAC;QAC3C,IAAIlC,aAAa,EAAE;UACjB,IAAImC,eAAe;UACnB,MAAMC,uBAAuB,GAAGhB,cAAc,KAAK,WAAW,GAAGO,eAAe,KAAKpK,WAAW,CAACyI,aAAa,CAAC,GAAG,KAAK;UACvH,MAAMqC,0BAA0B,GAAG,CAAC,CAACF,eAAe,GAAGJ,aAAa,CAAC,CAAC,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGI,eAAe,CAACpC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC;UAC7H,IAAI,CAACqC,uBAAuB,IAAIC,0BAA0B,EAAE;YAC1D;YACA,OAAO;cACLpH,IAAI,EAAE;gBACJyE,KAAK,EAAEwC,SAAS;gBAChBnC,SAAS,EAAEgC;cACb,CAAC;cACD7G,KAAK,EAAE;gBACLtC,SAAS,EAAEoH;cACb;YACF,CAAC;UACH;QACF;;QAEA;QACA;QACA,IAAIa,cAAc,GAAG,CAACoB,qBAAqB,GAAGF,aAAa,CAAC5H,MAAM,CAACgG,CAAC,IAAIA,CAAC,CAACJ,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAACS,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACV,SAAS,CAAC,CAAC,CAAC,GAAGW,CAAC,CAACX,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGkC,qBAAqB,CAACrJ,SAAS;;QAEnM;QACA,IAAI,CAACiI,cAAc,EAAE;UACnB,QAAQU,gBAAgB;YACtB,KAAK,SAAS;cACZ;gBACE,IAAIe,sBAAsB;gBAC1B,MAAM1J,SAAS,GAAG,CAAC0J,sBAAsB,GAAGP,aAAa,CAAC5H,MAAM,CAACgG,CAAC,IAAI;kBACpE,IAAI0B,4BAA4B,EAAE;oBAChC,MAAMU,eAAe,GAAGhL,WAAW,CAAC4I,CAAC,CAACvH,SAAS,CAAC;oBAChD,OAAO2J,eAAe,KAAKZ,eAAe;oBAC1C;oBACA;oBACAY,eAAe,KAAK,GAAG;kBACzB;kBACA,OAAO,IAAI;gBACb,CAAC,CAAC,CAACrC,GAAG,CAACC,CAAC,IAAI,CAACA,CAAC,CAACvH,SAAS,EAAEuH,CAAC,CAACJ,SAAS,CAAC5F,MAAM,CAACqF,QAAQ,IAAIA,QAAQ,GAAG,CAAC,CAAC,CAACa,MAAM,CAAC,CAACC,GAAG,EAAEd,QAAQ,KAAKc,GAAG,GAAGd,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAC,CAACgB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG4B,sBAAsB,CAAC,CAAC,CAAC;gBAClM,IAAI1J,SAAS,EAAE;kBACbiI,cAAc,GAAGjI,SAAS;gBAC5B;gBACA;cACF;YACF,KAAK,kBAAkB;cACrBiI,cAAc,GAAG1F,gBAAgB;cACjC;UACJ;QACF;QACA,IAAIvC,SAAS,KAAKiI,cAAc,EAAE;UAChC,OAAO;YACL3F,KAAK,EAAE;cACLtC,SAAS,EAAEiI;YACb;UACF,CAAC;QACH;MACF;MACA,OAAO,CAAC,CAAC;IACX;EACF,CAAC;AACH,CAAC;AAED,SAAS2B,cAAcA,CAAChD,QAAQ,EAAEjD,IAAI,EAAE;EACtC,OAAO;IACLO,GAAG,EAAE0C,QAAQ,CAAC1C,GAAG,GAAGP,IAAI,CAAC7C,MAAM;IAC/BuD,KAAK,EAAEuC,QAAQ,CAACvC,KAAK,GAAGV,IAAI,CAAChD,KAAK;IAClCwD,MAAM,EAAEyC,QAAQ,CAACzC,MAAM,GAAGR,IAAI,CAAC7C,MAAM;IACrCsD,IAAI,EAAEwC,QAAQ,CAACxC,IAAI,GAAGT,IAAI,CAAChD;EAC7B,CAAC;AACH;AACA,SAASkJ,qBAAqBA,CAACjD,QAAQ,EAAE;EACvC,OAAOjH,KAAK,CAACmK,IAAI,CAACvJ,IAAI,IAAIqG,QAAQ,CAACrG,IAAI,CAAC,IAAI,CAAC,CAAC;AAChD;AACA;AACA;AACA;AACA;AACA;AACA,MAAMwJ,IAAI,GAAG,SAAAA,CAAUnH,OAAO,EAAE;EAC9B,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IACtBA,OAAO,GAAG,CAAC,CAAC;EACd;EACA,OAAO;IACLX,IAAI,EAAE,MAAM;IACZW,OAAO;IACP,MAAMV,EAAEA,CAACS,KAAK,EAAE;MACd,MAAM;QACJjB;MACF,CAAC,GAAGiB,KAAK;MACT,MAAAqH,UAAA,GAGIhL,QAAQ,CAAC4D,OAAO,EAAED,KAAK,CAAC;QAHtB;UACJxB,QAAQ,GAAG;QAEb,CAAC,GAAA6I,UAAA;QADIzD,qBAAqB,GAAAC,wBAAA,CAAAwD,UAAA,EAAAC,UAAA;MAE1B,QAAQ9I,QAAQ;QACd,KAAK,iBAAiB;UACpB;YACE,MAAMyF,QAAQ,GAAG,MAAMlE,cAAc,CAACC,KAAK,EAAAF,aAAA,CAAAA,aAAA,KACtC8D,qBAAqB;cACxBvD,cAAc,EAAE;YAAW,EAC5B,CAAC;YACF,MAAMkH,OAAO,GAAGN,cAAc,CAAChD,QAAQ,EAAElF,KAAK,CAACxB,SAAS,CAAC;YACzD,OAAO;cACLmC,IAAI,EAAE;gBACJ8H,sBAAsB,EAAED,OAAO;gBAC/BE,eAAe,EAAEP,qBAAqB,CAACK,OAAO;cAChD;YACF,CAAC;UACH;QACF,KAAK,SAAS;UACZ;YACE,MAAMtD,QAAQ,GAAG,MAAMlE,cAAc,CAACC,KAAK,EAAAF,aAAA,CAAAA,aAAA,KACtC8D,qBAAqB;cACxBtD,WAAW,EAAE;YAAI,EAClB,CAAC;YACF,MAAMiH,OAAO,GAAGN,cAAc,CAAChD,QAAQ,EAAElF,KAAK,CAACvB,QAAQ,CAAC;YACxD,OAAO;cACLkC,IAAI,EAAE;gBACJgI,cAAc,EAAEH,OAAO;gBACvBI,OAAO,EAAET,qBAAqB,CAACK,OAAO;cACxC;YACF,CAAC;UACH;QACF;UACE;YACE,OAAO,CAAC,CAAC;UACX;MACJ;IACF;EACF,CAAC;AACH,CAAC;AAED,SAASK,eAAeA,CAAC7I,KAAK,EAAE;EAC9B,MAAM8I,IAAI,GAAGrL,GAAG,CAAC,GAAGuC,KAAK,CAAC4F,GAAG,CAAC3D,IAAI,IAAIA,IAAI,CAACS,IAAI,CAAC,CAAC;EACjD,MAAMqG,IAAI,GAAGtL,GAAG,CAAC,GAAGuC,KAAK,CAAC4F,GAAG,CAAC3D,IAAI,IAAIA,IAAI,CAACO,GAAG,CAAC,CAAC;EAChD,MAAMwG,IAAI,GAAG9K,GAAG,CAAC,GAAG8B,KAAK,CAAC4F,GAAG,CAAC3D,IAAI,IAAIA,IAAI,CAACU,KAAK,CAAC,CAAC;EAClD,MAAMsG,IAAI,GAAG/K,GAAG,CAAC,GAAG8B,KAAK,CAAC4F,GAAG,CAAC3D,IAAI,IAAIA,IAAI,CAACQ,MAAM,CAAC,CAAC;EACnD,OAAO;IACLzD,CAAC,EAAE8J,IAAI;IACP3J,CAAC,EAAE4J,IAAI;IACP9J,KAAK,EAAE+J,IAAI,GAAGF,IAAI;IAClB1J,MAAM,EAAE6J,IAAI,GAAGF;EACjB,CAAC;AACH;AACA,SAASG,cAAcA,CAAClJ,KAAK,EAAE;EAC7B,MAAMmJ,WAAW,GAAGnJ,KAAK,CAAC8F,KAAK,CAAC,CAAC,CAACI,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAChH,CAAC,GAAGiH,CAAC,CAACjH,CAAC,CAAC;EAC3D,MAAMiK,MAAM,GAAG,EAAE;EACjB,IAAIC,QAAQ,GAAG,IAAI;EACnB,KAAK,IAAIhJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8I,WAAW,CAAC7I,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3C,MAAM4B,IAAI,GAAGkH,WAAW,CAAC9I,CAAC,CAAC;IAC3B,IAAI,CAACgJ,QAAQ,IAAIpH,IAAI,CAAC9C,CAAC,GAAGkK,QAAQ,CAAClK,CAAC,GAAGkK,QAAQ,CAACjK,MAAM,GAAG,CAAC,EAAE;MAC1DgK,MAAM,CAAC5B,IAAI,CAAC,CAACvF,IAAI,CAAC,CAAC;IACrB,CAAC,MAAM;MACLmH,MAAM,CAACA,MAAM,CAAC9I,MAAM,GAAG,CAAC,CAAC,CAACkH,IAAI,CAACvF,IAAI,CAAC;IACtC;IACAoH,QAAQ,GAAGpH,IAAI;EACjB;EACA,OAAOmH,MAAM,CAACxD,GAAG,CAAC3D,IAAI,IAAIzE,gBAAgB,CAACqL,eAAe,CAAC5G,IAAI,CAAC,CAAC,CAAC;AACpE;AACA;AACA;AACA;AACA;AACA;AACA,MAAMqH,MAAM,GAAG,SAAAA,CAAUpI,OAAO,EAAE;EAChC,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IACtBA,OAAO,GAAG,CAAC,CAAC;EACd;EACA,OAAO;IACLX,IAAI,EAAE,QAAQ;IACdW,OAAO;IACP,MAAMV,EAAEA,CAACS,KAAK,EAAE;MACd,MAAM;QACJ3C,SAAS;QACTwC,QAAQ;QACRd,KAAK;QACLL,QAAQ;QACRF;MACF,CAAC,GAAGwB,KAAK;MACT;MACA;MACA;MACA,MAAM;QACJO,OAAO,GAAG,CAAC;QACXxC,CAAC;QACDG;MACF,CAAC,GAAG7B,QAAQ,CAAC4D,OAAO,EAAED,KAAK,CAAC;MAC5B,MAAMsI,iBAAiB,GAAGC,KAAK,CAACC,IAAI,CAAC,CAAC,OAAO9J,QAAQ,CAAC+J,cAAc,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG/J,QAAQ,CAAC+J,cAAc,CAAC5I,QAAQ,CAACtC,SAAS,CAAC,CAAC,KAAK,EAAE,CAAC;MAC5I,MAAMmL,WAAW,GAAGT,cAAc,CAACK,iBAAiB,CAAC;MACrD,MAAMK,QAAQ,GAAGpM,gBAAgB,CAACqL,eAAe,CAACU,iBAAiB,CAAC,CAAC;MACrE,MAAM9H,aAAa,GAAGlE,gBAAgB,CAACiE,OAAO,CAAC;MAC/C,SAASqI,qBAAqBA,CAAA,EAAG;QAC/B;QACA,IAAIF,WAAW,CAACrJ,MAAM,KAAK,CAAC,IAAIqJ,WAAW,CAAC,CAAC,CAAC,CAACjH,IAAI,GAAGiH,WAAW,CAAC,CAAC,CAAC,CAAChH,KAAK,IAAI3D,CAAC,IAAI,IAAI,IAAIG,CAAC,IAAI,IAAI,EAAE;UACpG;UACA,OAAOwK,WAAW,CAACG,IAAI,CAAC7H,IAAI,IAAIjD,CAAC,GAAGiD,IAAI,CAACS,IAAI,GAAGjB,aAAa,CAACiB,IAAI,IAAI1D,CAAC,GAAGiD,IAAI,CAACU,KAAK,GAAGlB,aAAa,CAACkB,KAAK,IAAIxD,CAAC,GAAG8C,IAAI,CAACO,GAAG,GAAGf,aAAa,CAACe,GAAG,IAAIrD,CAAC,GAAG8C,IAAI,CAACQ,MAAM,GAAGhB,aAAa,CAACgB,MAAM,CAAC,IAAImH,QAAQ;QACvM;;QAEA;QACA,IAAID,WAAW,CAACrJ,MAAM,IAAI,CAAC,EAAE;UAC3B,IAAIrD,WAAW,CAACqB,SAAS,CAAC,KAAK,GAAG,EAAE;YAClC,MAAMyL,SAAS,GAAGJ,WAAW,CAAC,CAAC,CAAC;YAChC,MAAMK,QAAQ,GAAGL,WAAW,CAACA,WAAW,CAACrJ,MAAM,GAAG,CAAC,CAAC;YACpD,MAAM2J,KAAK,GAAG7M,OAAO,CAACkB,SAAS,CAAC,KAAK,KAAK;YAC1C,MAAMkE,GAAG,GAAGuH,SAAS,CAACvH,GAAG;YACzB,MAAMC,MAAM,GAAGuH,QAAQ,CAACvH,MAAM;YAC9B,MAAMC,IAAI,GAAGuH,KAAK,GAAGF,SAAS,CAACrH,IAAI,GAAGsH,QAAQ,CAACtH,IAAI;YACnD,MAAMC,KAAK,GAAGsH,KAAK,GAAGF,SAAS,CAACpH,KAAK,GAAGqH,QAAQ,CAACrH,KAAK;YACtD,MAAM1D,KAAK,GAAG0D,KAAK,GAAGD,IAAI;YAC1B,MAAMtD,MAAM,GAAGqD,MAAM,GAAGD,GAAG;YAC3B,OAAO;cACLA,GAAG;cACHC,MAAM;cACNC,IAAI;cACJC,KAAK;cACL1D,KAAK;cACLG,MAAM;cACNJ,CAAC,EAAE0D,IAAI;cACPvD,CAAC,EAAEqD;YACL,CAAC;UACH;UACA,MAAM0H,UAAU,GAAG9M,OAAO,CAACkB,SAAS,CAAC,KAAK,MAAM;UAChD,MAAM6L,QAAQ,GAAGjM,GAAG,CAAC,GAAGyL,WAAW,CAAC/D,GAAG,CAAC3D,IAAI,IAAIA,IAAI,CAACU,KAAK,CAAC,CAAC;UAC5D,MAAMyH,OAAO,GAAG3M,GAAG,CAAC,GAAGkM,WAAW,CAAC/D,GAAG,CAAC3D,IAAI,IAAIA,IAAI,CAACS,IAAI,CAAC,CAAC;UAC1D,MAAM2H,YAAY,GAAGV,WAAW,CAAC9J,MAAM,CAACoC,IAAI,IAAIiI,UAAU,GAAGjI,IAAI,CAACS,IAAI,KAAK0H,OAAO,GAAGnI,IAAI,CAACU,KAAK,KAAKwH,QAAQ,CAAC;UAC7G,MAAM3H,GAAG,GAAG6H,YAAY,CAAC,CAAC,CAAC,CAAC7H,GAAG;UAC/B,MAAMC,MAAM,GAAG4H,YAAY,CAACA,YAAY,CAAC/J,MAAM,GAAG,CAAC,CAAC,CAACmC,MAAM;UAC3D,MAAMC,IAAI,GAAG0H,OAAO;UACpB,MAAMzH,KAAK,GAAGwH,QAAQ;UACtB,MAAMlL,KAAK,GAAG0D,KAAK,GAAGD,IAAI;UAC1B,MAAMtD,MAAM,GAAGqD,MAAM,GAAGD,GAAG;UAC3B,OAAO;YACLA,GAAG;YACHC,MAAM;YACNC,IAAI;YACJC,KAAK;YACL1D,KAAK;YACLG,MAAM;YACNJ,CAAC,EAAE0D,IAAI;YACPvD,CAAC,EAAEqD;UACL,CAAC;QACH;QACA,OAAOoH,QAAQ;MACjB;MACA,MAAMU,UAAU,GAAG,MAAM3K,QAAQ,CAACM,eAAe,CAAC;QAChDzB,SAAS,EAAE;UACTqL;QACF,CAAC;QACDpL,QAAQ,EAAEqC,QAAQ,CAACrC,QAAQ;QAC3BgB;MACF,CAAC,CAAC;MACF,IAAIO,KAAK,CAACxB,SAAS,CAACQ,CAAC,KAAKsL,UAAU,CAAC9L,SAAS,CAACQ,CAAC,IAAIgB,KAAK,CAACxB,SAAS,CAACW,CAAC,KAAKmL,UAAU,CAAC9L,SAAS,CAACW,CAAC,IAAIa,KAAK,CAACxB,SAAS,CAACS,KAAK,KAAKqL,UAAU,CAAC9L,SAAS,CAACS,KAAK,IAAIe,KAAK,CAACxB,SAAS,CAACY,MAAM,KAAKkL,UAAU,CAAC9L,SAAS,CAACY,MAAM,EAAE;QAClN,OAAO;UACLwB,KAAK,EAAE;YACLZ,KAAK,EAAEsK;UACT;QACF,CAAC;MACH;MACA,OAAO,CAAC,CAAC;IACX;EACF,CAAC;AACH,CAAC;;AAED;AACA;;AAEA,eAAeC,oBAAoBA,CAACtJ,KAAK,EAAEC,OAAO,EAAE;EAClD,MAAM;IACJ5C,SAAS;IACTqB,QAAQ;IACRmB;EACF,CAAC,GAAGG,KAAK;EACT,MAAM1C,GAAG,GAAG,OAAOoB,QAAQ,CAACI,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGJ,QAAQ,CAACI,KAAK,CAACe,QAAQ,CAACrC,QAAQ,CAAC,CAAC;EACvF,MAAMI,IAAI,GAAGzB,OAAO,CAACkB,SAAS,CAAC;EAC/B,MAAM6F,SAAS,GAAG9G,YAAY,CAACiB,SAAS,CAAC;EACzC,MAAMQ,UAAU,GAAG7B,WAAW,CAACqB,SAAS,CAAC,KAAK,GAAG;EACjD,MAAMkM,aAAa,GAAG,CAAC,MAAM,EAAE,KAAK,CAAC,CAACC,QAAQ,CAAC5L,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;EAC7D,MAAM6L,cAAc,GAAGnM,GAAG,IAAIO,UAAU,GAAG,CAAC,CAAC,GAAG,CAAC;EACjD,MAAM6L,QAAQ,GAAGrN,QAAQ,CAAC4D,OAAO,EAAED,KAAK,CAAC;;EAEzC;EACA,IAAI;IACF2F,QAAQ;IACRhC,SAAS;IACTjG;EACF,CAAC,GAAG,OAAOgM,QAAQ,KAAK,QAAQ,GAAG;IACjC/D,QAAQ,EAAE+D,QAAQ;IAClB/F,SAAS,EAAE,CAAC;IACZjG,aAAa,EAAE;EACjB,CAAC,GAAG;IACFiI,QAAQ,EAAE+D,QAAQ,CAAC/D,QAAQ,IAAI,CAAC;IAChChC,SAAS,EAAE+F,QAAQ,CAAC/F,SAAS,IAAI,CAAC;IAClCjG,aAAa,EAAEgM,QAAQ,CAAChM;EAC1B,CAAC;EACD,IAAIwF,SAAS,IAAI,OAAOxF,aAAa,KAAK,QAAQ,EAAE;IAClDiG,SAAS,GAAGT,SAAS,KAAK,KAAK,GAAGxF,aAAa,GAAG,CAAC,CAAC,GAAGA,aAAa;EACtE;EACA,OAAOG,UAAU,GAAG;IAClBE,CAAC,EAAE4F,SAAS,GAAG8F,cAAc;IAC7BvL,CAAC,EAAEyH,QAAQ,GAAG4D;EAChB,CAAC,GAAG;IACFxL,CAAC,EAAE4H,QAAQ,GAAG4D,aAAa;IAC3BrL,CAAC,EAAEyF,SAAS,GAAG8F;EACjB,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM5G,MAAM,GAAG,SAAAA,CAAU5C,OAAO,EAAE;EAChC,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IACtBA,OAAO,GAAG,CAAC;EACb;EACA,OAAO;IACLX,IAAI,EAAE,QAAQ;IACdW,OAAO;IACP,MAAMV,EAAEA,CAACS,KAAK,EAAE;MACd,IAAI2J,qBAAqB,EAAEnE,qBAAqB;MAChD,MAAM;QACJzH,CAAC;QACDG,CAAC;QACDb,SAAS;QACT6B;MACF,CAAC,GAAGc,KAAK;MACT,MAAM4J,UAAU,GAAG,MAAMN,oBAAoB,CAACtJ,KAAK,EAAEC,OAAO,CAAC;;MAE7D;MACA;MACA,IAAI5C,SAAS,MAAM,CAACsM,qBAAqB,GAAGzK,cAAc,CAAC2D,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG8G,qBAAqB,CAACtM,SAAS,CAAC,IAAI,CAACmI,qBAAqB,GAAGtG,cAAc,CAACyC,KAAK,KAAK,IAAI,IAAI6D,qBAAqB,CAACzC,eAAe,EAAE;QACzN,OAAO,CAAC,CAAC;MACX;MACA,OAAO;QACLhF,CAAC,EAAEA,CAAC,GAAG6L,UAAU,CAAC7L,CAAC;QACnBG,CAAC,EAAEA,CAAC,GAAG0L,UAAU,CAAC1L,CAAC;QACnBwB,IAAI,EAAAI,aAAA,CAAAA,aAAA,KACC8J,UAAU;UACbvM;QAAS;MAEb,CAAC;IACH;EACF,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,MAAMwM,KAAK,GAAG,SAAAA,CAAU5J,OAAO,EAAE;EAC/B,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IACtBA,OAAO,GAAG,CAAC,CAAC;EACd;EACA,OAAO;IACLX,IAAI,EAAE,OAAO;IACbW,OAAO;IACP,MAAMV,EAAEA,CAACS,KAAK,EAAE;MACd,MAAM;QACJjC,CAAC;QACDG,CAAC;QACDb;MACF,CAAC,GAAG2C,KAAK;MACT,MAAA8J,UAAA,GAgBIzN,QAAQ,CAAC4D,OAAO,EAAED,KAAK,CAAC;QAhBtB;UACJ2F,QAAQ,EAAEC,aAAa,GAAG,IAAI;UAC9BjC,SAAS,EAAEkC,cAAc,GAAG,KAAK;UACjCkE,OAAO,GAAG;YACRxK,EAAE,EAAEnC,IAAI,IAAI;cACV,IAAI;gBACFW,CAAC;gBACDG;cACF,CAAC,GAAGd,IAAI;cACR,OAAO;gBACLW,CAAC;gBACDG;cACF,CAAC;YACH;UACF;QAEF,CAAC,GAAA4L,UAAA;QADIlG,qBAAqB,GAAAC,wBAAA,CAAAiG,UAAA,EAAAE,UAAA;MAE1B,MAAM3L,MAAM,GAAG;QACbN,CAAC;QACDG;MACF,CAAC;MACD,MAAM+F,QAAQ,GAAG,MAAMlE,cAAc,CAACC,KAAK,EAAE4D,qBAAqB,CAAC;MACnE,MAAMD,SAAS,GAAG3H,WAAW,CAACG,OAAO,CAACkB,SAAS,CAAC,CAAC;MACjD,MAAMsI,QAAQ,GAAGzI,eAAe,CAACyG,SAAS,CAAC;MAC3C,IAAIsG,aAAa,GAAG5L,MAAM,CAACsH,QAAQ,CAAC;MACpC,IAAIuE,cAAc,GAAG7L,MAAM,CAACsF,SAAS,CAAC;MACtC,IAAIiC,aAAa,EAAE;QACjB,MAAMuE,OAAO,GAAGxE,QAAQ,KAAK,GAAG,GAAG,KAAK,GAAG,MAAM;QACjD,MAAMyE,OAAO,GAAGzE,QAAQ,KAAK,GAAG,GAAG,QAAQ,GAAG,OAAO;QACrD,MAAMnJ,GAAG,GAAGyN,aAAa,GAAGhG,QAAQ,CAACkG,OAAO,CAAC;QAC7C,MAAMlN,GAAG,GAAGgN,aAAa,GAAGhG,QAAQ,CAACmG,OAAO,CAAC;QAC7CH,aAAa,GAAGxN,KAAK,CAACD,GAAG,EAAEyN,aAAa,EAAEhN,GAAG,CAAC;MAChD;MACA,IAAI4I,cAAc,EAAE;QAClB,MAAMsE,OAAO,GAAGxG,SAAS,KAAK,GAAG,GAAG,KAAK,GAAG,MAAM;QAClD,MAAMyG,OAAO,GAAGzG,SAAS,KAAK,GAAG,GAAG,QAAQ,GAAG,OAAO;QACtD,MAAMnH,GAAG,GAAG0N,cAAc,GAAGjG,QAAQ,CAACkG,OAAO,CAAC;QAC9C,MAAMlN,GAAG,GAAGiN,cAAc,GAAGjG,QAAQ,CAACmG,OAAO,CAAC;QAC9CF,cAAc,GAAGzN,KAAK,CAACD,GAAG,EAAE0N,cAAc,EAAEjN,GAAG,CAAC;MAClD;MACA,MAAMoN,aAAa,GAAGN,OAAO,CAACxK,EAAE,CAAAO,aAAA,CAAAA,aAAA,KAC3BE,KAAK;QACR,CAAC2F,QAAQ,GAAGsE,aAAa;QACzB,CAACtG,SAAS,GAAGuG;MAAc,EAC5B,CAAC;MACF,OAAApK,aAAA,CAAAA,aAAA,KACKuK,aAAa;QAChB3K,IAAI,EAAE;UACJ3B,CAAC,EAAEsM,aAAa,CAACtM,CAAC,GAAGA,CAAC;UACtBG,CAAC,EAAEmM,aAAa,CAACnM,CAAC,GAAGA,CAAC;UACtBoM,OAAO,EAAE;YACP,CAAC3E,QAAQ,GAAGC,aAAa;YACzB,CAACjC,SAAS,GAAGkC;UACf;QACF;MAAC;IAEL;EACF,CAAC;AACH,CAAC;AACD;AACA;AACA;AACA,MAAM0E,UAAU,GAAG,SAAAA,CAAUtK,OAAO,EAAE;EACpC,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IACtBA,OAAO,GAAG,CAAC,CAAC;EACd;EACA,OAAO;IACLA,OAAO;IACPV,EAAEA,CAACS,KAAK,EAAE;MACR,MAAM;QACJjC,CAAC;QACDG,CAAC;QACDb,SAAS;QACT0B,KAAK;QACLG;MACF,CAAC,GAAGc,KAAK;MACT,MAAM;QACJ6C,MAAM,GAAG,CAAC;QACV8C,QAAQ,EAAEC,aAAa,GAAG,IAAI;QAC9BjC,SAAS,EAAEkC,cAAc,GAAG;MAC9B,CAAC,GAAGxJ,QAAQ,CAAC4D,OAAO,EAAED,KAAK,CAAC;MAC5B,MAAM3B,MAAM,GAAG;QACbN,CAAC;QACDG;MACF,CAAC;MACD,MAAMyF,SAAS,GAAG3H,WAAW,CAACqB,SAAS,CAAC;MACxC,MAAMsI,QAAQ,GAAGzI,eAAe,CAACyG,SAAS,CAAC;MAC3C,IAAIsG,aAAa,GAAG5L,MAAM,CAACsH,QAAQ,CAAC;MACpC,IAAIuE,cAAc,GAAG7L,MAAM,CAACsF,SAAS,CAAC;MACtC,MAAM6G,SAAS,GAAGnO,QAAQ,CAACwG,MAAM,EAAE7C,KAAK,CAAC;MACzC,MAAMyK,cAAc,GAAG,OAAOD,SAAS,KAAK,QAAQ,GAAG;QACrD7E,QAAQ,EAAE6E,SAAS;QACnB7G,SAAS,EAAE;MACb,CAAC,GAAA7D,aAAA;QACC6F,QAAQ,EAAE,CAAC;QACXhC,SAAS,EAAE;MAAC,GACT6G,SAAS,CACb;MACD,IAAI5E,aAAa,EAAE;QACjB,MAAM8E,GAAG,GAAG/E,QAAQ,KAAK,GAAG,GAAG,QAAQ,GAAG,OAAO;QACjD,MAAMgF,QAAQ,GAAG5L,KAAK,CAACxB,SAAS,CAACoI,QAAQ,CAAC,GAAG5G,KAAK,CAACvB,QAAQ,CAACkN,GAAG,CAAC,GAAGD,cAAc,CAAC9E,QAAQ;QAC1F,MAAMiF,QAAQ,GAAG7L,KAAK,CAACxB,SAAS,CAACoI,QAAQ,CAAC,GAAG5G,KAAK,CAACxB,SAAS,CAACmN,GAAG,CAAC,GAAGD,cAAc,CAAC9E,QAAQ;QAC3F,IAAIsE,aAAa,GAAGU,QAAQ,EAAE;UAC5BV,aAAa,GAAGU,QAAQ;QAC1B,CAAC,MAAM,IAAIV,aAAa,GAAGW,QAAQ,EAAE;UACnCX,aAAa,GAAGW,QAAQ;QAC1B;MACF;MACA,IAAI/E,cAAc,EAAE;QAClB,IAAI8D,qBAAqB,EAAEkB,sBAAsB;QACjD,MAAMH,GAAG,GAAG/E,QAAQ,KAAK,GAAG,GAAG,OAAO,GAAG,QAAQ;QACjD,MAAMmF,YAAY,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,CAACtB,QAAQ,CAACrN,OAAO,CAACkB,SAAS,CAAC,CAAC;QACjE,MAAMsN,QAAQ,GAAG5L,KAAK,CAACxB,SAAS,CAACoG,SAAS,CAAC,GAAG5E,KAAK,CAACvB,QAAQ,CAACkN,GAAG,CAAC,IAAII,YAAY,GAAG,CAAC,CAACnB,qBAAqB,GAAGzK,cAAc,CAAC2D,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG8G,qBAAqB,CAAChG,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAImH,YAAY,GAAG,CAAC,GAAGL,cAAc,CAAC9G,SAAS,CAAC;QACnP,MAAMiH,QAAQ,GAAG7L,KAAK,CAACxB,SAAS,CAACoG,SAAS,CAAC,GAAG5E,KAAK,CAACxB,SAAS,CAACmN,GAAG,CAAC,IAAII,YAAY,GAAG,CAAC,GAAG,CAAC,CAACD,sBAAsB,GAAG3L,cAAc,CAAC2D,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGgI,sBAAsB,CAAClH,SAAS,CAAC,KAAK,CAAC,CAAC,IAAImH,YAAY,GAAGL,cAAc,CAAC9G,SAAS,GAAG,CAAC,CAAC;QACtP,IAAIuG,cAAc,GAAGS,QAAQ,EAAE;UAC7BT,cAAc,GAAGS,QAAQ;QAC3B,CAAC,MAAM,IAAIT,cAAc,GAAGU,QAAQ,EAAE;UACpCV,cAAc,GAAGU,QAAQ;QAC3B;MACF;MACA,OAAO;QACL,CAACjF,QAAQ,GAAGsE,aAAa;QACzB,CAACtG,SAAS,GAAGuG;MACf,CAAC;IACH;EACF,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,MAAMa,IAAI,GAAG,SAAAA,CAAU9K,OAAO,EAAE;EAC9B,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IACtBA,OAAO,GAAG,CAAC,CAAC;EACd;EACA,OAAO;IACLX,IAAI,EAAE,MAAM;IACZW,OAAO;IACP,MAAMV,EAAEA,CAACS,KAAK,EAAE;MACd,IAAIgL,qBAAqB,EAAEC,sBAAsB;MACjD,MAAM;QACJ5N,SAAS;QACT0B,KAAK;QACLL,QAAQ;QACRmB;MACF,CAAC,GAAGG,KAAK;MACT,MAAAkL,UAAA,GAGI7O,QAAQ,CAAC4D,OAAO,EAAED,KAAK,CAAC;QAHtB;UACJmL,KAAK,GAAGA,CAAA,KAAM,CAAC;QAEjB,CAAC,GAAAD,UAAA;QADItH,qBAAqB,GAAAC,wBAAA,CAAAqH,UAAA,EAAAE,UAAA;MAE1B,MAAMnH,QAAQ,GAAG,MAAMlE,cAAc,CAACC,KAAK,EAAE4D,qBAAqB,CAAC;MACnE,MAAMhG,IAAI,GAAGzB,OAAO,CAACkB,SAAS,CAAC;MAC/B,MAAM6F,SAAS,GAAG9G,YAAY,CAACiB,SAAS,CAAC;MACzC,MAAM0E,OAAO,GAAG/F,WAAW,CAACqB,SAAS,CAAC,KAAK,GAAG;MAC9C,MAAM;QACJW,KAAK;QACLG;MACF,CAAC,GAAGY,KAAK,CAACvB,QAAQ;MAClB,IAAI6N,UAAU;MACd,IAAIC,SAAS;MACb,IAAI1N,IAAI,KAAK,KAAK,IAAIA,IAAI,KAAK,QAAQ,EAAE;QACvCyN,UAAU,GAAGzN,IAAI;QACjB0N,SAAS,GAAGpI,SAAS,MAAM,CAAC,OAAOxE,QAAQ,CAACI,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGJ,QAAQ,CAACI,KAAK,CAACe,QAAQ,CAACrC,QAAQ,CAAC,CAAC,IAAI,OAAO,GAAG,KAAK,CAAC,GAAG,MAAM,GAAG,OAAO;MAChJ,CAAC,MAAM;QACL8N,SAAS,GAAG1N,IAAI;QAChByN,UAAU,GAAGnI,SAAS,KAAK,KAAK,GAAG,KAAK,GAAG,QAAQ;MACrD;MACA,MAAMqI,qBAAqB,GAAGpN,MAAM,GAAG8F,QAAQ,CAAC1C,GAAG,GAAG0C,QAAQ,CAACzC,MAAM;MACrE,MAAMgK,oBAAoB,GAAGxN,KAAK,GAAGiG,QAAQ,CAACxC,IAAI,GAAGwC,QAAQ,CAACvC,KAAK;MACnE,MAAM+J,uBAAuB,GAAGjP,GAAG,CAAC2B,MAAM,GAAG8F,QAAQ,CAACoH,UAAU,CAAC,EAAEE,qBAAqB,CAAC;MACzF,MAAMG,sBAAsB,GAAGlP,GAAG,CAACwB,KAAK,GAAGiG,QAAQ,CAACqH,SAAS,CAAC,EAAEE,oBAAoB,CAAC;MACrF,MAAMG,OAAO,GAAG,CAAC3L,KAAK,CAACd,cAAc,CAAC2K,KAAK;MAC3C,IAAI+B,eAAe,GAAGH,uBAAuB;MAC7C,IAAII,cAAc,GAAGH,sBAAsB;MAC3C,IAAI,CAACV,qBAAqB,GAAGhL,KAAK,CAACd,cAAc,CAAC2K,KAAK,KAAK,IAAI,IAAImB,qBAAqB,CAACV,OAAO,CAACvM,CAAC,EAAE;QACnG8N,cAAc,GAAGL,oBAAoB;MACvC;MACA,IAAI,CAACP,sBAAsB,GAAGjL,KAAK,CAACd,cAAc,CAAC2K,KAAK,KAAK,IAAI,IAAIoB,sBAAsB,CAACX,OAAO,CAACpM,CAAC,EAAE;QACrG0N,eAAe,GAAGL,qBAAqB;MACzC;MACA,IAAII,OAAO,IAAI,CAACzI,SAAS,EAAE;QACzB,MAAM4I,IAAI,GAAG7O,GAAG,CAACgH,QAAQ,CAACxC,IAAI,EAAE,CAAC,CAAC;QAClC,MAAMsK,IAAI,GAAG9O,GAAG,CAACgH,QAAQ,CAACvC,KAAK,EAAE,CAAC,CAAC;QACnC,MAAMsK,IAAI,GAAG/O,GAAG,CAACgH,QAAQ,CAAC1C,GAAG,EAAE,CAAC,CAAC;QACjC,MAAM0K,IAAI,GAAGhP,GAAG,CAACgH,QAAQ,CAACzC,MAAM,EAAE,CAAC,CAAC;QACpC,IAAIO,OAAO,EAAE;UACX8J,cAAc,GAAG7N,KAAK,GAAG,CAAC,IAAI8N,IAAI,KAAK,CAAC,IAAIC,IAAI,KAAK,CAAC,GAAGD,IAAI,GAAGC,IAAI,GAAG9O,GAAG,CAACgH,QAAQ,CAACxC,IAAI,EAAEwC,QAAQ,CAACvC,KAAK,CAAC,CAAC;QAC5G,CAAC,MAAM;UACLkK,eAAe,GAAGzN,MAAM,GAAG,CAAC,IAAI6N,IAAI,KAAK,CAAC,IAAIC,IAAI,KAAK,CAAC,GAAGD,IAAI,GAAGC,IAAI,GAAGhP,GAAG,CAACgH,QAAQ,CAAC1C,GAAG,EAAE0C,QAAQ,CAACzC,MAAM,CAAC,CAAC;QAC9G;MACF;MACA,MAAM2J,KAAK,CAAArL,aAAA,CAAAA,aAAA,KACNE,KAAK;QACR6L,cAAc;QACdD;MAAe,EAChB,CAAC;MACF,MAAMM,cAAc,GAAG,MAAMxN,QAAQ,CAACoD,aAAa,CAACjC,QAAQ,CAACrC,QAAQ,CAAC;MACtE,IAAIQ,KAAK,KAAKkO,cAAc,CAAClO,KAAK,IAAIG,MAAM,KAAK+N,cAAc,CAAC/N,MAAM,EAAE;QACtE,OAAO;UACLwB,KAAK,EAAE;YACLZ,KAAK,EAAE;UACT;QACF,CAAC;MACH;MACA,OAAO,CAAC,CAAC;IACX;EACF,CAAC;AACH,CAAC;AAED,SAAS4C,KAAK,EAAE2B,aAAa,EAAEhF,eAAe,EAAEyB,cAAc,EAAEwF,IAAI,EAAE6B,IAAI,EAAEiB,MAAM,EAAEkC,UAAU,EAAE1H,MAAM,EAAEgH,KAAK,EAAEkB,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}