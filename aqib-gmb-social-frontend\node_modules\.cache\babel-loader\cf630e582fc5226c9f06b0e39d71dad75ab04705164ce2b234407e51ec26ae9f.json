{"ast": null, "code": "import * as React from 'react';\nimport { unstable_useEnhancedEffect as useEnhancedEffect } from '@mui/utils';\nimport { arrayIncludes } from \"../utils/utils.js\";\nfunction getOrientation() {\n  if (typeof window === 'undefined') {\n    return 'portrait';\n  }\n  if (window.screen && window.screen.orientation && window.screen.orientation.angle) {\n    return Math.abs(window.screen.orientation.angle) === 90 ? 'landscape' : 'portrait';\n  }\n\n  // Support IOS safari\n  if (window.orientation) {\n    return Math.abs(Number(window.orientation)) === 90 ? 'landscape' : 'portrait';\n  }\n  return 'portrait';\n}\nexport const useIsLandscape = (views, customOrientation) => {\n  const [orientation, setOrientation] = React.useState(getOrientation);\n  useEnhancedEffect(() => {\n    const eventHandler = () => {\n      setOrientation(getOrientation());\n    };\n    window.addEventListener('orientationchange', eventHandler);\n    return () => {\n      window.removeEventListener('orientationchange', eventHandler);\n    };\n  }, []);\n  if (arrayIncludes(views, ['hours', 'minutes', 'seconds'])) {\n    // could not display 13:34:44 in landscape mode\n    return false;\n  }\n  const orientationToUse = customOrientation || orientation;\n  return orientationToUse === 'landscape';\n};", "map": {"version": 3, "names": ["React", "unstable_useEnhancedEffect", "useEnhancedEffect", "arrayIncludes", "getOrientation", "window", "screen", "orientation", "angle", "Math", "abs", "Number", "useIsLandscape", "views", "customOrientation", "setOrientation", "useState", "<PERSON><PERSON><PERSON><PERSON>", "addEventListener", "removeEventListener", "orientationToUse"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/node_modules/@mui/x-date-pickers/internals/hooks/useIsLandscape.js"], "sourcesContent": ["import * as React from 'react';\nimport { unstable_useEnhancedEffect as useEnhancedEffect } from '@mui/utils';\nimport { arrayIncludes } from \"../utils/utils.js\";\nfunction getOrientation() {\n  if (typeof window === 'undefined') {\n    return 'portrait';\n  }\n  if (window.screen && window.screen.orientation && window.screen.orientation.angle) {\n    return Math.abs(window.screen.orientation.angle) === 90 ? 'landscape' : 'portrait';\n  }\n\n  // Support IOS safari\n  if (window.orientation) {\n    return Math.abs(Number(window.orientation)) === 90 ? 'landscape' : 'portrait';\n  }\n  return 'portrait';\n}\nexport const useIsLandscape = (views, customOrientation) => {\n  const [orientation, setOrientation] = React.useState(getOrientation);\n  useEnhancedEffect(() => {\n    const eventHandler = () => {\n      setOrientation(getOrientation());\n    };\n    window.addEventListener('orientationchange', eventHandler);\n    return () => {\n      window.removeEventListener('orientationchange', eventHandler);\n    };\n  }, []);\n  if (arrayIncludes(views, ['hours', 'minutes', 'seconds'])) {\n    // could not display 13:34:44 in landscape mode\n    return false;\n  }\n  const orientationToUse = customOrientation || orientation;\n  return orientationToUse === 'landscape';\n};"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,0BAA0B,IAAIC,iBAAiB,QAAQ,YAAY;AAC5E,SAASC,aAAa,QAAQ,mBAAmB;AACjD,SAASC,cAAcA,CAAA,EAAG;EACxB,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE;IACjC,OAAO,UAAU;EACnB;EACA,IAAIA,MAAM,CAACC,MAAM,IAAID,MAAM,CAACC,MAAM,CAACC,WAAW,IAAIF,MAAM,CAACC,MAAM,CAACC,WAAW,CAACC,KAAK,EAAE;IACjF,OAAOC,IAAI,CAACC,GAAG,CAACL,MAAM,CAACC,MAAM,CAACC,WAAW,CAACC,KAAK,CAAC,KAAK,EAAE,GAAG,WAAW,GAAG,UAAU;EACpF;;EAEA;EACA,IAAIH,MAAM,CAACE,WAAW,EAAE;IACtB,OAAOE,IAAI,CAACC,GAAG,CAACC,MAAM,CAACN,MAAM,CAACE,WAAW,CAAC,CAAC,KAAK,EAAE,GAAG,WAAW,GAAG,UAAU;EAC/E;EACA,OAAO,UAAU;AACnB;AACA,OAAO,MAAMK,cAAc,GAAGA,CAACC,KAAK,EAAEC,iBAAiB,KAAK;EAC1D,MAAM,CAACP,WAAW,EAAEQ,cAAc,CAAC,GAAGf,KAAK,CAACgB,QAAQ,CAACZ,cAAc,CAAC;EACpEF,iBAAiB,CAAC,MAAM;IACtB,MAAMe,YAAY,GAAGA,CAAA,KAAM;MACzBF,cAAc,CAACX,cAAc,CAAC,CAAC,CAAC;IAClC,CAAC;IACDC,MAAM,CAACa,gBAAgB,CAAC,mBAAmB,EAAED,YAAY,CAAC;IAC1D,OAAO,MAAM;MACXZ,MAAM,CAACc,mBAAmB,CAAC,mBAAmB,EAAEF,YAAY,CAAC;IAC/D,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN,IAAId,aAAa,CAACU,KAAK,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC,EAAE;IACzD;IACA,OAAO,KAAK;EACd;EACA,MAAMO,gBAAgB,GAAGN,iBAAiB,IAAIP,WAAW;EACzD,OAAOa,gBAAgB,KAAK,WAAW;AACzC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}