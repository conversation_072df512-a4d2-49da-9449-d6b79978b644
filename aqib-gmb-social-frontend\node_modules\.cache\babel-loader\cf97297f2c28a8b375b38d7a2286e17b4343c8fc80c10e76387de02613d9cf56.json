{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { useRtl } from '@mui/system/RtlProvider';\nimport { useIsLandscape } from \"../useIsLandscape.js\";\n\n/**\n * Props used to create the layout of the views.\n * Those props are exposed on all the pickers.\n */\n\n/**\n * Prepare the props for the view layout (managed by `PickersLayout`)\n */\nexport const usePickerLayoutProps = _ref => {\n  let {\n    props,\n    propsFromPickerValue,\n    propsFromPickerViews,\n    wrapperVariant\n  } = _ref;\n  const {\n    orientation\n  } = props;\n  const isLandscape = useIsLandscape(propsFromPickerViews.views, orientation);\n  const isRtl = useRtl();\n  const layoutProps = _extends({}, propsFromPickerViews, propsFromPickerValue, {\n    isLandscape,\n    isRtl,\n    wrapperVariant,\n    disabled: props.disabled,\n    readOnly: props.readOnly\n  });\n  return {\n    layoutProps\n  };\n};", "map": {"version": 3, "names": ["_extends", "useRtl", "useIsLandscape", "usePickerLayoutProps", "_ref", "props", "propsFromPickerValue", "propsFromPickerViews", "wrapperVariant", "orientation", "isLandscape", "views", "isRtl", "layoutProps", "disabled", "readOnly"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/node_modules/@mui/x-date-pickers/internals/hooks/usePicker/usePickerLayoutProps.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { useRtl } from '@mui/system/RtlProvider';\nimport { useIsLandscape } from \"../useIsLandscape.js\";\n\n/**\n * Props used to create the layout of the views.\n * Those props are exposed on all the pickers.\n */\n\n/**\n * Prepare the props for the view layout (managed by `PickersLayout`)\n */\nexport const usePickerLayoutProps = ({\n  props,\n  propsFromPickerValue,\n  propsFromPickerViews,\n  wrapperVariant\n}) => {\n  const {\n    orientation\n  } = props;\n  const isLandscape = useIsLandscape(propsFromPickerViews.views, orientation);\n  const isRtl = useRtl();\n  const layoutProps = _extends({}, propsFromPickerViews, propsFromPickerValue, {\n    isLandscape,\n    isRtl,\n    wrapperVariant,\n    disabled: props.disabled,\n    readOnly: props.readOnly\n  });\n  return {\n    layoutProps\n  };\n};"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,SAASC,MAAM,QAAQ,yBAAyB;AAChD,SAASC,cAAc,QAAQ,sBAAsB;;AAErD;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,OAAO,MAAMC,oBAAoB,GAAGC,IAAA,IAK9B;EAAA,IAL+B;IACnCC,KAAK;IACLC,oBAAoB;IACpBC,oBAAoB;IACpBC;EACF,CAAC,GAAAJ,IAAA;EACC,MAAM;IACJK;EACF,CAAC,GAAGJ,KAAK;EACT,MAAMK,WAAW,GAAGR,cAAc,CAACK,oBAAoB,CAACI,KAAK,EAAEF,WAAW,CAAC;EAC3E,MAAMG,KAAK,GAAGX,MAAM,CAAC,CAAC;EACtB,MAAMY,WAAW,GAAGb,QAAQ,CAAC,CAAC,CAAC,EAAEO,oBAAoB,EAAED,oBAAoB,EAAE;IAC3EI,WAAW;IACXE,KAAK;IACLJ,cAAc;IACdM,QAAQ,EAAET,KAAK,CAACS,QAAQ;IACxBC,QAAQ,EAAEV,KAAK,CAACU;EAClB,CAAC,CAAC;EACF,OAAO;IACLF;EACF,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}