{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { unstable_ownerDocument as ownerDocument, unstable_useControlled as useControlled, unstable_useEnhancedEffect as useEnhancedEffect, unstable_useEventCallback as useEventCallback, unstable_useForkRef as useForkRef, unstable_useIsFocusVisible as useIsFocusVisible, visuallyHidden, clamp } from '@mui/utils';\nimport { areArraysEqual, extractEventHandlers } from '../utils';\nconst INTENTIONAL_DRAG_COUNT_THRESHOLD = 2;\nfunction asc(a, b) {\n  return a - b;\n}\nfunction findClosest(values, currentValue) {\n  var _values$reduce;\n  const {\n    index: closestIndex\n  } = (_values$reduce = values.reduce((acc, value, index) => {\n    const distance = Math.abs(currentValue - value);\n    if (acc === null || distance < acc.distance || distance === acc.distance) {\n      return {\n        distance,\n        index\n      };\n    }\n    return acc;\n  }, null)) != null ? _values$reduce : {};\n  return closestIndex;\n}\nfunction trackFinger(event, touchId) {\n  // The event is TouchEvent\n  if (touchId.current !== undefined && event.changedTouches) {\n    const touchEvent = event;\n    for (let i = 0; i < touchEvent.changedTouches.length; i += 1) {\n      const touch = touchEvent.changedTouches[i];\n      if (touch.identifier === touchId.current) {\n        return {\n          x: touch.clientX,\n          y: touch.clientY\n        };\n      }\n    }\n    return false;\n  }\n\n  // The event is MouseEvent\n  return {\n    x: event.clientX,\n    y: event.clientY\n  };\n}\nexport function valueToPercent(value, min, max) {\n  return (value - min) * 100 / (max - min);\n}\nfunction percentToValue(percent, min, max) {\n  return (max - min) * percent + min;\n}\nfunction getDecimalPrecision(num) {\n  // This handles the case when num is very small (0.00000001), js will turn this into 1e-8.\n  // When num is bigger than 1 or less than -1 it won't get converted to this notation so it's fine.\n  if (Math.abs(num) < 1) {\n    const parts = num.toExponential().split('e-');\n    const matissaDecimalPart = parts[0].split('.')[1];\n    return (matissaDecimalPart ? matissaDecimalPart.length : 0) + parseInt(parts[1], 10);\n  }\n  const decimalPart = num.toString().split('.')[1];\n  return decimalPart ? decimalPart.length : 0;\n}\nfunction roundValueToStep(value, step, min) {\n  const nearest = Math.round((value - min) / step) * step + min;\n  return Number(nearest.toFixed(getDecimalPrecision(step)));\n}\nfunction setValueIndex(_ref) {\n  let {\n    values,\n    newValue,\n    index\n  } = _ref;\n  const output = values.slice();\n  output[index] = newValue;\n  return output.sort(asc);\n}\nfunction focusThumb(_ref2) {\n  let {\n    sliderRef,\n    activeIndex,\n    setActive\n  } = _ref2;\n  var _sliderRef$current, _doc$activeElement;\n  const doc = ownerDocument(sliderRef.current);\n  if (!((_sliderRef$current = sliderRef.current) != null && _sliderRef$current.contains(doc.activeElement)) || Number(doc == null || (_doc$activeElement = doc.activeElement) == null ? void 0 : _doc$activeElement.getAttribute('data-index')) !== activeIndex) {\n    var _sliderRef$current2;\n    (_sliderRef$current2 = sliderRef.current) == null || _sliderRef$current2.querySelector(\"[type=\\\"range\\\"][data-index=\\\"\".concat(activeIndex, \"\\\"]\")).focus();\n  }\n  if (setActive) {\n    setActive(activeIndex);\n  }\n}\nfunction areValuesEqual(newValue, oldValue) {\n  if (typeof newValue === 'number' && typeof oldValue === 'number') {\n    return newValue === oldValue;\n  }\n  if (typeof newValue === 'object' && typeof oldValue === 'object') {\n    return areArraysEqual(newValue, oldValue);\n  }\n  return false;\n}\nconst axisProps = {\n  horizontal: {\n    offset: percent => ({\n      left: \"\".concat(percent, \"%\")\n    }),\n    leap: percent => ({\n      width: \"\".concat(percent, \"%\")\n    })\n  },\n  'horizontal-reverse': {\n    offset: percent => ({\n      right: \"\".concat(percent, \"%\")\n    }),\n    leap: percent => ({\n      width: \"\".concat(percent, \"%\")\n    })\n  },\n  vertical: {\n    offset: percent => ({\n      bottom: \"\".concat(percent, \"%\")\n    }),\n    leap: percent => ({\n      height: \"\".concat(percent, \"%\")\n    })\n  }\n};\nexport const Identity = x => x;\n\n// TODO: remove support for Safari < 13.\n// https://caniuse.com/#search=touch-action\n//\n// Safari, on iOS, supports touch action since v13.\n// Over 80% of the iOS phones are compatible\n// in August 2020.\n// Utilizing the CSS.supports method to check if touch-action is supported.\n// Since CSS.supports is supported on all but Edge@12 and IE and touch-action\n// is supported on both Edge@12 and IE if CSS.supports is not available that means that\n// touch-action will be supported\nlet cachedSupportsTouchActionNone;\nfunction doesSupportTouchActionNone() {\n  if (cachedSupportsTouchActionNone === undefined) {\n    if (typeof CSS !== 'undefined' && typeof CSS.supports === 'function') {\n      cachedSupportsTouchActionNone = CSS.supports('touch-action', 'none');\n    } else {\n      cachedSupportsTouchActionNone = true;\n    }\n  }\n  return cachedSupportsTouchActionNone;\n}\n/**\n *\n * Demos:\n *\n * - [Slider](https://mui.com/base-ui/react-slider/#hook)\n *\n * API:\n *\n * - [useSlider API](https://mui.com/base-ui/react-slider/hooks-api/#use-slider)\n */\nexport function useSlider(parameters) {\n  const {\n    'aria-labelledby': ariaLabelledby,\n    defaultValue,\n    disabled = false,\n    disableSwap = false,\n    isRtl = false,\n    marks: marksProp = false,\n    max = 100,\n    min = 0,\n    name,\n    onChange,\n    onChangeCommitted,\n    orientation = 'horizontal',\n    rootRef: ref,\n    scale = Identity,\n    step = 1,\n    shiftStep = 10,\n    tabIndex,\n    value: valueProp\n  } = parameters;\n  const touchId = React.useRef(undefined);\n  // We can't use the :active browser pseudo-classes.\n  // - The active state isn't triggered when clicking on the rail.\n  // - The active state isn't transferred when inversing a range slider.\n  const [active, setActive] = React.useState(-1);\n  const [open, setOpen] = React.useState(-1);\n  const [dragging, setDragging] = React.useState(false);\n  const moveCount = React.useRef(0);\n  const [valueDerived, setValueState] = useControlled({\n    controlled: valueProp,\n    default: defaultValue != null ? defaultValue : min,\n    name: 'Slider'\n  });\n  const handleChange = onChange && ((event, value, thumbIndex) => {\n    // Redefine target to allow name and value to be read.\n    // This allows seamless integration with the most popular form libraries.\n    // https://github.com/mui/material-ui/issues/13485#issuecomment-676048492\n    // Clone the event to not override `target` of the original event.\n    const nativeEvent = event.nativeEvent || event;\n    // @ts-ignore The nativeEvent is function, not object\n    const clonedEvent = new nativeEvent.constructor(nativeEvent.type, nativeEvent);\n    Object.defineProperty(clonedEvent, 'target', {\n      writable: true,\n      value: {\n        value,\n        name\n      }\n    });\n    onChange(clonedEvent, value, thumbIndex);\n  });\n  const range = Array.isArray(valueDerived);\n  let values = range ? valueDerived.slice().sort(asc) : [valueDerived];\n  values = values.map(value => value == null ? min : clamp(value, min, max));\n  const marks = marksProp === true && step !== null ? [...Array(Math.floor((max - min) / step) + 1)].map((_, index) => ({\n    value: min + step * index\n  })) : marksProp || [];\n  const marksValues = marks.map(mark => mark.value);\n  const {\n    isFocusVisibleRef,\n    onBlur: handleBlurVisible,\n    onFocus: handleFocusVisible,\n    ref: focusVisibleRef\n  } = useIsFocusVisible();\n  const [focusedThumbIndex, setFocusedThumbIndex] = React.useState(-1);\n  const sliderRef = React.useRef(null);\n  const handleFocusRef = useForkRef(focusVisibleRef, sliderRef);\n  const handleRef = useForkRef(ref, handleFocusRef);\n  const createHandleHiddenInputFocus = otherHandlers => event => {\n    var _otherHandlers$onFocu;\n    const index = Number(event.currentTarget.getAttribute('data-index'));\n    handleFocusVisible(event);\n    if (isFocusVisibleRef.current === true) {\n      setFocusedThumbIndex(index);\n    }\n    setOpen(index);\n    otherHandlers == null || (_otherHandlers$onFocu = otherHandlers.onFocus) == null || _otherHandlers$onFocu.call(otherHandlers, event);\n  };\n  const createHandleHiddenInputBlur = otherHandlers => event => {\n    var _otherHandlers$onBlur;\n    handleBlurVisible(event);\n    if (isFocusVisibleRef.current === false) {\n      setFocusedThumbIndex(-1);\n    }\n    setOpen(-1);\n    otherHandlers == null || (_otherHandlers$onBlur = otherHandlers.onBlur) == null || _otherHandlers$onBlur.call(otherHandlers, event);\n  };\n  const changeValue = (event, valueInput) => {\n    const index = Number(event.currentTarget.getAttribute('data-index'));\n    const value = values[index];\n    const marksIndex = marksValues.indexOf(value);\n    let newValue = valueInput;\n    if (marks && step == null) {\n      const maxMarksValue = marksValues[marksValues.length - 1];\n      if (newValue > maxMarksValue) {\n        newValue = maxMarksValue;\n      } else if (newValue < marksValues[0]) {\n        newValue = marksValues[0];\n      } else {\n        newValue = newValue < value ? marksValues[marksIndex - 1] : marksValues[marksIndex + 1];\n      }\n    }\n    newValue = clamp(newValue, min, max);\n    if (range) {\n      // Bound the new value to the thumb's neighbours.\n      if (disableSwap) {\n        newValue = clamp(newValue, values[index - 1] || -Infinity, values[index + 1] || Infinity);\n      }\n      const previousValue = newValue;\n      newValue = setValueIndex({\n        values,\n        newValue,\n        index\n      });\n      let activeIndex = index;\n\n      // Potentially swap the index if needed.\n      if (!disableSwap) {\n        activeIndex = newValue.indexOf(previousValue);\n      }\n      focusThumb({\n        sliderRef,\n        activeIndex\n      });\n    }\n    setValueState(newValue);\n    setFocusedThumbIndex(index);\n    if (handleChange && !areValuesEqual(newValue, valueDerived)) {\n      handleChange(event, newValue, index);\n    }\n    if (onChangeCommitted) {\n      onChangeCommitted(event, newValue);\n    }\n  };\n  const createHandleHiddenInputKeyDown = otherHandlers => event => {\n    var _otherHandlers$onKeyD;\n    // The Shift + Up/Down keyboard shortcuts for moving the slider makes sense to be supported\n    // only if the step is defined. If the step is null, this means tha the marks are used for specifying the valid values.\n    if (step !== null) {\n      const index = Number(event.currentTarget.getAttribute('data-index'));\n      const value = values[index];\n      let newValue = null;\n      if ((event.key === 'ArrowLeft' || event.key === 'ArrowDown') && event.shiftKey || event.key === 'PageDown') {\n        newValue = Math.max(value - shiftStep, min);\n      } else if ((event.key === 'ArrowRight' || event.key === 'ArrowUp') && event.shiftKey || event.key === 'PageUp') {\n        newValue = Math.min(value + shiftStep, max);\n      }\n      if (newValue !== null) {\n        changeValue(event, newValue);\n        event.preventDefault();\n      }\n    }\n    otherHandlers == null || (_otherHandlers$onKeyD = otherHandlers.onKeyDown) == null || _otherHandlers$onKeyD.call(otherHandlers, event);\n  };\n  useEnhancedEffect(() => {\n    if (disabled && sliderRef.current.contains(document.activeElement)) {\n      var _document$activeEleme;\n      // This is necessary because Firefox and Safari will keep focus\n      // on a disabled element:\n      // https://codesandbox.io/p/sandbox/mui-pr-22247-forked-h151h?file=/src/App.js\n      // @ts-ignore\n      (_document$activeEleme = document.activeElement) == null || _document$activeEleme.blur();\n    }\n  }, [disabled]);\n  if (disabled && active !== -1) {\n    setActive(-1);\n  }\n  if (disabled && focusedThumbIndex !== -1) {\n    setFocusedThumbIndex(-1);\n  }\n  const createHandleHiddenInputChange = otherHandlers => event => {\n    var _otherHandlers$onChan;\n    (_otherHandlers$onChan = otherHandlers.onChange) == null || _otherHandlers$onChan.call(otherHandlers, event);\n    // @ts-ignore\n    changeValue(event, event.target.valueAsNumber);\n  };\n  const previousIndex = React.useRef(undefined);\n  let axis = orientation;\n  if (isRtl && orientation === 'horizontal') {\n    axis += '-reverse';\n  }\n  const getFingerNewValue = _ref3 => {\n    let {\n      finger,\n      move = false\n    } = _ref3;\n    const {\n      current: slider\n    } = sliderRef;\n    const {\n      width,\n      height,\n      bottom,\n      left\n    } = slider.getBoundingClientRect();\n    let percent;\n    if (axis.indexOf('vertical') === 0) {\n      percent = (bottom - finger.y) / height;\n    } else {\n      percent = (finger.x - left) / width;\n    }\n    if (axis.indexOf('-reverse') !== -1) {\n      percent = 1 - percent;\n    }\n    let newValue;\n    newValue = percentToValue(percent, min, max);\n    if (step) {\n      newValue = roundValueToStep(newValue, step, min);\n    } else {\n      const closestIndex = findClosest(marksValues, newValue);\n      newValue = marksValues[closestIndex];\n    }\n    newValue = clamp(newValue, min, max);\n    let activeIndex = 0;\n    if (range) {\n      if (!move) {\n        activeIndex = findClosest(values, newValue);\n      } else {\n        activeIndex = previousIndex.current;\n      }\n\n      // Bound the new value to the thumb's neighbours.\n      if (disableSwap) {\n        newValue = clamp(newValue, values[activeIndex - 1] || -Infinity, values[activeIndex + 1] || Infinity);\n      }\n      const previousValue = newValue;\n      newValue = setValueIndex({\n        values,\n        newValue,\n        index: activeIndex\n      });\n\n      // Potentially swap the index if needed.\n      if (!(disableSwap && move)) {\n        activeIndex = newValue.indexOf(previousValue);\n        previousIndex.current = activeIndex;\n      }\n    }\n    return {\n      newValue,\n      activeIndex\n    };\n  };\n  const handleTouchMove = useEventCallback(nativeEvent => {\n    const finger = trackFinger(nativeEvent, touchId);\n    if (!finger) {\n      return;\n    }\n    moveCount.current += 1;\n\n    // Cancel move in case some other element consumed a mouseup event and it was not fired.\n    // @ts-ignore buttons doesn't not exists on touch event\n    if (nativeEvent.type === 'mousemove' && nativeEvent.buttons === 0) {\n      // eslint-disable-next-line @typescript-eslint/no-use-before-define\n      handleTouchEnd(nativeEvent);\n      return;\n    }\n    const {\n      newValue,\n      activeIndex\n    } = getFingerNewValue({\n      finger,\n      move: true\n    });\n    focusThumb({\n      sliderRef,\n      activeIndex,\n      setActive\n    });\n    setValueState(newValue);\n    if (!dragging && moveCount.current > INTENTIONAL_DRAG_COUNT_THRESHOLD) {\n      setDragging(true);\n    }\n    if (handleChange && !areValuesEqual(newValue, valueDerived)) {\n      handleChange(nativeEvent, newValue, activeIndex);\n    }\n  });\n  const handleTouchEnd = useEventCallback(nativeEvent => {\n    const finger = trackFinger(nativeEvent, touchId);\n    setDragging(false);\n    if (!finger) {\n      return;\n    }\n    const {\n      newValue\n    } = getFingerNewValue({\n      finger,\n      move: true\n    });\n    setActive(-1);\n    if (nativeEvent.type === 'touchend') {\n      setOpen(-1);\n    }\n    if (onChangeCommitted) {\n      onChangeCommitted(nativeEvent, newValue);\n    }\n    touchId.current = undefined;\n\n    // eslint-disable-next-line @typescript-eslint/no-use-before-define\n    stopListening();\n  });\n  const handleTouchStart = useEventCallback(nativeEvent => {\n    if (disabled) {\n      return;\n    }\n    // If touch-action: none; is not supported we need to prevent the scroll manually.\n    if (!doesSupportTouchActionNone()) {\n      nativeEvent.preventDefault();\n    }\n    const touch = nativeEvent.changedTouches[0];\n    if (touch != null) {\n      // A number that uniquely identifies the current finger in the touch session.\n      touchId.current = touch.identifier;\n    }\n    const finger = trackFinger(nativeEvent, touchId);\n    if (finger !== false) {\n      const {\n        newValue,\n        activeIndex\n      } = getFingerNewValue({\n        finger\n      });\n      focusThumb({\n        sliderRef,\n        activeIndex,\n        setActive\n      });\n      setValueState(newValue);\n      if (handleChange && !areValuesEqual(newValue, valueDerived)) {\n        handleChange(nativeEvent, newValue, activeIndex);\n      }\n    }\n    moveCount.current = 0;\n    const doc = ownerDocument(sliderRef.current);\n    doc.addEventListener('touchmove', handleTouchMove, {\n      passive: true\n    });\n    doc.addEventListener('touchend', handleTouchEnd, {\n      passive: true\n    });\n  });\n  const stopListening = React.useCallback(() => {\n    const doc = ownerDocument(sliderRef.current);\n    doc.removeEventListener('mousemove', handleTouchMove);\n    doc.removeEventListener('mouseup', handleTouchEnd);\n    doc.removeEventListener('touchmove', handleTouchMove);\n    doc.removeEventListener('touchend', handleTouchEnd);\n  }, [handleTouchEnd, handleTouchMove]);\n  React.useEffect(() => {\n    const {\n      current: slider\n    } = sliderRef;\n    slider.addEventListener('touchstart', handleTouchStart, {\n      passive: doesSupportTouchActionNone()\n    });\n    return () => {\n      slider.removeEventListener('touchstart', handleTouchStart);\n      stopListening();\n    };\n  }, [stopListening, handleTouchStart]);\n  React.useEffect(() => {\n    if (disabled) {\n      stopListening();\n    }\n  }, [disabled, stopListening]);\n  const createHandleMouseDown = otherHandlers => event => {\n    var _otherHandlers$onMous;\n    (_otherHandlers$onMous = otherHandlers.onMouseDown) == null || _otherHandlers$onMous.call(otherHandlers, event);\n    if (disabled) {\n      return;\n    }\n    if (event.defaultPrevented) {\n      return;\n    }\n\n    // Only handle left clicks\n    if (event.button !== 0) {\n      return;\n    }\n\n    // Avoid text selection\n    event.preventDefault();\n    const finger = trackFinger(event, touchId);\n    if (finger !== false) {\n      const {\n        newValue,\n        activeIndex\n      } = getFingerNewValue({\n        finger\n      });\n      focusThumb({\n        sliderRef,\n        activeIndex,\n        setActive\n      });\n      setValueState(newValue);\n      if (handleChange && !areValuesEqual(newValue, valueDerived)) {\n        handleChange(event, newValue, activeIndex);\n      }\n    }\n    moveCount.current = 0;\n    const doc = ownerDocument(sliderRef.current);\n    doc.addEventListener('mousemove', handleTouchMove, {\n      passive: true\n    });\n    doc.addEventListener('mouseup', handleTouchEnd);\n  };\n  const trackOffset = valueToPercent(range ? values[0] : min, min, max);\n  const trackLeap = valueToPercent(values[values.length - 1], min, max) - trackOffset;\n  const getRootProps = function () {\n    let externalProps = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    const externalHandlers = extractEventHandlers(externalProps);\n    const ownEventHandlers = {\n      onMouseDown: createHandleMouseDown(externalHandlers || {})\n    };\n    const mergedEventHandlers = _extends({}, externalHandlers, ownEventHandlers);\n    return _extends({}, externalProps, {\n      ref: handleRef\n    }, mergedEventHandlers);\n  };\n  const createHandleMouseOver = otherHandlers => event => {\n    var _otherHandlers$onMous2;\n    (_otherHandlers$onMous2 = otherHandlers.onMouseOver) == null || _otherHandlers$onMous2.call(otherHandlers, event);\n    const index = Number(event.currentTarget.getAttribute('data-index'));\n    setOpen(index);\n  };\n  const createHandleMouseLeave = otherHandlers => event => {\n    var _otherHandlers$onMous3;\n    (_otherHandlers$onMous3 = otherHandlers.onMouseLeave) == null || _otherHandlers$onMous3.call(otherHandlers, event);\n    setOpen(-1);\n  };\n  const getThumbProps = function () {\n    let externalProps = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    const externalHandlers = extractEventHandlers(externalProps);\n    const ownEventHandlers = {\n      onMouseOver: createHandleMouseOver(externalHandlers || {}),\n      onMouseLeave: createHandleMouseLeave(externalHandlers || {})\n    };\n    return _extends({}, externalProps, externalHandlers, ownEventHandlers);\n  };\n  const getThumbStyle = index => {\n    return {\n      // So the non active thumb doesn't show its label on hover.\n      pointerEvents: active !== -1 && active !== index ? 'none' : undefined\n    };\n  };\n  const getHiddenInputProps = function () {\n    let externalProps = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    var _parameters$step;\n    const externalHandlers = extractEventHandlers(externalProps);\n    const ownEventHandlers = {\n      onChange: createHandleHiddenInputChange(externalHandlers || {}),\n      onFocus: createHandleHiddenInputFocus(externalHandlers || {}),\n      onBlur: createHandleHiddenInputBlur(externalHandlers || {}),\n      onKeyDown: createHandleHiddenInputKeyDown(externalHandlers || {})\n    };\n    const mergedEventHandlers = _extends({}, externalHandlers, ownEventHandlers);\n    return _extends({\n      tabIndex,\n      'aria-labelledby': ariaLabelledby,\n      'aria-orientation': orientation,\n      'aria-valuemax': scale(max),\n      'aria-valuemin': scale(min),\n      name,\n      type: 'range',\n      min: parameters.min,\n      max: parameters.max,\n      step: parameters.step === null && parameters.marks ? 'any' : (_parameters$step = parameters.step) != null ? _parameters$step : undefined,\n      disabled\n    }, externalProps, mergedEventHandlers, {\n      style: _extends({}, visuallyHidden, {\n        direction: isRtl ? 'rtl' : 'ltr',\n        // So that VoiceOver's focus indicator matches the thumb's dimensions\n        width: '100%',\n        height: '100%'\n      })\n    });\n  };\n  return {\n    active,\n    axis: axis,\n    axisProps,\n    dragging,\n    focusedThumbIndex,\n    getHiddenInputProps,\n    getRootProps,\n    getThumbProps,\n    marks: marks,\n    open,\n    range,\n    rootRef: handleRef,\n    trackLeap,\n    trackOffset,\n    values,\n    getThumbStyle\n  };\n}", "map": {"version": 3, "names": ["_extends", "React", "unstable_ownerDocument", "ownerDocument", "unstable_useControlled", "useControlled", "unstable_useEnhancedEffect", "useEnhancedEffect", "unstable_useEventCallback", "useEventCallback", "unstable_useForkRef", "useForkRef", "unstable_useIsFocusVisible", "useIsFocusVisible", "visuallyHidden", "clamp", "areArraysEqual", "extractEventHandlers", "INTENTIONAL_DRAG_COUNT_THRESHOLD", "asc", "a", "b", "findClosest", "values", "currentValue", "_values$reduce", "index", "closestIndex", "reduce", "acc", "value", "distance", "Math", "abs", "trackFinger", "event", "touchId", "current", "undefined", "changedTouches", "touchEvent", "i", "length", "touch", "identifier", "x", "clientX", "y", "clientY", "valueToPercent", "min", "max", "percentToValue", "percent", "getDecimalPrecision", "num", "parts", "toExponential", "split", "mat<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parseInt", "decimalPart", "toString", "roundValueToStep", "step", "nearest", "round", "Number", "toFixed", "setValueIndex", "_ref", "newValue", "output", "slice", "sort", "focusThumb", "_ref2", "sliderRef", "activeIndex", "setActive", "_sliderRef$current", "_doc$activeElement", "doc", "contains", "activeElement", "getAttribute", "_sliderRef$current2", "querySelector", "concat", "focus", "areValuesEqual", "oldValue", "axisProps", "horizontal", "offset", "left", "leap", "width", "right", "vertical", "bottom", "height", "Identity", "cachedSupportsTouchActionNone", "doesSupportTouchActionNone", "CSS", "supports", "useSlider", "parameters", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "defaultValue", "disabled", "disableSwap", "isRtl", "marks", "marksProp", "name", "onChange", "onChangeCommitted", "orientation", "rootRef", "ref", "scale", "shiftStep", "tabIndex", "valueProp", "useRef", "active", "useState", "open", "<PERSON><PERSON><PERSON>", "dragging", "setDragging", "moveCount", "valueDerived", "setValueState", "controlled", "default", "handleChange", "thumbIndex", "nativeEvent", "clonedEvent", "constructor", "type", "Object", "defineProperty", "writable", "range", "Array", "isArray", "map", "floor", "_", "marksV<PERSON>ues", "mark", "isFocusVisibleRef", "onBlur", "handleBlurVisible", "onFocus", "handleFocusVisible", "focusVisibleRef", "focusedThumbIndex", "setFocusedThumbIndex", "handleFocusRef", "handleRef", "createHandleHiddenInputFocus", "otherHandlers", "_otherHandlers$onFocu", "currentTarget", "call", "createHandleHiddenInputBlur", "_otherHandlers$onBlur", "changeValue", "valueInput", "marksIndex", "indexOf", "maxMarksValue", "Infinity", "previousValue", "createHandleHiddenInputKeyDown", "_otherHandlers$onKeyD", "key", "shift<PERSON>ey", "preventDefault", "onKeyDown", "document", "_document$activeEleme", "blur", "createHandleHiddenInputChange", "_otherHandlers$onChan", "target", "valueAsNumber", "previousIndex", "axis", "getFingerNewValue", "_ref3", "finger", "move", "slider", "getBoundingClientRect", "handleTouchMove", "buttons", "handleTouchEnd", "stopListening", "handleTouchStart", "addEventListener", "passive", "useCallback", "removeEventListener", "useEffect", "createHandleMouseDown", "_otherHandlers$onMous", "onMouseDown", "defaultPrevented", "button", "trackOffset", "trackLeap", "getRootProps", "externalProps", "arguments", "externalHandlers", "ownEventHandlers", "mergedEventHandlers", "createHandleMouseOver", "_otherHandlers$onMous2", "onMouseOver", "createHandleMouseLeave", "_otherHandlers$onMous3", "onMouseLeave", "getThumbProps", "getThumbStyle", "pointerEvents", "getHiddenInputProps", "_parameters$step", "style", "direction"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/node_modules/@mui/base/useSlider/useSlider.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { unstable_ownerDocument as ownerDocument, unstable_useControlled as useControlled, unstable_useEnhancedEffect as useEnhancedEffect, unstable_useEventCallback as useEventCallback, unstable_useForkRef as useForkRef, unstable_useIsFocusVisible as useIsFocusVisible, visuallyHidden, clamp } from '@mui/utils';\nimport { areArraysEqual, extractEventHandlers } from '../utils';\nconst INTENTIONAL_DRAG_COUNT_THRESHOLD = 2;\nfunction asc(a, b) {\n  return a - b;\n}\nfunction findClosest(values, currentValue) {\n  var _values$reduce;\n  const {\n    index: closestIndex\n  } = (_values$reduce = values.reduce((acc, value, index) => {\n    const distance = Math.abs(currentValue - value);\n    if (acc === null || distance < acc.distance || distance === acc.distance) {\n      return {\n        distance,\n        index\n      };\n    }\n    return acc;\n  }, null)) != null ? _values$reduce : {};\n  return closestIndex;\n}\nfunction trackFinger(event, touchId) {\n  // The event is TouchEvent\n  if (touchId.current !== undefined && event.changedTouches) {\n    const touchEvent = event;\n    for (let i = 0; i < touchEvent.changedTouches.length; i += 1) {\n      const touch = touchEvent.changedTouches[i];\n      if (touch.identifier === touchId.current) {\n        return {\n          x: touch.clientX,\n          y: touch.clientY\n        };\n      }\n    }\n    return false;\n  }\n\n  // The event is MouseEvent\n  return {\n    x: event.clientX,\n    y: event.clientY\n  };\n}\nexport function valueToPercent(value, min, max) {\n  return (value - min) * 100 / (max - min);\n}\nfunction percentToValue(percent, min, max) {\n  return (max - min) * percent + min;\n}\nfunction getDecimalPrecision(num) {\n  // This handles the case when num is very small (0.00000001), js will turn this into 1e-8.\n  // When num is bigger than 1 or less than -1 it won't get converted to this notation so it's fine.\n  if (Math.abs(num) < 1) {\n    const parts = num.toExponential().split('e-');\n    const matissaDecimalPart = parts[0].split('.')[1];\n    return (matissaDecimalPart ? matissaDecimalPart.length : 0) + parseInt(parts[1], 10);\n  }\n  const decimalPart = num.toString().split('.')[1];\n  return decimalPart ? decimalPart.length : 0;\n}\nfunction roundValueToStep(value, step, min) {\n  const nearest = Math.round((value - min) / step) * step + min;\n  return Number(nearest.toFixed(getDecimalPrecision(step)));\n}\nfunction setValueIndex({\n  values,\n  newValue,\n  index\n}) {\n  const output = values.slice();\n  output[index] = newValue;\n  return output.sort(asc);\n}\nfunction focusThumb({\n  sliderRef,\n  activeIndex,\n  setActive\n}) {\n  var _sliderRef$current, _doc$activeElement;\n  const doc = ownerDocument(sliderRef.current);\n  if (!((_sliderRef$current = sliderRef.current) != null && _sliderRef$current.contains(doc.activeElement)) || Number(doc == null || (_doc$activeElement = doc.activeElement) == null ? void 0 : _doc$activeElement.getAttribute('data-index')) !== activeIndex) {\n    var _sliderRef$current2;\n    (_sliderRef$current2 = sliderRef.current) == null || _sliderRef$current2.querySelector(`[type=\"range\"][data-index=\"${activeIndex}\"]`).focus();\n  }\n  if (setActive) {\n    setActive(activeIndex);\n  }\n}\nfunction areValuesEqual(newValue, oldValue) {\n  if (typeof newValue === 'number' && typeof oldValue === 'number') {\n    return newValue === oldValue;\n  }\n  if (typeof newValue === 'object' && typeof oldValue === 'object') {\n    return areArraysEqual(newValue, oldValue);\n  }\n  return false;\n}\nconst axisProps = {\n  horizontal: {\n    offset: percent => ({\n      left: `${percent}%`\n    }),\n    leap: percent => ({\n      width: `${percent}%`\n    })\n  },\n  'horizontal-reverse': {\n    offset: percent => ({\n      right: `${percent}%`\n    }),\n    leap: percent => ({\n      width: `${percent}%`\n    })\n  },\n  vertical: {\n    offset: percent => ({\n      bottom: `${percent}%`\n    }),\n    leap: percent => ({\n      height: `${percent}%`\n    })\n  }\n};\nexport const Identity = x => x;\n\n// TODO: remove support for Safari < 13.\n// https://caniuse.com/#search=touch-action\n//\n// Safari, on iOS, supports touch action since v13.\n// Over 80% of the iOS phones are compatible\n// in August 2020.\n// Utilizing the CSS.supports method to check if touch-action is supported.\n// Since CSS.supports is supported on all but Edge@12 and IE and touch-action\n// is supported on both Edge@12 and IE if CSS.supports is not available that means that\n// touch-action will be supported\nlet cachedSupportsTouchActionNone;\nfunction doesSupportTouchActionNone() {\n  if (cachedSupportsTouchActionNone === undefined) {\n    if (typeof CSS !== 'undefined' && typeof CSS.supports === 'function') {\n      cachedSupportsTouchActionNone = CSS.supports('touch-action', 'none');\n    } else {\n      cachedSupportsTouchActionNone = true;\n    }\n  }\n  return cachedSupportsTouchActionNone;\n}\n/**\n *\n * Demos:\n *\n * - [Slider](https://mui.com/base-ui/react-slider/#hook)\n *\n * API:\n *\n * - [useSlider API](https://mui.com/base-ui/react-slider/hooks-api/#use-slider)\n */\nexport function useSlider(parameters) {\n  const {\n    'aria-labelledby': ariaLabelledby,\n    defaultValue,\n    disabled = false,\n    disableSwap = false,\n    isRtl = false,\n    marks: marksProp = false,\n    max = 100,\n    min = 0,\n    name,\n    onChange,\n    onChangeCommitted,\n    orientation = 'horizontal',\n    rootRef: ref,\n    scale = Identity,\n    step = 1,\n    shiftStep = 10,\n    tabIndex,\n    value: valueProp\n  } = parameters;\n  const touchId = React.useRef(undefined);\n  // We can't use the :active browser pseudo-classes.\n  // - The active state isn't triggered when clicking on the rail.\n  // - The active state isn't transferred when inversing a range slider.\n  const [active, setActive] = React.useState(-1);\n  const [open, setOpen] = React.useState(-1);\n  const [dragging, setDragging] = React.useState(false);\n  const moveCount = React.useRef(0);\n  const [valueDerived, setValueState] = useControlled({\n    controlled: valueProp,\n    default: defaultValue != null ? defaultValue : min,\n    name: 'Slider'\n  });\n  const handleChange = onChange && ((event, value, thumbIndex) => {\n    // Redefine target to allow name and value to be read.\n    // This allows seamless integration with the most popular form libraries.\n    // https://github.com/mui/material-ui/issues/13485#issuecomment-676048492\n    // Clone the event to not override `target` of the original event.\n    const nativeEvent = event.nativeEvent || event;\n    // @ts-ignore The nativeEvent is function, not object\n    const clonedEvent = new nativeEvent.constructor(nativeEvent.type, nativeEvent);\n    Object.defineProperty(clonedEvent, 'target', {\n      writable: true,\n      value: {\n        value,\n        name\n      }\n    });\n    onChange(clonedEvent, value, thumbIndex);\n  });\n  const range = Array.isArray(valueDerived);\n  let values = range ? valueDerived.slice().sort(asc) : [valueDerived];\n  values = values.map(value => value == null ? min : clamp(value, min, max));\n  const marks = marksProp === true && step !== null ? [...Array(Math.floor((max - min) / step) + 1)].map((_, index) => ({\n    value: min + step * index\n  })) : marksProp || [];\n  const marksValues = marks.map(mark => mark.value);\n  const {\n    isFocusVisibleRef,\n    onBlur: handleBlurVisible,\n    onFocus: handleFocusVisible,\n    ref: focusVisibleRef\n  } = useIsFocusVisible();\n  const [focusedThumbIndex, setFocusedThumbIndex] = React.useState(-1);\n  const sliderRef = React.useRef(null);\n  const handleFocusRef = useForkRef(focusVisibleRef, sliderRef);\n  const handleRef = useForkRef(ref, handleFocusRef);\n  const createHandleHiddenInputFocus = otherHandlers => event => {\n    var _otherHandlers$onFocu;\n    const index = Number(event.currentTarget.getAttribute('data-index'));\n    handleFocusVisible(event);\n    if (isFocusVisibleRef.current === true) {\n      setFocusedThumbIndex(index);\n    }\n    setOpen(index);\n    otherHandlers == null || (_otherHandlers$onFocu = otherHandlers.onFocus) == null || _otherHandlers$onFocu.call(otherHandlers, event);\n  };\n  const createHandleHiddenInputBlur = otherHandlers => event => {\n    var _otherHandlers$onBlur;\n    handleBlurVisible(event);\n    if (isFocusVisibleRef.current === false) {\n      setFocusedThumbIndex(-1);\n    }\n    setOpen(-1);\n    otherHandlers == null || (_otherHandlers$onBlur = otherHandlers.onBlur) == null || _otherHandlers$onBlur.call(otherHandlers, event);\n  };\n  const changeValue = (event, valueInput) => {\n    const index = Number(event.currentTarget.getAttribute('data-index'));\n    const value = values[index];\n    const marksIndex = marksValues.indexOf(value);\n    let newValue = valueInput;\n    if (marks && step == null) {\n      const maxMarksValue = marksValues[marksValues.length - 1];\n      if (newValue > maxMarksValue) {\n        newValue = maxMarksValue;\n      } else if (newValue < marksValues[0]) {\n        newValue = marksValues[0];\n      } else {\n        newValue = newValue < value ? marksValues[marksIndex - 1] : marksValues[marksIndex + 1];\n      }\n    }\n    newValue = clamp(newValue, min, max);\n    if (range) {\n      // Bound the new value to the thumb's neighbours.\n      if (disableSwap) {\n        newValue = clamp(newValue, values[index - 1] || -Infinity, values[index + 1] || Infinity);\n      }\n      const previousValue = newValue;\n      newValue = setValueIndex({\n        values,\n        newValue,\n        index\n      });\n      let activeIndex = index;\n\n      // Potentially swap the index if needed.\n      if (!disableSwap) {\n        activeIndex = newValue.indexOf(previousValue);\n      }\n      focusThumb({\n        sliderRef,\n        activeIndex\n      });\n    }\n    setValueState(newValue);\n    setFocusedThumbIndex(index);\n    if (handleChange && !areValuesEqual(newValue, valueDerived)) {\n      handleChange(event, newValue, index);\n    }\n    if (onChangeCommitted) {\n      onChangeCommitted(event, newValue);\n    }\n  };\n  const createHandleHiddenInputKeyDown = otherHandlers => event => {\n    var _otherHandlers$onKeyD;\n    // The Shift + Up/Down keyboard shortcuts for moving the slider makes sense to be supported\n    // only if the step is defined. If the step is null, this means tha the marks are used for specifying the valid values.\n    if (step !== null) {\n      const index = Number(event.currentTarget.getAttribute('data-index'));\n      const value = values[index];\n      let newValue = null;\n      if ((event.key === 'ArrowLeft' || event.key === 'ArrowDown') && event.shiftKey || event.key === 'PageDown') {\n        newValue = Math.max(value - shiftStep, min);\n      } else if ((event.key === 'ArrowRight' || event.key === 'ArrowUp') && event.shiftKey || event.key === 'PageUp') {\n        newValue = Math.min(value + shiftStep, max);\n      }\n      if (newValue !== null) {\n        changeValue(event, newValue);\n        event.preventDefault();\n      }\n    }\n    otherHandlers == null || (_otherHandlers$onKeyD = otherHandlers.onKeyDown) == null || _otherHandlers$onKeyD.call(otherHandlers, event);\n  };\n  useEnhancedEffect(() => {\n    if (disabled && sliderRef.current.contains(document.activeElement)) {\n      var _document$activeEleme;\n      // This is necessary because Firefox and Safari will keep focus\n      // on a disabled element:\n      // https://codesandbox.io/p/sandbox/mui-pr-22247-forked-h151h?file=/src/App.js\n      // @ts-ignore\n      (_document$activeEleme = document.activeElement) == null || _document$activeEleme.blur();\n    }\n  }, [disabled]);\n  if (disabled && active !== -1) {\n    setActive(-1);\n  }\n  if (disabled && focusedThumbIndex !== -1) {\n    setFocusedThumbIndex(-1);\n  }\n  const createHandleHiddenInputChange = otherHandlers => event => {\n    var _otherHandlers$onChan;\n    (_otherHandlers$onChan = otherHandlers.onChange) == null || _otherHandlers$onChan.call(otherHandlers, event);\n    // @ts-ignore\n    changeValue(event, event.target.valueAsNumber);\n  };\n  const previousIndex = React.useRef(undefined);\n  let axis = orientation;\n  if (isRtl && orientation === 'horizontal') {\n    axis += '-reverse';\n  }\n  const getFingerNewValue = ({\n    finger,\n    move = false\n  }) => {\n    const {\n      current: slider\n    } = sliderRef;\n    const {\n      width,\n      height,\n      bottom,\n      left\n    } = slider.getBoundingClientRect();\n    let percent;\n    if (axis.indexOf('vertical') === 0) {\n      percent = (bottom - finger.y) / height;\n    } else {\n      percent = (finger.x - left) / width;\n    }\n    if (axis.indexOf('-reverse') !== -1) {\n      percent = 1 - percent;\n    }\n    let newValue;\n    newValue = percentToValue(percent, min, max);\n    if (step) {\n      newValue = roundValueToStep(newValue, step, min);\n    } else {\n      const closestIndex = findClosest(marksValues, newValue);\n      newValue = marksValues[closestIndex];\n    }\n    newValue = clamp(newValue, min, max);\n    let activeIndex = 0;\n    if (range) {\n      if (!move) {\n        activeIndex = findClosest(values, newValue);\n      } else {\n        activeIndex = previousIndex.current;\n      }\n\n      // Bound the new value to the thumb's neighbours.\n      if (disableSwap) {\n        newValue = clamp(newValue, values[activeIndex - 1] || -Infinity, values[activeIndex + 1] || Infinity);\n      }\n      const previousValue = newValue;\n      newValue = setValueIndex({\n        values,\n        newValue,\n        index: activeIndex\n      });\n\n      // Potentially swap the index if needed.\n      if (!(disableSwap && move)) {\n        activeIndex = newValue.indexOf(previousValue);\n        previousIndex.current = activeIndex;\n      }\n    }\n    return {\n      newValue,\n      activeIndex\n    };\n  };\n  const handleTouchMove = useEventCallback(nativeEvent => {\n    const finger = trackFinger(nativeEvent, touchId);\n    if (!finger) {\n      return;\n    }\n    moveCount.current += 1;\n\n    // Cancel move in case some other element consumed a mouseup event and it was not fired.\n    // @ts-ignore buttons doesn't not exists on touch event\n    if (nativeEvent.type === 'mousemove' && nativeEvent.buttons === 0) {\n      // eslint-disable-next-line @typescript-eslint/no-use-before-define\n      handleTouchEnd(nativeEvent);\n      return;\n    }\n    const {\n      newValue,\n      activeIndex\n    } = getFingerNewValue({\n      finger,\n      move: true\n    });\n    focusThumb({\n      sliderRef,\n      activeIndex,\n      setActive\n    });\n    setValueState(newValue);\n    if (!dragging && moveCount.current > INTENTIONAL_DRAG_COUNT_THRESHOLD) {\n      setDragging(true);\n    }\n    if (handleChange && !areValuesEqual(newValue, valueDerived)) {\n      handleChange(nativeEvent, newValue, activeIndex);\n    }\n  });\n  const handleTouchEnd = useEventCallback(nativeEvent => {\n    const finger = trackFinger(nativeEvent, touchId);\n    setDragging(false);\n    if (!finger) {\n      return;\n    }\n    const {\n      newValue\n    } = getFingerNewValue({\n      finger,\n      move: true\n    });\n    setActive(-1);\n    if (nativeEvent.type === 'touchend') {\n      setOpen(-1);\n    }\n    if (onChangeCommitted) {\n      onChangeCommitted(nativeEvent, newValue);\n    }\n    touchId.current = undefined;\n\n    // eslint-disable-next-line @typescript-eslint/no-use-before-define\n    stopListening();\n  });\n  const handleTouchStart = useEventCallback(nativeEvent => {\n    if (disabled) {\n      return;\n    }\n    // If touch-action: none; is not supported we need to prevent the scroll manually.\n    if (!doesSupportTouchActionNone()) {\n      nativeEvent.preventDefault();\n    }\n    const touch = nativeEvent.changedTouches[0];\n    if (touch != null) {\n      // A number that uniquely identifies the current finger in the touch session.\n      touchId.current = touch.identifier;\n    }\n    const finger = trackFinger(nativeEvent, touchId);\n    if (finger !== false) {\n      const {\n        newValue,\n        activeIndex\n      } = getFingerNewValue({\n        finger\n      });\n      focusThumb({\n        sliderRef,\n        activeIndex,\n        setActive\n      });\n      setValueState(newValue);\n      if (handleChange && !areValuesEqual(newValue, valueDerived)) {\n        handleChange(nativeEvent, newValue, activeIndex);\n      }\n    }\n    moveCount.current = 0;\n    const doc = ownerDocument(sliderRef.current);\n    doc.addEventListener('touchmove', handleTouchMove, {\n      passive: true\n    });\n    doc.addEventListener('touchend', handleTouchEnd, {\n      passive: true\n    });\n  });\n  const stopListening = React.useCallback(() => {\n    const doc = ownerDocument(sliderRef.current);\n    doc.removeEventListener('mousemove', handleTouchMove);\n    doc.removeEventListener('mouseup', handleTouchEnd);\n    doc.removeEventListener('touchmove', handleTouchMove);\n    doc.removeEventListener('touchend', handleTouchEnd);\n  }, [handleTouchEnd, handleTouchMove]);\n  React.useEffect(() => {\n    const {\n      current: slider\n    } = sliderRef;\n    slider.addEventListener('touchstart', handleTouchStart, {\n      passive: doesSupportTouchActionNone()\n    });\n    return () => {\n      slider.removeEventListener('touchstart', handleTouchStart);\n      stopListening();\n    };\n  }, [stopListening, handleTouchStart]);\n  React.useEffect(() => {\n    if (disabled) {\n      stopListening();\n    }\n  }, [disabled, stopListening]);\n  const createHandleMouseDown = otherHandlers => event => {\n    var _otherHandlers$onMous;\n    (_otherHandlers$onMous = otherHandlers.onMouseDown) == null || _otherHandlers$onMous.call(otherHandlers, event);\n    if (disabled) {\n      return;\n    }\n    if (event.defaultPrevented) {\n      return;\n    }\n\n    // Only handle left clicks\n    if (event.button !== 0) {\n      return;\n    }\n\n    // Avoid text selection\n    event.preventDefault();\n    const finger = trackFinger(event, touchId);\n    if (finger !== false) {\n      const {\n        newValue,\n        activeIndex\n      } = getFingerNewValue({\n        finger\n      });\n      focusThumb({\n        sliderRef,\n        activeIndex,\n        setActive\n      });\n      setValueState(newValue);\n      if (handleChange && !areValuesEqual(newValue, valueDerived)) {\n        handleChange(event, newValue, activeIndex);\n      }\n    }\n    moveCount.current = 0;\n    const doc = ownerDocument(sliderRef.current);\n    doc.addEventListener('mousemove', handleTouchMove, {\n      passive: true\n    });\n    doc.addEventListener('mouseup', handleTouchEnd);\n  };\n  const trackOffset = valueToPercent(range ? values[0] : min, min, max);\n  const trackLeap = valueToPercent(values[values.length - 1], min, max) - trackOffset;\n  const getRootProps = (externalProps = {}) => {\n    const externalHandlers = extractEventHandlers(externalProps);\n    const ownEventHandlers = {\n      onMouseDown: createHandleMouseDown(externalHandlers || {})\n    };\n    const mergedEventHandlers = _extends({}, externalHandlers, ownEventHandlers);\n    return _extends({}, externalProps, {\n      ref: handleRef\n    }, mergedEventHandlers);\n  };\n  const createHandleMouseOver = otherHandlers => event => {\n    var _otherHandlers$onMous2;\n    (_otherHandlers$onMous2 = otherHandlers.onMouseOver) == null || _otherHandlers$onMous2.call(otherHandlers, event);\n    const index = Number(event.currentTarget.getAttribute('data-index'));\n    setOpen(index);\n  };\n  const createHandleMouseLeave = otherHandlers => event => {\n    var _otherHandlers$onMous3;\n    (_otherHandlers$onMous3 = otherHandlers.onMouseLeave) == null || _otherHandlers$onMous3.call(otherHandlers, event);\n    setOpen(-1);\n  };\n  const getThumbProps = (externalProps = {}) => {\n    const externalHandlers = extractEventHandlers(externalProps);\n    const ownEventHandlers = {\n      onMouseOver: createHandleMouseOver(externalHandlers || {}),\n      onMouseLeave: createHandleMouseLeave(externalHandlers || {})\n    };\n    return _extends({}, externalProps, externalHandlers, ownEventHandlers);\n  };\n  const getThumbStyle = index => {\n    return {\n      // So the non active thumb doesn't show its label on hover.\n      pointerEvents: active !== -1 && active !== index ? 'none' : undefined\n    };\n  };\n  const getHiddenInputProps = (externalProps = {}) => {\n    var _parameters$step;\n    const externalHandlers = extractEventHandlers(externalProps);\n    const ownEventHandlers = {\n      onChange: createHandleHiddenInputChange(externalHandlers || {}),\n      onFocus: createHandleHiddenInputFocus(externalHandlers || {}),\n      onBlur: createHandleHiddenInputBlur(externalHandlers || {}),\n      onKeyDown: createHandleHiddenInputKeyDown(externalHandlers || {})\n    };\n    const mergedEventHandlers = _extends({}, externalHandlers, ownEventHandlers);\n    return _extends({\n      tabIndex,\n      'aria-labelledby': ariaLabelledby,\n      'aria-orientation': orientation,\n      'aria-valuemax': scale(max),\n      'aria-valuemin': scale(min),\n      name,\n      type: 'range',\n      min: parameters.min,\n      max: parameters.max,\n      step: parameters.step === null && parameters.marks ? 'any' : (_parameters$step = parameters.step) != null ? _parameters$step : undefined,\n      disabled\n    }, externalProps, mergedEventHandlers, {\n      style: _extends({}, visuallyHidden, {\n        direction: isRtl ? 'rtl' : 'ltr',\n        // So that VoiceOver's focus indicator matches the thumb's dimensions\n        width: '100%',\n        height: '100%'\n      })\n    });\n  };\n  return {\n    active,\n    axis: axis,\n    axisProps,\n    dragging,\n    focusedThumbIndex,\n    getHiddenInputProps,\n    getRootProps,\n    getThumbProps,\n    marks: marks,\n    open,\n    range,\n    rootRef: handleRef,\n    trackLeap,\n    trackOffset,\n    values,\n    getThumbStyle\n  };\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,sBAAsB,IAAIC,aAAa,EAAEC,sBAAsB,IAAIC,aAAa,EAAEC,0BAA0B,IAAIC,iBAAiB,EAAEC,yBAAyB,IAAIC,gBAAgB,EAAEC,mBAAmB,IAAIC,UAAU,EAAEC,0BAA0B,IAAIC,iBAAiB,EAAEC,cAAc,EAAEC,KAAK,QAAQ,YAAY;AACxT,SAASC,cAAc,EAAEC,oBAAoB,QAAQ,UAAU;AAC/D,MAAMC,gCAAgC,GAAG,CAAC;AAC1C,SAASC,GAAGA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACjB,OAAOD,CAAC,GAAGC,CAAC;AACd;AACA,SAASC,WAAWA,CAACC,MAAM,EAAEC,YAAY,EAAE;EACzC,IAAIC,cAAc;EAClB,MAAM;IACJC,KAAK,EAAEC;EACT,CAAC,GAAG,CAACF,cAAc,GAAGF,MAAM,CAACK,MAAM,CAAC,CAACC,GAAG,EAAEC,KAAK,EAAEJ,KAAK,KAAK;IACzD,MAAMK,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAACT,YAAY,GAAGM,KAAK,CAAC;IAC/C,IAAID,GAAG,KAAK,IAAI,IAAIE,QAAQ,GAAGF,GAAG,CAACE,QAAQ,IAAIA,QAAQ,KAAKF,GAAG,CAACE,QAAQ,EAAE;MACxE,OAAO;QACLA,QAAQ;QACRL;MACF,CAAC;IACH;IACA,OAAOG,GAAG;EACZ,CAAC,EAAE,IAAI,CAAC,KAAK,IAAI,GAAGJ,cAAc,GAAG,CAAC,CAAC;EACvC,OAAOE,YAAY;AACrB;AACA,SAASO,WAAWA,CAACC,KAAK,EAAEC,OAAO,EAAE;EACnC;EACA,IAAIA,OAAO,CAACC,OAAO,KAAKC,SAAS,IAAIH,KAAK,CAACI,cAAc,EAAE;IACzD,MAAMC,UAAU,GAAGL,KAAK;IACxB,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,UAAU,CAACD,cAAc,CAACG,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;MAC5D,MAAME,KAAK,GAAGH,UAAU,CAACD,cAAc,CAACE,CAAC,CAAC;MAC1C,IAAIE,KAAK,CAACC,UAAU,KAAKR,OAAO,CAACC,OAAO,EAAE;QACxC,OAAO;UACLQ,CAAC,EAAEF,KAAK,CAACG,OAAO;UAChBC,CAAC,EAAEJ,KAAK,CAACK;QACX,CAAC;MACH;IACF;IACA,OAAO,KAAK;EACd;;EAEA;EACA,OAAO;IACLH,CAAC,EAAEV,KAAK,CAACW,OAAO;IAChBC,CAAC,EAAEZ,KAAK,CAACa;EACX,CAAC;AACH;AACA,OAAO,SAASC,cAAcA,CAACnB,KAAK,EAAEoB,GAAG,EAAEC,GAAG,EAAE;EAC9C,OAAO,CAACrB,KAAK,GAAGoB,GAAG,IAAI,GAAG,IAAIC,GAAG,GAAGD,GAAG,CAAC;AAC1C;AACA,SAASE,cAAcA,CAACC,OAAO,EAAEH,GAAG,EAAEC,GAAG,EAAE;EACzC,OAAO,CAACA,GAAG,GAAGD,GAAG,IAAIG,OAAO,GAAGH,GAAG;AACpC;AACA,SAASI,mBAAmBA,CAACC,GAAG,EAAE;EAChC;EACA;EACA,IAAIvB,IAAI,CAACC,GAAG,CAACsB,GAAG,CAAC,GAAG,CAAC,EAAE;IACrB,MAAMC,KAAK,GAAGD,GAAG,CAACE,aAAa,CAAC,CAAC,CAACC,KAAK,CAAC,IAAI,CAAC;IAC7C,MAAMC,kBAAkB,GAAGH,KAAK,CAAC,CAAC,CAAC,CAACE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACjD,OAAO,CAACC,kBAAkB,GAAGA,kBAAkB,CAACjB,MAAM,GAAG,CAAC,IAAIkB,QAAQ,CAACJ,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;EACtF;EACA,MAAMK,WAAW,GAAGN,GAAG,CAACO,QAAQ,CAAC,CAAC,CAACJ,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EAChD,OAAOG,WAAW,GAAGA,WAAW,CAACnB,MAAM,GAAG,CAAC;AAC7C;AACA,SAASqB,gBAAgBA,CAACjC,KAAK,EAAEkC,IAAI,EAAEd,GAAG,EAAE;EAC1C,MAAMe,OAAO,GAAGjC,IAAI,CAACkC,KAAK,CAAC,CAACpC,KAAK,GAAGoB,GAAG,IAAIc,IAAI,CAAC,GAAGA,IAAI,GAAGd,GAAG;EAC7D,OAAOiB,MAAM,CAACF,OAAO,CAACG,OAAO,CAACd,mBAAmB,CAACU,IAAI,CAAC,CAAC,CAAC;AAC3D;AACA,SAASK,aAAaA,CAAAC,IAAA,EAInB;EAAA,IAJoB;IACrB/C,MAAM;IACNgD,QAAQ;IACR7C;EACF,CAAC,GAAA4C,IAAA;EACC,MAAME,MAAM,GAAGjD,MAAM,CAACkD,KAAK,CAAC,CAAC;EAC7BD,MAAM,CAAC9C,KAAK,CAAC,GAAG6C,QAAQ;EACxB,OAAOC,MAAM,CAACE,IAAI,CAACvD,GAAG,CAAC;AACzB;AACA,SAASwD,UAAUA,CAAAC,KAAA,EAIhB;EAAA,IAJiB;IAClBC,SAAS;IACTC,WAAW;IACXC;EACF,CAAC,GAAAH,KAAA;EACC,IAAII,kBAAkB,EAAEC,kBAAkB;EAC1C,MAAMC,GAAG,GAAG/E,aAAa,CAAC0E,SAAS,CAACxC,OAAO,CAAC;EAC5C,IAAI,EAAE,CAAC2C,kBAAkB,GAAGH,SAAS,CAACxC,OAAO,KAAK,IAAI,IAAI2C,kBAAkB,CAACG,QAAQ,CAACD,GAAG,CAACE,aAAa,CAAC,CAAC,IAAIjB,MAAM,CAACe,GAAG,IAAI,IAAI,IAAI,CAACD,kBAAkB,GAAGC,GAAG,CAACE,aAAa,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGH,kBAAkB,CAACI,YAAY,CAAC,YAAY,CAAC,CAAC,KAAKP,WAAW,EAAE;IAC7P,IAAIQ,mBAAmB;IACvB,CAACA,mBAAmB,GAAGT,SAAS,CAACxC,OAAO,KAAK,IAAI,IAAIiD,mBAAmB,CAACC,aAAa,kCAAAC,MAAA,CAA+BV,WAAW,QAAI,CAAC,CAACW,KAAK,CAAC,CAAC;EAC/I;EACA,IAAIV,SAAS,EAAE;IACbA,SAAS,CAACD,WAAW,CAAC;EACxB;AACF;AACA,SAASY,cAAcA,CAACnB,QAAQ,EAAEoB,QAAQ,EAAE;EAC1C,IAAI,OAAOpB,QAAQ,KAAK,QAAQ,IAAI,OAAOoB,QAAQ,KAAK,QAAQ,EAAE;IAChE,OAAOpB,QAAQ,KAAKoB,QAAQ;EAC9B;EACA,IAAI,OAAOpB,QAAQ,KAAK,QAAQ,IAAI,OAAOoB,QAAQ,KAAK,QAAQ,EAAE;IAChE,OAAO3E,cAAc,CAACuD,QAAQ,EAAEoB,QAAQ,CAAC;EAC3C;EACA,OAAO,KAAK;AACd;AACA,MAAMC,SAAS,GAAG;EAChBC,UAAU,EAAE;IACVC,MAAM,EAAEzC,OAAO,KAAK;MAClB0C,IAAI,KAAAP,MAAA,CAAKnC,OAAO;IAClB,CAAC,CAAC;IACF2C,IAAI,EAAE3C,OAAO,KAAK;MAChB4C,KAAK,KAAAT,MAAA,CAAKnC,OAAO;IACnB,CAAC;EACH,CAAC;EACD,oBAAoB,EAAE;IACpByC,MAAM,EAAEzC,OAAO,KAAK;MAClB6C,KAAK,KAAAV,MAAA,CAAKnC,OAAO;IACnB,CAAC,CAAC;IACF2C,IAAI,EAAE3C,OAAO,KAAK;MAChB4C,KAAK,KAAAT,MAAA,CAAKnC,OAAO;IACnB,CAAC;EACH,CAAC;EACD8C,QAAQ,EAAE;IACRL,MAAM,EAAEzC,OAAO,KAAK;MAClB+C,MAAM,KAAAZ,MAAA,CAAKnC,OAAO;IACpB,CAAC,CAAC;IACF2C,IAAI,EAAE3C,OAAO,KAAK;MAChBgD,MAAM,KAAAb,MAAA,CAAKnC,OAAO;IACpB,CAAC;EACH;AACF,CAAC;AACD,OAAO,MAAMiD,QAAQ,GAAGzD,CAAC,IAAIA,CAAC;;AAE9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI0D,6BAA6B;AACjC,SAASC,0BAA0BA,CAAA,EAAG;EACpC,IAAID,6BAA6B,KAAKjE,SAAS,EAAE;IAC/C,IAAI,OAAOmE,GAAG,KAAK,WAAW,IAAI,OAAOA,GAAG,CAACC,QAAQ,KAAK,UAAU,EAAE;MACpEH,6BAA6B,GAAGE,GAAG,CAACC,QAAQ,CAAC,cAAc,EAAE,MAAM,CAAC;IACtE,CAAC,MAAM;MACLH,6BAA6B,GAAG,IAAI;IACtC;EACF;EACA,OAAOA,6BAA6B;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASI,SAASA,CAACC,UAAU,EAAE;EACpC,MAAM;IACJ,iBAAiB,EAAEC,cAAc;IACjCC,YAAY;IACZC,QAAQ,GAAG,KAAK;IAChBC,WAAW,GAAG,KAAK;IACnBC,KAAK,GAAG,KAAK;IACbC,KAAK,EAAEC,SAAS,GAAG,KAAK;IACxBhE,GAAG,GAAG,GAAG;IACTD,GAAG,GAAG,CAAC;IACPkE,IAAI;IACJC,QAAQ;IACRC,iBAAiB;IACjBC,WAAW,GAAG,YAAY;IAC1BC,OAAO,EAAEC,GAAG;IACZC,KAAK,GAAGpB,QAAQ;IAChBtC,IAAI,GAAG,CAAC;IACR2D,SAAS,GAAG,EAAE;IACdC,QAAQ;IACR9F,KAAK,EAAE+F;EACT,CAAC,GAAGjB,UAAU;EACd,MAAMxE,OAAO,GAAGnC,KAAK,CAAC6H,MAAM,CAACxF,SAAS,CAAC;EACvC;EACA;EACA;EACA,MAAM,CAACyF,MAAM,EAAEhD,SAAS,CAAC,GAAG9E,KAAK,CAAC+H,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC9C,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGjI,KAAK,CAAC+H,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1C,MAAM,CAACG,QAAQ,EAAEC,WAAW,CAAC,GAAGnI,KAAK,CAAC+H,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAMK,SAAS,GAAGpI,KAAK,CAAC6H,MAAM,CAAC,CAAC,CAAC;EACjC,MAAM,CAACQ,YAAY,EAAEC,aAAa,CAAC,GAAGlI,aAAa,CAAC;IAClDmI,UAAU,EAAEX,SAAS;IACrBY,OAAO,EAAE3B,YAAY,IAAI,IAAI,GAAGA,YAAY,GAAG5D,GAAG;IAClDkE,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAMsB,YAAY,GAAGrB,QAAQ,KAAK,CAAClF,KAAK,EAAEL,KAAK,EAAE6G,UAAU,KAAK;IAC9D;IACA;IACA;IACA;IACA,MAAMC,WAAW,GAAGzG,KAAK,CAACyG,WAAW,IAAIzG,KAAK;IAC9C;IACA,MAAM0G,WAAW,GAAG,IAAID,WAAW,CAACE,WAAW,CAACF,WAAW,CAACG,IAAI,EAAEH,WAAW,CAAC;IAC9EI,MAAM,CAACC,cAAc,CAACJ,WAAW,EAAE,QAAQ,EAAE;MAC3CK,QAAQ,EAAE,IAAI;MACdpH,KAAK,EAAE;QACLA,KAAK;QACLsF;MACF;IACF,CAAC,CAAC;IACFC,QAAQ,CAACwB,WAAW,EAAE/G,KAAK,EAAE6G,UAAU,CAAC;EAC1C,CAAC,CAAC;EACF,MAAMQ,KAAK,GAAGC,KAAK,CAACC,OAAO,CAACf,YAAY,CAAC;EACzC,IAAI/G,MAAM,GAAG4H,KAAK,GAAGb,YAAY,CAAC7D,KAAK,CAAC,CAAC,CAACC,IAAI,CAACvD,GAAG,CAAC,GAAG,CAACmH,YAAY,CAAC;EACpE/G,MAAM,GAAGA,MAAM,CAAC+H,GAAG,CAACxH,KAAK,IAAIA,KAAK,IAAI,IAAI,GAAGoB,GAAG,GAAGnC,KAAK,CAACe,KAAK,EAAEoB,GAAG,EAAEC,GAAG,CAAC,CAAC;EAC1E,MAAM+D,KAAK,GAAGC,SAAS,KAAK,IAAI,IAAInD,IAAI,KAAK,IAAI,GAAG,CAAC,GAAGoF,KAAK,CAACpH,IAAI,CAACuH,KAAK,CAAC,CAACpG,GAAG,GAAGD,GAAG,IAAIc,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAACsF,GAAG,CAAC,CAACE,CAAC,EAAE9H,KAAK,MAAM;IACpHI,KAAK,EAAEoB,GAAG,GAAGc,IAAI,GAAGtC;EACtB,CAAC,CAAC,CAAC,GAAGyF,SAAS,IAAI,EAAE;EACrB,MAAMsC,WAAW,GAAGvC,KAAK,CAACoC,GAAG,CAACI,IAAI,IAAIA,IAAI,CAAC5H,KAAK,CAAC;EACjD,MAAM;IACJ6H,iBAAiB;IACjBC,MAAM,EAAEC,iBAAiB;IACzBC,OAAO,EAAEC,kBAAkB;IAC3BtC,GAAG,EAAEuC;EACP,CAAC,GAAGnJ,iBAAiB,CAAC,CAAC;EACvB,MAAM,CAACoJ,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGjK,KAAK,CAAC+H,QAAQ,CAAC,CAAC,CAAC,CAAC;EACpE,MAAMnD,SAAS,GAAG5E,KAAK,CAAC6H,MAAM,CAAC,IAAI,CAAC;EACpC,MAAMqC,cAAc,GAAGxJ,UAAU,CAACqJ,eAAe,EAAEnF,SAAS,CAAC;EAC7D,MAAMuF,SAAS,GAAGzJ,UAAU,CAAC8G,GAAG,EAAE0C,cAAc,CAAC;EACjD,MAAME,4BAA4B,GAAGC,aAAa,IAAInI,KAAK,IAAI;IAC7D,IAAIoI,qBAAqB;IACzB,MAAM7I,KAAK,GAAGyC,MAAM,CAAChC,KAAK,CAACqI,aAAa,CAACnF,YAAY,CAAC,YAAY,CAAC,CAAC;IACpE0E,kBAAkB,CAAC5H,KAAK,CAAC;IACzB,IAAIwH,iBAAiB,CAACtH,OAAO,KAAK,IAAI,EAAE;MACtC6H,oBAAoB,CAACxI,KAAK,CAAC;IAC7B;IACAwG,OAAO,CAACxG,KAAK,CAAC;IACd4I,aAAa,IAAI,IAAI,IAAI,CAACC,qBAAqB,GAAGD,aAAa,CAACR,OAAO,KAAK,IAAI,IAAIS,qBAAqB,CAACE,IAAI,CAACH,aAAa,EAAEnI,KAAK,CAAC;EACtI,CAAC;EACD,MAAMuI,2BAA2B,GAAGJ,aAAa,IAAInI,KAAK,IAAI;IAC5D,IAAIwI,qBAAqB;IACzBd,iBAAiB,CAAC1H,KAAK,CAAC;IACxB,IAAIwH,iBAAiB,CAACtH,OAAO,KAAK,KAAK,EAAE;MACvC6H,oBAAoB,CAAC,CAAC,CAAC,CAAC;IAC1B;IACAhC,OAAO,CAAC,CAAC,CAAC,CAAC;IACXoC,aAAa,IAAI,IAAI,IAAI,CAACK,qBAAqB,GAAGL,aAAa,CAACV,MAAM,KAAK,IAAI,IAAIe,qBAAqB,CAACF,IAAI,CAACH,aAAa,EAAEnI,KAAK,CAAC;EACrI,CAAC;EACD,MAAMyI,WAAW,GAAGA,CAACzI,KAAK,EAAE0I,UAAU,KAAK;IACzC,MAAMnJ,KAAK,GAAGyC,MAAM,CAAChC,KAAK,CAACqI,aAAa,CAACnF,YAAY,CAAC,YAAY,CAAC,CAAC;IACpE,MAAMvD,KAAK,GAAGP,MAAM,CAACG,KAAK,CAAC;IAC3B,MAAMoJ,UAAU,GAAGrB,WAAW,CAACsB,OAAO,CAACjJ,KAAK,CAAC;IAC7C,IAAIyC,QAAQ,GAAGsG,UAAU;IACzB,IAAI3D,KAAK,IAAIlD,IAAI,IAAI,IAAI,EAAE;MACzB,MAAMgH,aAAa,GAAGvB,WAAW,CAACA,WAAW,CAAC/G,MAAM,GAAG,CAAC,CAAC;MACzD,IAAI6B,QAAQ,GAAGyG,aAAa,EAAE;QAC5BzG,QAAQ,GAAGyG,aAAa;MAC1B,CAAC,MAAM,IAAIzG,QAAQ,GAAGkF,WAAW,CAAC,CAAC,CAAC,EAAE;QACpClF,QAAQ,GAAGkF,WAAW,CAAC,CAAC,CAAC;MAC3B,CAAC,MAAM;QACLlF,QAAQ,GAAGA,QAAQ,GAAGzC,KAAK,GAAG2H,WAAW,CAACqB,UAAU,GAAG,CAAC,CAAC,GAAGrB,WAAW,CAACqB,UAAU,GAAG,CAAC,CAAC;MACzF;IACF;IACAvG,QAAQ,GAAGxD,KAAK,CAACwD,QAAQ,EAAErB,GAAG,EAAEC,GAAG,CAAC;IACpC,IAAIgG,KAAK,EAAE;MACT;MACA,IAAInC,WAAW,EAAE;QACfzC,QAAQ,GAAGxD,KAAK,CAACwD,QAAQ,EAAEhD,MAAM,CAACG,KAAK,GAAG,CAAC,CAAC,IAAI,CAACuJ,QAAQ,EAAE1J,MAAM,CAACG,KAAK,GAAG,CAAC,CAAC,IAAIuJ,QAAQ,CAAC;MAC3F;MACA,MAAMC,aAAa,GAAG3G,QAAQ;MAC9BA,QAAQ,GAAGF,aAAa,CAAC;QACvB9C,MAAM;QACNgD,QAAQ;QACR7C;MACF,CAAC,CAAC;MACF,IAAIoD,WAAW,GAAGpD,KAAK;;MAEvB;MACA,IAAI,CAACsF,WAAW,EAAE;QAChBlC,WAAW,GAAGP,QAAQ,CAACwG,OAAO,CAACG,aAAa,CAAC;MAC/C;MACAvG,UAAU,CAAC;QACTE,SAAS;QACTC;MACF,CAAC,CAAC;IACJ;IACAyD,aAAa,CAAChE,QAAQ,CAAC;IACvB2F,oBAAoB,CAACxI,KAAK,CAAC;IAC3B,IAAIgH,YAAY,IAAI,CAAChD,cAAc,CAACnB,QAAQ,EAAE+D,YAAY,CAAC,EAAE;MAC3DI,YAAY,CAACvG,KAAK,EAAEoC,QAAQ,EAAE7C,KAAK,CAAC;IACtC;IACA,IAAI4F,iBAAiB,EAAE;MACrBA,iBAAiB,CAACnF,KAAK,EAAEoC,QAAQ,CAAC;IACpC;EACF,CAAC;EACD,MAAM4G,8BAA8B,GAAGb,aAAa,IAAInI,KAAK,IAAI;IAC/D,IAAIiJ,qBAAqB;IACzB;IACA;IACA,IAAIpH,IAAI,KAAK,IAAI,EAAE;MACjB,MAAMtC,KAAK,GAAGyC,MAAM,CAAChC,KAAK,CAACqI,aAAa,CAACnF,YAAY,CAAC,YAAY,CAAC,CAAC;MACpE,MAAMvD,KAAK,GAAGP,MAAM,CAACG,KAAK,CAAC;MAC3B,IAAI6C,QAAQ,GAAG,IAAI;MACnB,IAAI,CAACpC,KAAK,CAACkJ,GAAG,KAAK,WAAW,IAAIlJ,KAAK,CAACkJ,GAAG,KAAK,WAAW,KAAKlJ,KAAK,CAACmJ,QAAQ,IAAInJ,KAAK,CAACkJ,GAAG,KAAK,UAAU,EAAE;QAC1G9G,QAAQ,GAAGvC,IAAI,CAACmB,GAAG,CAACrB,KAAK,GAAG6F,SAAS,EAAEzE,GAAG,CAAC;MAC7C,CAAC,MAAM,IAAI,CAACf,KAAK,CAACkJ,GAAG,KAAK,YAAY,IAAIlJ,KAAK,CAACkJ,GAAG,KAAK,SAAS,KAAKlJ,KAAK,CAACmJ,QAAQ,IAAInJ,KAAK,CAACkJ,GAAG,KAAK,QAAQ,EAAE;QAC9G9G,QAAQ,GAAGvC,IAAI,CAACkB,GAAG,CAACpB,KAAK,GAAG6F,SAAS,EAAExE,GAAG,CAAC;MAC7C;MACA,IAAIoB,QAAQ,KAAK,IAAI,EAAE;QACrBqG,WAAW,CAACzI,KAAK,EAAEoC,QAAQ,CAAC;QAC5BpC,KAAK,CAACoJ,cAAc,CAAC,CAAC;MACxB;IACF;IACAjB,aAAa,IAAI,IAAI,IAAI,CAACc,qBAAqB,GAAGd,aAAa,CAACkB,SAAS,KAAK,IAAI,IAAIJ,qBAAqB,CAACX,IAAI,CAACH,aAAa,EAAEnI,KAAK,CAAC;EACxI,CAAC;EACD5B,iBAAiB,CAAC,MAAM;IACtB,IAAIwG,QAAQ,IAAIlC,SAAS,CAACxC,OAAO,CAAC8C,QAAQ,CAACsG,QAAQ,CAACrG,aAAa,CAAC,EAAE;MAClE,IAAIsG,qBAAqB;MACzB;MACA;MACA;MACA;MACA,CAACA,qBAAqB,GAAGD,QAAQ,CAACrG,aAAa,KAAK,IAAI,IAAIsG,qBAAqB,CAACC,IAAI,CAAC,CAAC;IAC1F;EACF,CAAC,EAAE,CAAC5E,QAAQ,CAAC,CAAC;EACd,IAAIA,QAAQ,IAAIgB,MAAM,KAAK,CAAC,CAAC,EAAE;IAC7BhD,SAAS,CAAC,CAAC,CAAC,CAAC;EACf;EACA,IAAIgC,QAAQ,IAAIkD,iBAAiB,KAAK,CAAC,CAAC,EAAE;IACxCC,oBAAoB,CAAC,CAAC,CAAC,CAAC;EAC1B;EACA,MAAM0B,6BAA6B,GAAGtB,aAAa,IAAInI,KAAK,IAAI;IAC9D,IAAI0J,qBAAqB;IACzB,CAACA,qBAAqB,GAAGvB,aAAa,CAACjD,QAAQ,KAAK,IAAI,IAAIwE,qBAAqB,CAACpB,IAAI,CAACH,aAAa,EAAEnI,KAAK,CAAC;IAC5G;IACAyI,WAAW,CAACzI,KAAK,EAAEA,KAAK,CAAC2J,MAAM,CAACC,aAAa,CAAC;EAChD,CAAC;EACD,MAAMC,aAAa,GAAG/L,KAAK,CAAC6H,MAAM,CAACxF,SAAS,CAAC;EAC7C,IAAI2J,IAAI,GAAG1E,WAAW;EACtB,IAAIN,KAAK,IAAIM,WAAW,KAAK,YAAY,EAAE;IACzC0E,IAAI,IAAI,UAAU;EACpB;EACA,MAAMC,iBAAiB,GAAGC,KAAA,IAGpB;IAAA,IAHqB;MACzBC,MAAM;MACNC,IAAI,GAAG;IACT,CAAC,GAAAF,KAAA;IACC,MAAM;MACJ9J,OAAO,EAAEiK;IACX,CAAC,GAAGzH,SAAS;IACb,MAAM;MACJoB,KAAK;MACLI,MAAM;MACND,MAAM;MACNL;IACF,CAAC,GAAGuG,MAAM,CAACC,qBAAqB,CAAC,CAAC;IAClC,IAAIlJ,OAAO;IACX,IAAI4I,IAAI,CAAClB,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE;MAClC1H,OAAO,GAAG,CAAC+C,MAAM,GAAGgG,MAAM,CAACrJ,CAAC,IAAIsD,MAAM;IACxC,CAAC,MAAM;MACLhD,OAAO,GAAG,CAAC+I,MAAM,CAACvJ,CAAC,GAAGkD,IAAI,IAAIE,KAAK;IACrC;IACA,IAAIgG,IAAI,CAAClB,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE;MACnC1H,OAAO,GAAG,CAAC,GAAGA,OAAO;IACvB;IACA,IAAIkB,QAAQ;IACZA,QAAQ,GAAGnB,cAAc,CAACC,OAAO,EAAEH,GAAG,EAAEC,GAAG,CAAC;IAC5C,IAAIa,IAAI,EAAE;MACRO,QAAQ,GAAGR,gBAAgB,CAACQ,QAAQ,EAAEP,IAAI,EAAEd,GAAG,CAAC;IAClD,CAAC,MAAM;MACL,MAAMvB,YAAY,GAAGL,WAAW,CAACmI,WAAW,EAAElF,QAAQ,CAAC;MACvDA,QAAQ,GAAGkF,WAAW,CAAC9H,YAAY,CAAC;IACtC;IACA4C,QAAQ,GAAGxD,KAAK,CAACwD,QAAQ,EAAErB,GAAG,EAAEC,GAAG,CAAC;IACpC,IAAI2B,WAAW,GAAG,CAAC;IACnB,IAAIqE,KAAK,EAAE;MACT,IAAI,CAACkD,IAAI,EAAE;QACTvH,WAAW,GAAGxD,WAAW,CAACC,MAAM,EAAEgD,QAAQ,CAAC;MAC7C,CAAC,MAAM;QACLO,WAAW,GAAGkH,aAAa,CAAC3J,OAAO;MACrC;;MAEA;MACA,IAAI2E,WAAW,EAAE;QACfzC,QAAQ,GAAGxD,KAAK,CAACwD,QAAQ,EAAEhD,MAAM,CAACuD,WAAW,GAAG,CAAC,CAAC,IAAI,CAACmG,QAAQ,EAAE1J,MAAM,CAACuD,WAAW,GAAG,CAAC,CAAC,IAAImG,QAAQ,CAAC;MACvG;MACA,MAAMC,aAAa,GAAG3G,QAAQ;MAC9BA,QAAQ,GAAGF,aAAa,CAAC;QACvB9C,MAAM;QACNgD,QAAQ;QACR7C,KAAK,EAAEoD;MACT,CAAC,CAAC;;MAEF;MACA,IAAI,EAAEkC,WAAW,IAAIqF,IAAI,CAAC,EAAE;QAC1BvH,WAAW,GAAGP,QAAQ,CAACwG,OAAO,CAACG,aAAa,CAAC;QAC7Cc,aAAa,CAAC3J,OAAO,GAAGyC,WAAW;MACrC;IACF;IACA,OAAO;MACLP,QAAQ;MACRO;IACF,CAAC;EACH,CAAC;EACD,MAAM0H,eAAe,GAAG/L,gBAAgB,CAACmI,WAAW,IAAI;IACtD,MAAMwD,MAAM,GAAGlK,WAAW,CAAC0G,WAAW,EAAExG,OAAO,CAAC;IAChD,IAAI,CAACgK,MAAM,EAAE;MACX;IACF;IACA/D,SAAS,CAAChG,OAAO,IAAI,CAAC;;IAEtB;IACA;IACA,IAAIuG,WAAW,CAACG,IAAI,KAAK,WAAW,IAAIH,WAAW,CAAC6D,OAAO,KAAK,CAAC,EAAE;MACjE;MACAC,cAAc,CAAC9D,WAAW,CAAC;MAC3B;IACF;IACA,MAAM;MACJrE,QAAQ;MACRO;IACF,CAAC,GAAGoH,iBAAiB,CAAC;MACpBE,MAAM;MACNC,IAAI,EAAE;IACR,CAAC,CAAC;IACF1H,UAAU,CAAC;MACTE,SAAS;MACTC,WAAW;MACXC;IACF,CAAC,CAAC;IACFwD,aAAa,CAAChE,QAAQ,CAAC;IACvB,IAAI,CAAC4D,QAAQ,IAAIE,SAAS,CAAChG,OAAO,GAAGnB,gCAAgC,EAAE;MACrEkH,WAAW,CAAC,IAAI,CAAC;IACnB;IACA,IAAIM,YAAY,IAAI,CAAChD,cAAc,CAACnB,QAAQ,EAAE+D,YAAY,CAAC,EAAE;MAC3DI,YAAY,CAACE,WAAW,EAAErE,QAAQ,EAAEO,WAAW,CAAC;IAClD;EACF,CAAC,CAAC;EACF,MAAM4H,cAAc,GAAGjM,gBAAgB,CAACmI,WAAW,IAAI;IACrD,MAAMwD,MAAM,GAAGlK,WAAW,CAAC0G,WAAW,EAAExG,OAAO,CAAC;IAChDgG,WAAW,CAAC,KAAK,CAAC;IAClB,IAAI,CAACgE,MAAM,EAAE;MACX;IACF;IACA,MAAM;MACJ7H;IACF,CAAC,GAAG2H,iBAAiB,CAAC;MACpBE,MAAM;MACNC,IAAI,EAAE;IACR,CAAC,CAAC;IACFtH,SAAS,CAAC,CAAC,CAAC,CAAC;IACb,IAAI6D,WAAW,CAACG,IAAI,KAAK,UAAU,EAAE;MACnCb,OAAO,CAAC,CAAC,CAAC,CAAC;IACb;IACA,IAAIZ,iBAAiB,EAAE;MACrBA,iBAAiB,CAACsB,WAAW,EAAErE,QAAQ,CAAC;IAC1C;IACAnC,OAAO,CAACC,OAAO,GAAGC,SAAS;;IAE3B;IACAqK,aAAa,CAAC,CAAC;EACjB,CAAC,CAAC;EACF,MAAMC,gBAAgB,GAAGnM,gBAAgB,CAACmI,WAAW,IAAI;IACvD,IAAI7B,QAAQ,EAAE;MACZ;IACF;IACA;IACA,IAAI,CAACP,0BAA0B,CAAC,CAAC,EAAE;MACjCoC,WAAW,CAAC2C,cAAc,CAAC,CAAC;IAC9B;IACA,MAAM5I,KAAK,GAAGiG,WAAW,CAACrG,cAAc,CAAC,CAAC,CAAC;IAC3C,IAAII,KAAK,IAAI,IAAI,EAAE;MACjB;MACAP,OAAO,CAACC,OAAO,GAAGM,KAAK,CAACC,UAAU;IACpC;IACA,MAAMwJ,MAAM,GAAGlK,WAAW,CAAC0G,WAAW,EAAExG,OAAO,CAAC;IAChD,IAAIgK,MAAM,KAAK,KAAK,EAAE;MACpB,MAAM;QACJ7H,QAAQ;QACRO;MACF,CAAC,GAAGoH,iBAAiB,CAAC;QACpBE;MACF,CAAC,CAAC;MACFzH,UAAU,CAAC;QACTE,SAAS;QACTC,WAAW;QACXC;MACF,CAAC,CAAC;MACFwD,aAAa,CAAChE,QAAQ,CAAC;MACvB,IAAImE,YAAY,IAAI,CAAChD,cAAc,CAACnB,QAAQ,EAAE+D,YAAY,CAAC,EAAE;QAC3DI,YAAY,CAACE,WAAW,EAAErE,QAAQ,EAAEO,WAAW,CAAC;MAClD;IACF;IACAuD,SAAS,CAAChG,OAAO,GAAG,CAAC;IACrB,MAAM6C,GAAG,GAAG/E,aAAa,CAAC0E,SAAS,CAACxC,OAAO,CAAC;IAC5C6C,GAAG,CAAC2H,gBAAgB,CAAC,WAAW,EAAEL,eAAe,EAAE;MACjDM,OAAO,EAAE;IACX,CAAC,CAAC;IACF5H,GAAG,CAAC2H,gBAAgB,CAAC,UAAU,EAAEH,cAAc,EAAE;MAC/CI,OAAO,EAAE;IACX,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,MAAMH,aAAa,GAAG1M,KAAK,CAAC8M,WAAW,CAAC,MAAM;IAC5C,MAAM7H,GAAG,GAAG/E,aAAa,CAAC0E,SAAS,CAACxC,OAAO,CAAC;IAC5C6C,GAAG,CAAC8H,mBAAmB,CAAC,WAAW,EAAER,eAAe,CAAC;IACrDtH,GAAG,CAAC8H,mBAAmB,CAAC,SAAS,EAAEN,cAAc,CAAC;IAClDxH,GAAG,CAAC8H,mBAAmB,CAAC,WAAW,EAAER,eAAe,CAAC;IACrDtH,GAAG,CAAC8H,mBAAmB,CAAC,UAAU,EAAEN,cAAc,CAAC;EACrD,CAAC,EAAE,CAACA,cAAc,EAAEF,eAAe,CAAC,CAAC;EACrCvM,KAAK,CAACgN,SAAS,CAAC,MAAM;IACpB,MAAM;MACJ5K,OAAO,EAAEiK;IACX,CAAC,GAAGzH,SAAS;IACbyH,MAAM,CAACO,gBAAgB,CAAC,YAAY,EAAED,gBAAgB,EAAE;MACtDE,OAAO,EAAEtG,0BAA0B,CAAC;IACtC,CAAC,CAAC;IACF,OAAO,MAAM;MACX8F,MAAM,CAACU,mBAAmB,CAAC,YAAY,EAAEJ,gBAAgB,CAAC;MAC1DD,aAAa,CAAC,CAAC;IACjB,CAAC;EACH,CAAC,EAAE,CAACA,aAAa,EAAEC,gBAAgB,CAAC,CAAC;EACrC3M,KAAK,CAACgN,SAAS,CAAC,MAAM;IACpB,IAAIlG,QAAQ,EAAE;MACZ4F,aAAa,CAAC,CAAC;IACjB;EACF,CAAC,EAAE,CAAC5F,QAAQ,EAAE4F,aAAa,CAAC,CAAC;EAC7B,MAAMO,qBAAqB,GAAG5C,aAAa,IAAInI,KAAK,IAAI;IACtD,IAAIgL,qBAAqB;IACzB,CAACA,qBAAqB,GAAG7C,aAAa,CAAC8C,WAAW,KAAK,IAAI,IAAID,qBAAqB,CAAC1C,IAAI,CAACH,aAAa,EAAEnI,KAAK,CAAC;IAC/G,IAAI4E,QAAQ,EAAE;MACZ;IACF;IACA,IAAI5E,KAAK,CAACkL,gBAAgB,EAAE;MAC1B;IACF;;IAEA;IACA,IAAIlL,KAAK,CAACmL,MAAM,KAAK,CAAC,EAAE;MACtB;IACF;;IAEA;IACAnL,KAAK,CAACoJ,cAAc,CAAC,CAAC;IACtB,MAAMa,MAAM,GAAGlK,WAAW,CAACC,KAAK,EAAEC,OAAO,CAAC;IAC1C,IAAIgK,MAAM,KAAK,KAAK,EAAE;MACpB,MAAM;QACJ7H,QAAQ;QACRO;MACF,CAAC,GAAGoH,iBAAiB,CAAC;QACpBE;MACF,CAAC,CAAC;MACFzH,UAAU,CAAC;QACTE,SAAS;QACTC,WAAW;QACXC;MACF,CAAC,CAAC;MACFwD,aAAa,CAAChE,QAAQ,CAAC;MACvB,IAAImE,YAAY,IAAI,CAAChD,cAAc,CAACnB,QAAQ,EAAE+D,YAAY,CAAC,EAAE;QAC3DI,YAAY,CAACvG,KAAK,EAAEoC,QAAQ,EAAEO,WAAW,CAAC;MAC5C;IACF;IACAuD,SAAS,CAAChG,OAAO,GAAG,CAAC;IACrB,MAAM6C,GAAG,GAAG/E,aAAa,CAAC0E,SAAS,CAACxC,OAAO,CAAC;IAC5C6C,GAAG,CAAC2H,gBAAgB,CAAC,WAAW,EAAEL,eAAe,EAAE;MACjDM,OAAO,EAAE;IACX,CAAC,CAAC;IACF5H,GAAG,CAAC2H,gBAAgB,CAAC,SAAS,EAAEH,cAAc,CAAC;EACjD,CAAC;EACD,MAAMa,WAAW,GAAGtK,cAAc,CAACkG,KAAK,GAAG5H,MAAM,CAAC,CAAC,CAAC,GAAG2B,GAAG,EAAEA,GAAG,EAAEC,GAAG,CAAC;EACrE,MAAMqK,SAAS,GAAGvK,cAAc,CAAC1B,MAAM,CAACA,MAAM,CAACmB,MAAM,GAAG,CAAC,CAAC,EAAEQ,GAAG,EAAEC,GAAG,CAAC,GAAGoK,WAAW;EACnF,MAAME,YAAY,GAAG,SAAAA,CAAA,EAAwB;IAAA,IAAvBC,aAAa,GAAAC,SAAA,CAAAjL,MAAA,QAAAiL,SAAA,QAAArL,SAAA,GAAAqL,SAAA,MAAG,CAAC,CAAC;IACtC,MAAMC,gBAAgB,GAAG3M,oBAAoB,CAACyM,aAAa,CAAC;IAC5D,MAAMG,gBAAgB,GAAG;MACvBT,WAAW,EAAEF,qBAAqB,CAACU,gBAAgB,IAAI,CAAC,CAAC;IAC3D,CAAC;IACD,MAAME,mBAAmB,GAAG9N,QAAQ,CAAC,CAAC,CAAC,EAAE4N,gBAAgB,EAAEC,gBAAgB,CAAC;IAC5E,OAAO7N,QAAQ,CAAC,CAAC,CAAC,EAAE0N,aAAa,EAAE;MACjCjG,GAAG,EAAE2C;IACP,CAAC,EAAE0D,mBAAmB,CAAC;EACzB,CAAC;EACD,MAAMC,qBAAqB,GAAGzD,aAAa,IAAInI,KAAK,IAAI;IACtD,IAAI6L,sBAAsB;IAC1B,CAACA,sBAAsB,GAAG1D,aAAa,CAAC2D,WAAW,KAAK,IAAI,IAAID,sBAAsB,CAACvD,IAAI,CAACH,aAAa,EAAEnI,KAAK,CAAC;IACjH,MAAMT,KAAK,GAAGyC,MAAM,CAAChC,KAAK,CAACqI,aAAa,CAACnF,YAAY,CAAC,YAAY,CAAC,CAAC;IACpE6C,OAAO,CAACxG,KAAK,CAAC;EAChB,CAAC;EACD,MAAMwM,sBAAsB,GAAG5D,aAAa,IAAInI,KAAK,IAAI;IACvD,IAAIgM,sBAAsB;IAC1B,CAACA,sBAAsB,GAAG7D,aAAa,CAAC8D,YAAY,KAAK,IAAI,IAAID,sBAAsB,CAAC1D,IAAI,CAACH,aAAa,EAAEnI,KAAK,CAAC;IAClH+F,OAAO,CAAC,CAAC,CAAC,CAAC;EACb,CAAC;EACD,MAAMmG,aAAa,GAAG,SAAAA,CAAA,EAAwB;IAAA,IAAvBX,aAAa,GAAAC,SAAA,CAAAjL,MAAA,QAAAiL,SAAA,QAAArL,SAAA,GAAAqL,SAAA,MAAG,CAAC,CAAC;IACvC,MAAMC,gBAAgB,GAAG3M,oBAAoB,CAACyM,aAAa,CAAC;IAC5D,MAAMG,gBAAgB,GAAG;MACvBI,WAAW,EAAEF,qBAAqB,CAACH,gBAAgB,IAAI,CAAC,CAAC,CAAC;MAC1DQ,YAAY,EAAEF,sBAAsB,CAACN,gBAAgB,IAAI,CAAC,CAAC;IAC7D,CAAC;IACD,OAAO5N,QAAQ,CAAC,CAAC,CAAC,EAAE0N,aAAa,EAAEE,gBAAgB,EAAEC,gBAAgB,CAAC;EACxE,CAAC;EACD,MAAMS,aAAa,GAAG5M,KAAK,IAAI;IAC7B,OAAO;MACL;MACA6M,aAAa,EAAExG,MAAM,KAAK,CAAC,CAAC,IAAIA,MAAM,KAAKrG,KAAK,GAAG,MAAM,GAAGY;IAC9D,CAAC;EACH,CAAC;EACD,MAAMkM,mBAAmB,GAAG,SAAAA,CAAA,EAAwB;IAAA,IAAvBd,aAAa,GAAAC,SAAA,CAAAjL,MAAA,QAAAiL,SAAA,QAAArL,SAAA,GAAAqL,SAAA,MAAG,CAAC,CAAC;IAC7C,IAAIc,gBAAgB;IACpB,MAAMb,gBAAgB,GAAG3M,oBAAoB,CAACyM,aAAa,CAAC;IAC5D,MAAMG,gBAAgB,GAAG;MACvBxG,QAAQ,EAAEuE,6BAA6B,CAACgC,gBAAgB,IAAI,CAAC,CAAC,CAAC;MAC/D9D,OAAO,EAAEO,4BAA4B,CAACuD,gBAAgB,IAAI,CAAC,CAAC,CAAC;MAC7DhE,MAAM,EAAEc,2BAA2B,CAACkD,gBAAgB,IAAI,CAAC,CAAC,CAAC;MAC3DpC,SAAS,EAAEL,8BAA8B,CAACyC,gBAAgB,IAAI,CAAC,CAAC;IAClE,CAAC;IACD,MAAME,mBAAmB,GAAG9N,QAAQ,CAAC,CAAC,CAAC,EAAE4N,gBAAgB,EAAEC,gBAAgB,CAAC;IAC5E,OAAO7N,QAAQ,CAAC;MACd4H,QAAQ;MACR,iBAAiB,EAAEf,cAAc;MACjC,kBAAkB,EAAEU,WAAW;MAC/B,eAAe,EAAEG,KAAK,CAACvE,GAAG,CAAC;MAC3B,eAAe,EAAEuE,KAAK,CAACxE,GAAG,CAAC;MAC3BkE,IAAI;MACJ2B,IAAI,EAAE,OAAO;MACb7F,GAAG,EAAE0D,UAAU,CAAC1D,GAAG;MACnBC,GAAG,EAAEyD,UAAU,CAACzD,GAAG;MACnBa,IAAI,EAAE4C,UAAU,CAAC5C,IAAI,KAAK,IAAI,IAAI4C,UAAU,CAACM,KAAK,GAAG,KAAK,GAAG,CAACuH,gBAAgB,GAAG7H,UAAU,CAAC5C,IAAI,KAAK,IAAI,GAAGyK,gBAAgB,GAAGnM,SAAS;MACxIyE;IACF,CAAC,EAAE2G,aAAa,EAAEI,mBAAmB,EAAE;MACrCY,KAAK,EAAE1O,QAAQ,CAAC,CAAC,CAAC,EAAEc,cAAc,EAAE;QAClC6N,SAAS,EAAE1H,KAAK,GAAG,KAAK,GAAG,KAAK;QAChC;QACAhB,KAAK,EAAE,MAAM;QACbI,MAAM,EAAE;MACV,CAAC;IACH,CAAC,CAAC;EACJ,CAAC;EACD,OAAO;IACL0B,MAAM;IACNkE,IAAI,EAAEA,IAAI;IACVrG,SAAS;IACTuC,QAAQ;IACR8B,iBAAiB;IACjBuE,mBAAmB;IACnBf,YAAY;IACZY,aAAa;IACbnH,KAAK,EAAEA,KAAK;IACZe,IAAI;IACJkB,KAAK;IACL3B,OAAO,EAAE4C,SAAS;IAClBoD,SAAS;IACTD,WAAW;IACXhM,MAAM;IACN+M;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}