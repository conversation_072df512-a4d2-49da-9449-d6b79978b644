{"ast": null, "code": "import PropTypes from 'prop-types';\nimport chainPropTypes from '../chainPropTypes';\nfunction isClassComponent(elementType) {\n  // elementType.prototype?.isReactComponent\n  const {\n    prototype = {}\n  } = elementType;\n  return Boolean(prototype.isReactComponent);\n}\nfunction elementTypeAcceptingRef(props, propName, componentName, location, propFullName) {\n  const propValue = props[propName];\n  const safePropName = propFullName || propName;\n  if (propValue == null ||\n  // When server-side rendering React doesn't warn either.\n  // This is not an accurate check for SSR.\n  // This is only in place for emotion compat.\n  // TODO: Revisit once https://github.com/facebook/react/issues/20047 is resolved.\n  typeof window === 'undefined') {\n    return null;\n  }\n  let warningHint;\n\n  /**\n   * Blacklisting instead of whitelisting\n   *\n   * Blacklisting will miss some components, such as React.Fragment. Those will at least\n   * trigger a warning in React.\n   * We can't whitelist because there is no safe way to detect React.forwardRef\n   * or class components. \"Safe\" means there's no public API.\n   *\n   */\n  if (typeof propValue === 'function' && !isClassComponent(propValue)) {\n    warningHint = 'Did you accidentally provide a plain function component instead?';\n  }\n  if (warningHint !== undefined) {\n    return new Error(\"Invalid \".concat(location, \" `\").concat(safePropName, \"` supplied to `\").concat(componentName, \"`. \") + \"Expected an element type that can hold a ref. \".concat(warningHint, \" \") + 'For more information see https://mui.com/r/caveat-with-refs-guide');\n  }\n  return null;\n}\nexport default chainPropTypes(PropTypes.elementType, elementTypeAcceptingRef);", "map": {"version": 3, "names": ["PropTypes", "chainPropTypes", "isClassComponent", "elementType", "prototype", "Boolean", "isReactComponent", "elementTypeAcceptingRef", "props", "propName", "componentName", "location", "prop<PERSON><PERSON><PERSON><PERSON>", "propValue", "safePropName", "window", "warningHint", "undefined", "Error", "concat"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/node_modules/@mui/lab/node_modules/@mui/utils/esm/elementTypeAcceptingRef/elementTypeAcceptingRef.js"], "sourcesContent": ["import PropTypes from 'prop-types';\nimport chainPropTypes from '../chainPropTypes';\nfunction isClassComponent(elementType) {\n  // elementType.prototype?.isReactComponent\n  const {\n    prototype = {}\n  } = elementType;\n  return Boolean(prototype.isReactComponent);\n}\nfunction elementTypeAcceptingRef(props, propName, componentName, location, propFullName) {\n  const propValue = props[propName];\n  const safePropName = propFullName || propName;\n  if (propValue == null ||\n  // When server-side rendering React doesn't warn either.\n  // This is not an accurate check for SSR.\n  // This is only in place for emotion compat.\n  // TODO: Revisit once https://github.com/facebook/react/issues/20047 is resolved.\n  typeof window === 'undefined') {\n    return null;\n  }\n  let warningHint;\n\n  /**\n   * Blacklisting instead of whitelisting\n   *\n   * Blacklisting will miss some components, such as React.Fragment. Those will at least\n   * trigger a warning in React.\n   * We can't whitelist because there is no safe way to detect React.forwardRef\n   * or class components. \"Safe\" means there's no public API.\n   *\n   */\n  if (typeof propValue === 'function' && !isClassComponent(propValue)) {\n    warningHint = 'Did you accidentally provide a plain function component instead?';\n  }\n  if (warningHint !== undefined) {\n    return new Error(`Invalid ${location} \\`${safePropName}\\` supplied to \\`${componentName}\\`. ` + `Expected an element type that can hold a ref. ${warningHint} ` + 'For more information see https://mui.com/r/caveat-with-refs-guide');\n  }\n  return null;\n}\nexport default chainPropTypes(PropTypes.elementType, elementTypeAcceptingRef);"], "mappings": "AAAA,OAAOA,SAAS,MAAM,YAAY;AAClC,OAAOC,cAAc,MAAM,mBAAmB;AAC9C,SAASC,gBAAgBA,CAACC,WAAW,EAAE;EACrC;EACA,MAAM;IACJC,SAAS,GAAG,CAAC;EACf,CAAC,GAAGD,WAAW;EACf,OAAOE,OAAO,CAACD,SAAS,CAACE,gBAAgB,CAAC;AAC5C;AACA,SAASC,uBAAuBA,CAACC,KAAK,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,QAAQ,EAAEC,YAAY,EAAE;EACvF,MAAMC,SAAS,GAAGL,KAAK,CAACC,QAAQ,CAAC;EACjC,MAAMK,YAAY,GAAGF,YAAY,IAAIH,QAAQ;EAC7C,IAAII,SAAS,IAAI,IAAI;EACrB;EACA;EACA;EACA;EACA,OAAOE,MAAM,KAAK,WAAW,EAAE;IAC7B,OAAO,IAAI;EACb;EACA,IAAIC,WAAW;;EAEf;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,IAAI,OAAOH,SAAS,KAAK,UAAU,IAAI,CAACX,gBAAgB,CAACW,SAAS,CAAC,EAAE;IACnEG,WAAW,GAAG,kEAAkE;EAClF;EACA,IAAIA,WAAW,KAAKC,SAAS,EAAE;IAC7B,OAAO,IAAIC,KAAK,CAAC,WAAAC,MAAA,CAAWR,QAAQ,QAAAQ,MAAA,CAAML,YAAY,qBAAAK,MAAA,CAAoBT,aAAa,4DAAAS,MAAA,CAA0DH,WAAW,MAAG,GAAG,mEAAmE,CAAC;EACxO;EACA,OAAO,IAAI;AACb;AACA,eAAef,cAAc,CAACD,SAAS,CAACG,WAAW,EAAEI,uBAAuB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}